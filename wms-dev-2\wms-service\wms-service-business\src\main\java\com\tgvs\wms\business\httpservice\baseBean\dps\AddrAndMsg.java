package com.tgvs.wms.business.httpservice.baseBean.dps;


import com.alibaba.fastjson.annotation.JSO<PERSON>ield;

public class AddrAndMsg {
    @J<PERSON>NField(name = "Address")
    private String Address;

    @J<PERSON>NField(name = "Message")
    private String Message;

    public void setAddress(String Address) {
        this.Address = Address;
    }

    public void setMessage(String Message) {
        this.Message = Message;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof AddrAndMsg))
            return false;
        AddrAndMsg other = (AddrAndMsg)o;
        if (!other.canEqual(this))
            return false;
        Object this$Address = getAddress(), other$Address = other.getAddress();
        if ((this$Address == null) ? (other$Address != null) : !this$Address.equals(other$Address))
            return false;
        Object this$Message = getMessage(), other$Message = other.getMessage();
        return !((this$Message == null) ? (other$Message != null) : !this$Message.equals(other$Message));
    }

    protected boolean canEqual(Object other) {
        return other instanceof AddrAndMsg;
    }

    public int hashCode() {
        int PRIME = 59, result = 1;
        Object $Address = getAddress();
        result = result * 59 + (($Address == null) ? 43 : $Address.hashCode());
        Object $Message = getMessage();
        return result * 59 + (($Message == null) ? 43 : $Message.hashCode());
    }

    public String toString() {
        return "AddrAndMsg(Address=" + getAddress() + ", Message=" + getMessage() + ")";
    }

    public String getAddress() {
        return this.Address;
    }

    public String getMessage() {
        return this.Message;
    }
}

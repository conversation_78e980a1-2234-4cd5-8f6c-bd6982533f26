package com.tgvs.wms.business.httpservice.baseBean.agv;

import com.alibaba.fastjson.annotation.J<PERSON>NField;

public class Stockup {
    @J<PERSON><PERSON>ield(name = "TaskID")
    private String TaskID;

    @J<PERSON><PERSON>ield(name = "SiteNo")
    private String SiteNo;

    @J<PERSON><PERSON>ield(name = "<PERSON>No")
    private String BillNo;

    @J<PERSON><PERSON>ield(name = "DollyNo")
    private String DollyNo;

    @J<PERSON>NField(name = "Content")
    private String Content;

    public void setTaskID(String TaskID) {
        this.TaskID = TaskID;
    }

    public void setSiteNo(String SiteNo) {
        this.SiteNo = SiteNo;
    }

    public void setBillNo(String BillNo) {
        this.BillNo = BillNo;
    }

    public void setDollyNo(String DollyNo) {
        this.DollyNo = DollyNo;
    }

    public void setContent(String Content) {
        this.Content = Content;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof Stockup))
            return false;
        Stockup other = (Stockup)o;
        if (!other.canEqual(this))
            return false;
        Object this$TaskID = getTaskID(), other$TaskID = other.getTaskID();
        if ((this$TaskID == null) ? (other$TaskID != null) : !this$TaskID.equals(other$TaskID))
            return false;
        Object this$SiteNo = getSiteNo(), other$SiteNo = other.getSiteNo();
        if ((this$SiteNo == null) ? (other$SiteNo != null) : !this$SiteNo.equals(other$SiteNo))
            return false;
        Object this$BillNo = getBillNo(), other$BillNo = other.getBillNo();
        if ((this$BillNo == null) ? (other$BillNo != null) : !this$BillNo.equals(other$BillNo))
            return false;
        Object this$DollyNo = getDollyNo(), other$DollyNo = other.getDollyNo();
        if ((this$DollyNo == null) ? (other$DollyNo != null) : !this$DollyNo.equals(other$DollyNo))
            return false;
        Object this$Content = getContent(), other$Content = other.getContent();
        return !((this$Content == null) ? (other$Content != null) : !this$Content.equals(other$Content));
    }

    protected boolean canEqual(Object other) {
        return other instanceof Stockup;
    }

    public int hashCode() {
        int PRIME = 59,result = 1;
        Object $TaskID = getTaskID();
        result = result * 59 + (($TaskID == null) ? 43 : $TaskID.hashCode());
        Object $SiteNo = getSiteNo();
        result = result * 59 + (($SiteNo == null) ? 43 : $SiteNo.hashCode());
        Object $BillNo = getBillNo();
        result = result * 59 + (($BillNo == null) ? 43 : $BillNo.hashCode());
        Object $DollyNo = getDollyNo();
        result = result * 59 + (($DollyNo == null) ? 43 : $DollyNo.hashCode());
        Object $Content = getContent();
        return result * 59 + (($Content == null) ? 43 : $Content.hashCode());
    }

    public String toString() {
        return "Stockup(TaskID=" + getTaskID() + ", SiteNo=" + getSiteNo() + ", BillNo=" + getBillNo() + ", DollyNo=" + getDollyNo() + ", Content=" + getContent() + ")";
    }

    public String getTaskID() {
        return this.TaskID;
    }

    public String getSiteNo() {
        return this.SiteNo;
    }

    public String getBillNo() {
        return this.BillNo;
    }

    public String getDollyNo() {
        return this.DollyNo;
    }

    public String getContent() {
        return this.Content;
    }
}

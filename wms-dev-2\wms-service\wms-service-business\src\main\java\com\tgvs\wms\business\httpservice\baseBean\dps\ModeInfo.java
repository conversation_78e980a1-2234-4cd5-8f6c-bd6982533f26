package com.tgvs.wms.business.httpservice.baseBean.dps;


import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonInclude;

public class ModeInfo {
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @JSONField(name = "ConfirmColor")
    private int ConfirmColor;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @JSONField(name = "ConfirmStyle")
    private int ConfirmStyle;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @JSONField(name = "DisplayStyle")
    private int DisplayStyle;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @JSONField(name = "LightModuleBuzzer")
    private int LightModuleBuzzer;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @JSONField(name = "LightModuleBuzzerTime")
    private int LightModuleBuzzerTime;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @JSONField(name = "ConfirmStatus")
    private int ConfirmStatus;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @JSONField(name = "FnStatus")
    private int FnStatus;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @JSONField(name = "FnDecrementStatus")
    private int FnDecrementStatus;

    public void setConfirmColor(int ConfirmColor) {
        this.ConfirmColor = ConfirmColor;
    }

    public void setConfirmStyle(int ConfirmStyle) {
        this.ConfirmStyle = ConfirmStyle;
    }

    public void setDisplayStyle(int DisplayStyle) {
        this.DisplayStyle = DisplayStyle;
    }

    public void setLightModuleBuzzer(int LightModuleBuzzer) {
        this.LightModuleBuzzer = LightModuleBuzzer;
    }

    public void setLightModuleBuzzerTime(int LightModuleBuzzerTime) {
        this.LightModuleBuzzerTime = LightModuleBuzzerTime;
    }

    public void setConfirmStatus(int ConfirmStatus) {
        this.ConfirmStatus = ConfirmStatus;
    }

    public void setFnStatus(int FnStatus) {
        this.FnStatus = FnStatus;
    }

    public void setFnDecrementStatus(int FnDecrementStatus) {
        this.FnDecrementStatus = FnDecrementStatus;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof ModeInfo))
            return false;
        ModeInfo other = (ModeInfo)o;
        return !other.canEqual(this) ? false : ((getConfirmColor() != other.getConfirmColor()) ? false : ((getConfirmStyle() != other.getConfirmStyle()) ? false : ((getDisplayStyle() != other.getDisplayStyle()) ? false : ((getLightModuleBuzzer() != other.getLightModuleBuzzer()) ? false : ((getLightModuleBuzzerTime() != other.getLightModuleBuzzerTime()) ? false : ((getConfirmStatus() != other.getConfirmStatus()) ? false : ((getFnStatus() != other.getFnStatus()) ? false : (!(getFnDecrementStatus() != other.getFnDecrementStatus())))))))));
    }

    protected boolean canEqual(Object other) {
        return other instanceof ModeInfo;
    }

    public int hashCode() {
        int PRIME = 59,
        result = 1;
        result = result * 59 + getConfirmColor();
        result = result * 59 + getConfirmStyle();
        result = result * 59 + getDisplayStyle();
        result = result * 59 + getLightModuleBuzzer();
        result = result * 59 + getLightModuleBuzzerTime();
        result = result * 59 + getConfirmStatus();
        result = result * 59 + getFnStatus();
        return result * 59 + getFnDecrementStatus();
    }

    public String toString() {
        return "ModeInfo(ConfirmColor=" + getConfirmColor() + ", ConfirmStyle=" + getConfirmStyle() + ", DisplayStyle=" + getDisplayStyle() + ", LightModuleBuzzer=" + getLightModuleBuzzer() + ", LightModuleBuzzerTime=" + getLightModuleBuzzerTime() + ", ConfirmStatus=" + getConfirmStatus() + ", FnStatus=" + getFnStatus() + ", FnDecrementStatus=" + getFnDecrementStatus() + ")";
    }

    public int getConfirmColor() {
        return this.ConfirmColor;
    }

    public int getConfirmStyle() {
        return this.ConfirmStyle;
    }

    public int getDisplayStyle() {
        return this.DisplayStyle;
    }

    public int getLightModuleBuzzer() {
        return this.LightModuleBuzzer;
    }

    public int getLightModuleBuzzerTime() {
        return this.LightModuleBuzzerTime;
    }

    public int getConfirmStatus() {
        return this.ConfirmStatus;
    }

    public int getFnStatus() {
        return this.FnStatus;
    }

    public int getFnDecrementStatus() {
        return this.FnDecrementStatus;
    }

    public ModeInfo() {
        setConfirmColor(2);
        setConfirmStyle(2);
        setDisplayStyle(2);
        setLightModuleBuzzer(1);
        setLightModuleBuzzerTime(0);
    }

    public ModeInfo(int i) {
        setConfirmColor(0);
        setConfirmStyle(1);
        setDisplayStyle(1);
    }
}

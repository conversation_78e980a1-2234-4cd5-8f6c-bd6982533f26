package com.tgvs.wms.business.httpservice.baseBean.dps;

import java.util.List;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonInclude;

public class SingalBean {
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @JSONField(name = "Addresses")
    private List<String> Addresses;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @JSONField(name = "SingalType")
    private int SingalType;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @JSONField(name = "SingalColor")
    private int SingalColor;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @JSONField(name = "SingalStyle")
    private int SingalStyle;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @JSONField(name = "SingalBuzzer")
    private int SingalBuzzer;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @JSONField(name = "SingalBuzzerTime")
    private int SingalBuzzerTime;

    public void setAddresses(List<String> Addresses) {
        this.Addresses = Addresses;
    }

    public void setSingalType(int SingalType) {
        this.SingalType = SingalType;
    }

    public void setSingalColor(int SingalColor) {
        this.SingalColor = SingalColor;
    }

    public void setSingalStyle(int SingalStyle) {
        this.SingalStyle = SingalStyle;
    }

    public void setSingalBuzzer(int SingalBuzzer) {
        this.SingalBuzzer = SingalBuzzer;
    }

    public void setSingalBuzzerTime(int SingalBuzzerTime) {
        this.SingalBuzzerTime = SingalBuzzerTime;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof SingalBean))
            return false;
        SingalBean other = (SingalBean)o;
        if (!other.canEqual(this))
            return false;
        List<String> this$Addresses = (List<String>)getAddresses(), other$Addresses = (List<String>)other.getAddresses();
        return ((this$Addresses == null) ? (other$Addresses != null) : !this$Addresses.equals(other$Addresses)) ? false : ((getSingalType() != other.getSingalType()) ? false : ((getSingalColor() != other.getSingalColor()) ? false : ((getSingalStyle() != other.getSingalStyle()) ? false : ((getSingalBuzzer() != other.getSingalBuzzer()) ? false : (!(getSingalBuzzerTime() != other.getSingalBuzzerTime()))))));
    }

    protected boolean canEqual(Object other) {
        return other instanceof SingalBean;
    }

    public int hashCode() {
        int PRIME = 59,
        result = 1;
        List<String> $Addresses = (List<String>)getAddresses();
        result = result * 59 + (($Addresses == null) ? 43 : $Addresses.hashCode());
        result = result * 59 + getSingalType();
        result = result * 59 + getSingalColor();
        result = result * 59 + getSingalStyle();
        result = result * 59 + getSingalBuzzer();
        return result * 59 + getSingalBuzzerTime();
    }

    public String toString() {
        return "SingalBean(Addresses=" + getAddresses() + ", SingalType=" + getSingalType() + ", SingalColor=" + getSingalColor() + ", SingalStyle=" + getSingalStyle() + ", SingalBuzzer=" + getSingalBuzzer() + ", SingalBuzzerTime=" + getSingalBuzzerTime() + ")";
    }

    public List<String> getAddresses() {
        return this.Addresses;
    }

    public int getSingalType() {
        return this.SingalType;
    }

    public int getSingalColor() {
        return this.SingalColor;
    }

    public int getSingalStyle() {
        return this.SingalStyle;
    }

    public int getSingalBuzzer() {
        return this.SingalBuzzer;
    }

    public int getSingalBuzzerTime() {
        return this.SingalBuzzerTime;
    }
}

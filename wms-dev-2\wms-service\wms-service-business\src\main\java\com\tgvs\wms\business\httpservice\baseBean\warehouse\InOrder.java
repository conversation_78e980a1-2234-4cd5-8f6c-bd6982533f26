package com.tgvs.wms.business.httpservice.baseBean.warehouse;


import com.fasterxml.jackson.annotation.JsonProperty;

public class InOrder {
    private Integer id;

    public void setId(Integer id) {
        this.id = id;
    }

    public void setMessageName(String messageName) {
        this.messageName = messageName;
    }

    public void setBoxId(String boxId) {
        this.boxId = boxId;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public void setLocation(Integer location) {
        this.location = location;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public void setWeight(Integer weight) {
        this.weight = weight;
    }

    public void setWmsId(String wmsId) {
        this.wmsId = wmsId;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof InOrder))
            return false;
        InOrder other = (InOrder)o;
        if (!other.canEqual(this))
            return false;
        Object this$id = getId(), other$id = other.getId();
        if ((this$id == null) ? (other$id != null) : !this$id.equals(other$id))
            return false;
        Object this$messageName = getMessageName(), other$messageName = other.getMessageName();
        if ((this$messageName == null) ? (other$messageName != null) : !this$messageName.equals(other$messageName))
            return false;
        Object this$boxId = getBoxId(), other$boxId = other.getBoxId();
        if ((this$boxId == null) ? (other$boxId != null) : !this$boxId.equals(other$boxId))
            return false;
        Object this$level = getLevel(), other$level = other.getLevel();
        if ((this$level == null) ? (other$level != null) : !this$level.equals(other$level))
            return false;
        Object this$location = getLocation(), other$location = other.getLocation();
        if ((this$location == null) ? (other$location != null) : !this$location.equals(other$location))
            return false;
        Object this$type = getType(), other$type = other.getType();
        if ((this$type == null) ? (other$type != null) : !this$type.equals(other$type))
            return false;
        Object this$weight = getWeight(), other$weight = other.getWeight();
        if ((this$weight == null) ? (other$weight != null) : !this$weight.equals(other$weight))
            return false;
        Object this$wmsId = getWmsId(), other$wmsId = other.getWmsId();
        return !((this$wmsId == null) ? (other$wmsId != null) : !this$wmsId.equals(other$wmsId));
    }

    protected boolean canEqual(Object other) {
        return other instanceof InOrder;
    }

    public int hashCode() {
        int PRIME = 59,
        result = 1;
        Object $id = getId();
        result = result * 59 + (($id == null) ? 43 : $id.hashCode());
        Object $messageName = getMessageName();
        result = result * 59 + (($messageName == null) ? 43 : $messageName.hashCode());
        Object $boxId = getBoxId();
        result = result * 59 + (($boxId == null) ? 43 : $boxId.hashCode());
        Object $level = getLevel();
        result = result * 59 + (($level == null) ? 43 : $level.hashCode());
        Object $location = getLocation();
        result = result * 59 + (($location == null) ? 43 : $location.hashCode());
        Object $type = getType();
        result = result * 59 + (($type == null) ? 43 : $type.hashCode());
        Object $weight = getWeight();
        result = result * 59 + (($weight == null) ? 43 : $weight.hashCode());
        Object $wmsId = getWmsId();
        return result * 59 + (($wmsId == null) ? 43 : $wmsId.hashCode());
    }

    public String toString() {
        return "InOrder(id=" + getId() + ", messageName=" + getMessageName() + ", boxId=" + getBoxId() + ", level=" + getLevel() + ", location=" + getLocation() + ", type=" + getType() + ", weight=" + getWeight() + ", wmsId=" + getWmsId() + ")";
    }

    public Integer getId() {
        return this.id;
    }

    @JsonProperty("messageName")
    private String messageName = "appointBoxAnnounce";

    @JsonProperty("boxId")
    private String boxId;

    private Integer level;

    private Integer location;

    private Integer type;

    public String getMessageName() {
        return this.messageName;
    }

    public String getBoxId() {
        return this.boxId;
    }

    public Integer getLevel() {
        return this.level;
    }

    public Integer getLocation() {
        return this.location;
    }

    public Integer getType() {
        return this.type;
    }

    private Integer weight = Integer.valueOf(20);

    @JsonProperty("wmsId")
    private String wmsId;

    public Integer getWeight() {
        return this.weight;
    }

    public String getWmsId() {
        return this.wmsId;
    }
}

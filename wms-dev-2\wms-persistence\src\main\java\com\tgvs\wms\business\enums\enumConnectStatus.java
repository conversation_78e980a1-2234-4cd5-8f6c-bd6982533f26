package com.tgvs.wms.business.enums;

import com.tgvs.wms.common.util.oConvertUtils;

public enum enumConnectStatus {
    unconnected(Integer.valueOf(0), "unconnected", "未连接"),
    connected(Integer.valueOf(1), "connected", "已连接"),
    connecting(Integer.valueOf(2), "connecting", "连接中"),
    disconnected(Integer.valueOf(3), "disconnected", "已断开");

    private Integer value;

    private String code;

    private String text;

    enumConnectStatus(Integer value, String code, String text) {
        this.value = value;
        this.code = code;
        this.text = text;
    }

    public static enumConnectStatus getByValue(int value) {
        if (oConvertUtils.isEmpty(Integer.valueOf(value)))
            return null;
        for (enumConnectStatus val : values()) {
            if (val.getValue().equals(Integer.valueOf(value)))
                return val;
        }
        return null;
    }

    public Integer getValue() {
        return this.value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getText() {
        return this.text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public static enumConnectStatus toEnum(Integer Value) {
        for (enumConnectStatus e : values()) {
            if (e.getValue().equals(Value))
                return e;
        }
        return null;
    }
}

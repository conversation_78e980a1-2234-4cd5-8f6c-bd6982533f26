package com.tgvs.wms.business.modules.bd.entity;

import java.io.Serializable;
import java.util.Date;

import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TableName("wms_base_line_config")
@ApiModel(value = "wms_base_line_config对象", description = "入库参考线设置")
@Data
public class BaseLineConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("id")
    private String id;

    @Excel(name = "设定值", width = 15.0D)
    @ApiModelProperty("设定值")
    private Integer value;

    @Excel(name = "描述", width = 15.0D)
    @ApiModelProperty("描述")
    private String text;

    @Excel(name = "最大值", width = 15.0D)
    @ApiModelProperty("最大值")
    private Integer maxValue;

    @Excel(name = "最小值", width = 15.0D)
    @ApiModelProperty("最小值")
    private Integer minValue;

    @ApiModelProperty("创建人")
    private String createBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("更新时间")
    private Date updateTime;

    @Excel(name = "删除状态", width = 15.0D)
    @ApiModelProperty("删除状态")
    private Integer delFlag;
}

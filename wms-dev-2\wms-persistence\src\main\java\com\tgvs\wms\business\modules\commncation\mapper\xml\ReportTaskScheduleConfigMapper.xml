<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tgvs.wms.business.modules.commncation.mapper.ReportTaskScheduleConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tgvs.wms.business.modules.commncation.entity.ReportTaskScheduleConfig">
        <id column="id" property="id" />
        <result column="config_name" property="configName" />
        <result column="task_type" property="taskType" />
        <result column="is_enabled" property="isEnabled" />
        <result column="interval_minutes" property="intervalMinutes" />
        <result column="batch_size" property="batchSize" />
        <result column="retry_count" property="retryCount" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="last_execute_time" property="lastExecuteTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="version" property="version" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, config_name, task_type, is_enabled, interval_minutes, batch_size, retry_count,
        create_time, update_time, last_execute_time, create_by, update_by, version, delete_flag, remark
    </sql>

    <!-- 查询所有启用的配置 -->
    <select id="selectEnabledConfigs" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM wms_report_task_schedule_config
        WHERE delete_flag = 0 
          AND is_enabled = 1
        ORDER BY task_type ASC, create_time ASC
    </select>

    <!-- 根据任务类型查询配置 -->
    <select id="selectByTaskType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM wms_report_task_schedule_config
        WHERE delete_flag = 0 
          AND task_type = #{taskType}
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <!-- 根据任务类型查询启用的配置 -->
    <select id="selectEnabledByTaskType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM wms_report_task_schedule_config
        WHERE delete_flag = 0 
          AND is_enabled = 1
          AND task_type = #{taskType}
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <!-- 批量更新启用状态 -->
    <update id="batchUpdateEnabledStatus">
        UPDATE wms_report_task_schedule_config
        SET is_enabled = #{isEnabled},
            update_time = NOW(),
            version = version + 1
        WHERE delete_flag = 0
          AND id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 根据执行间隔查询配置 -->
    <select id="selectByInterval" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM wms_report_task_schedule_config
        WHERE delete_flag = 0 
          AND is_enabled = 1
          AND interval_minutes = #{intervalMinutes}
        ORDER BY task_type ASC
    </select>

    <!-- 查询需要执行的配置（根据时间间隔） -->
    <select id="selectConfigsToExecute" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM wms_report_task_schedule_config
        WHERE delete_flag = 0 
          AND is_enabled = 1
          AND (
              -- 从未执行过的配置
              last_execute_time IS NULL
              OR 
              -- 距离上次执行时间超过间隔的配置
              (#{currentTime} - last_execute_time) >= (interval_minutes * 60 * 1000)
          )
        ORDER BY task_type ASC, create_time ASC
    </select>

    <!-- 更新配置的最后执行时间 -->
    <update id="updateLastExecuteTime">
        UPDATE wms_report_task_schedule_config
        SET last_execute_time = #{lastExecuteTime},
            update_time = NOW(),
            version = version + 1
        WHERE delete_flag = 0
          AND id = #{id}
    </update>

    <!-- 统计各任务类型的配置数量 -->
    <select id="countByTaskType" resultType="java.util.Map">
        SELECT 
            task_type AS taskType,
            COUNT(*) AS totalCount,
            SUM(CASE WHEN is_enabled = 1 THEN 1 ELSE 0 END) AS enabledCount,
            SUM(CASE WHEN is_enabled = 0 THEN 1 ELSE 0 END) AS disabledCount
        FROM wms_report_task_schedule_config
        WHERE delete_flag = 0
        GROUP BY task_type
        ORDER BY task_type ASC
    </select>

    <!-- 查询配置详情（包含统计信息） -->
    <select id="selectConfigDetail" resultType="java.util.Map">
        SELECT 
            c.*,
            -- 计算下次执行时间
            CASE 
                WHEN c.last_execute_time IS NULL THEN '立即执行'
                WHEN (UNIX_TIMESTAMP() * 1000 - c.last_execute_time) >= (c.interval_minutes * 60 * 1000) THEN '立即执行'
                ELSE CONCAT(
                    ROUND((c.interval_minutes * 60 * 1000 - (UNIX_TIMESTAMP() * 1000 - c.last_execute_time)) / 60000, 1),
                    '分钟后'
                )
            END AS nextExecuteTime,
            -- 计算执行状态
            CASE 
                WHEN c.is_enabled = 0 THEN '已禁用'
                WHEN c.last_execute_time IS NULL THEN '未执行'
                WHEN (UNIX_TIMESTAMP() * 1000 - c.last_execute_time) >= (c.interval_minutes * 60 * 1000) THEN '待执行'
                ELSE '等待中'
            END AS executeStatus,
            -- 格式化最后执行时间
            CASE 
                WHEN c.last_execute_time IS NULL THEN '从未执行'
                ELSE FROM_UNIXTIME(c.last_execute_time / 1000, '%Y-%m-%d %H:%i:%s')
            END AS lastExecuteTimeFormatted
        FROM wms_report_task_schedule_config c
        WHERE c.delete_flag = 0
          AND c.id = #{id}
    </select>

    <!-- 软删除配置 -->
    <update id="softDeleteByIds">
        UPDATE wms_report_task_schedule_config
        SET delete_flag = 1,
            update_time = NOW(),
            version = version + 1
        WHERE delete_flag = 0
          AND id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 恢复软删除的配置 -->
    <update id="restoreByIds">
        UPDATE wms_report_task_schedule_config
        SET delete_flag = 0,
            update_time = NOW(),
            version = version + 1
        WHERE delete_flag = 1
          AND id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

</mapper> 
package com.tgvs.wms.business.modules.machineAuxiliary.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 辅料入库历史查询DTO
 */
@Data
public class AuxiliaryInBoundHistoryDto implements Serializable {
    private static final long serialVersionUID = 1L;

    // WmsAuxiliaryInBound(父类)的字段
    @ApiModelProperty("入库单号")
    private String inStoreNumber;
    
    @ApiModelProperty("任务类型")
    private Integer taskType;
    
    @ApiModelProperty("状态")
    private Integer status;
    
    @ApiModelProperty("备注")
    private String remarks;
    
    @ApiModelProperty("创建人")
    private String createBy;
    
    @ApiModelProperty("创建时间")
    private Date createTime;
    
    @ApiModelProperty("更新人")
    private String updateBy;
    
    @ApiModelProperty("更新时间")
    private Date updateTime;
    
    @ApiModelProperty("删除标志")
    private Integer deleteFlag;

    // 明细表字段
    @ApiModelProperty("明细ID")
    private String id;
    
    @ApiModelProperty("操作类型(0入库,1出库)")
    private Integer operationType;

    @ApiModelProperty("容器号")
    private  String boxNo;
    
    @ApiModelProperty("合约号")
    private String contractNo;
    
    @ApiModelProperty("款号")
    private String itemNo;
    
    @ApiModelProperty("物料唯一值")
    private String reqListId;
    
    @ApiModelProperty("物料编码")
    private String materialCode;
    
    @ApiModelProperty("物料名称")
    private String materialName;
    
    @ApiModelProperty("物料颜色")
    private String materialColor;
    
    @ApiModelProperty("物料颜色编码")
    private String materialColorCode;
    
    @ApiModelProperty("物料规格/型号")
    private String materialModel;
    
    @ApiModelProperty("入库单数量")
    private BigDecimal quantity;
    
    @ApiModelProperty("单位")
    private String materialUnit;
    
    @ApiModelProperty("优先级")
    private Integer priority;

    // 物料基本信息表字段
    @ApiModelProperty("物料类型")
    private Integer materialType = -1;
    
    @ApiModelProperty("是否入智能仓")
    private Integer isStore;

    // 预装箱表字段
    @ApiModelProperty("实际入库数量")
    private BigDecimal actualInboundQty;


    @ApiModelProperty("入库开始时间(任务创建时间)")
    private Date taskStartTime;
    
    @ApiModelProperty("入库结束时间(任务更新时间)")
    private Date taskEndTime;
    
    @ApiModelProperty("任务号")
    private String taskOrder;
    
    @ApiModelProperty("容器类型")
    private Integer containerType;
}

package com.tgvs.wms.business.modules.order.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.tgvs.wms.base.entity.BaseEntity;
import com.tgvs.wms.common.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("mes_box")
@ApiModel(value = "mes_box对象", description = "料箱")
public class MesBox extends BaseEntity {

    @ApiModelProperty("箱号")
    private String boxNo;

    @ApiModelProperty("源位置编号")
    private String depSiteNo;

    @ApiModelProperty("到达目的位置编号")
    private String destSiteNo;

    @Dict(dicCode = "box_empty_status")
    @ApiModelProperty("空实状态 0小空箱，1大空箱，2实箱")
    private String isEmpty;

    @Dict(dicCode = "box_type")
    @ApiModelProperty("物料类型，1花片、2十字架、3辅料、10成品 空箱时 0")
    private String boxType;

    @ApiModelProperty("订单编号，多编号用|分隔")
    private String bookNo;

    @Dict(dicCode = "box_height")
    @ApiModelProperty("料箱高度  1标准，2高箱，3超高箱")
    private Integer boxHeight;

    @Dict(dicCode = "box_volume")
    @ApiModelProperty("容量，0空，1 四分之一，2二分之一，3四分之三，4满箱")
    private Integer boxVolume;

    @ApiModelProperty("删除状态")
    private Integer delFlag;
}

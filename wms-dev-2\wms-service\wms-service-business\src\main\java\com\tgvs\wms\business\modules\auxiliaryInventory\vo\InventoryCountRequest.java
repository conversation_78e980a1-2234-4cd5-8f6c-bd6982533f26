package com.tgvs.wms.business.modules.auxiliaryInventory.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 盘点结果提交请求
 */
@Data
public class InventoryCountRequest {
    
    /**
     * 盘点明细ID
     */
    private String inventoryDetailId;
    
    /**
     * 实盘数量
     */
    private BigDecimal actualQty;
    
    /**
     * 盘点备注
     */
    private String countRemark;
    
    /**
     * AGV任务ID
     */
    private String agvTaskId;
}
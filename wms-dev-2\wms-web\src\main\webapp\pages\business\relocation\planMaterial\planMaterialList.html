<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>生产计划物料管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../../../../lib/layui-v2.11.0/css/layui.css" media="all">
    <link rel="stylesheet" href="../../../../css/public.css" media="all">
    <link rel="stylesheet" href="../../../../css/font-awesome.min.css">
    <script src="../../../../js/base-config.js" charset="utf-8"></script>
    <script src="../../../../js/jquery.min.js" charset="utf-8"></script>
    <script src="../../../../js/validate/jquery.validate.min.js" charset="utf-8"></script>
</head>
<body>
<div class="layuimini-container">
    <div class="layuimini-main">
        
        <!-- 搜索条件 -->
        <fieldset class="table-search-fieldset">
            <legend>搜索信息</legend>
            <div style="margin: 10px">
                <form class="layui-form" action="">
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">计划编号：</label>
                            <div class="layui-input-inline">
                                <input type="text" name="planNo" placeholder="请输入计划编号" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">物料编码：</label>
                            <div class="layui-input-inline">
                                <input type="text" name="materialCode" placeholder="请输入物料编码" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">物料名称：</label>
                            <div class="layui-input-inline">
                                <input type="text" name="materialName" placeholder="请输入物料名称" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">状态：</label>
                            <div class="layui-input-inline">
                                <select name="status" lay-search="">
                                    <option value="">请选择状态</option>
                                    <option value="0">待处理</option>
                                    <option value="1">已处理</option>
                                    <option value="2">移库中</option>
                                    <option value="3">移库完成</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <button type="submit" class="layui-btn layui-btn-primary layui-border-green" lay-submit lay-filter="data-search-btn">
                                <i class="layui-icon layui-icon-search"></i> 搜索
                            </button>
                            <button type="reset" class="layui-btn layui-btn-primary" lay-reset>
                                <i class="layui-icon layui-icon-refresh-1"></i> 重置
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </fieldset>

        <!-- 操作按钮 -->
        <script type="text/html" id="toolbarDemo">
            <div class="layui-btn-container">
                <button class="layui-btn layui-btn-normal layui-btn-sm" lay-event="add">
                    <i class="layui-icon layui-icon-add-1"></i> 新增计划
                </button>
                <button class="layui-btn layui-btn-warm layui-btn-sm" lay-event="analyzeHot">
                    <i class="layui-icon layui-icon-fire"></i> 分析热门物料
                </button>
                <button class="layui-btn layui-btn-sm layui-btn-primary layui-border-green" lay-event="relocation">
                    <i class="layui-icon layui-icon-transfer"></i> 移库操作
                </button>
                <button class="layui-btn layui-btn-danger layui-btn-sm" lay-event="delete">
                    <i class="layui-icon layui-icon-delete"></i> 批量删除
                </button>
                <button class="layui-btn layui-btn-sm layui-btn-primary" lay-event="refresh">
                    <i class="layui-icon layui-icon-refresh-1"></i> 刷新
                </button>
            </div>
        </script>

        <!-- 数据表格 -->
        <table class="layui-hide" id="currentTableId" lay-filter="currentTableFilter"></table>

        <!-- 表格行工具栏 -->
        <script type="text/html" id="currentTableBar">
            <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="edit">编辑</a>
            <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="delete">删除</a>
        </script>

        <!-- 状态模板 -->
        <script type="text/html" id="statusTpl">
            {{# if(d.status == 0){ }}
                <span class="layui-badge layui-bg-gray">待处理</span>
            {{# } else if(d.status == 1){ }}
                <span class="layui-badge layui-bg-blue">已处理</span>
            {{# } else if(d.status == 2){ }}
                <span class="layui-badge layui-bg-orange">移库中</span>
            {{# } else if(d.status == 3){ }}
                <span class="layui-badge layui-bg-green">移库完成</span>
            {{# } }}
        </script>

        <!-- 热门物料模板 -->
        <script type="text/html" id="isHotTpl">
            {{# if(d.isHot == 1){ }}
                <span class="layui-badge layui-bg-red">热门</span>
            {{# } else { }}
                <span class="layui-badge layui-bg-gray">普通</span>
            {{# } }}
        </script>

    </div>
</div>

<script src="../../../../lib/layui-v2.11.0/layui.js" charset="utf-8"></script>
<script src="../../../../js/common-util.js" charset="utf-8"></script>
<script>
    layui.use(['form', 'table', 'layer'], function () {
        var $ = layui.jquery,
            form = layui.form,
            table = layui.table,
            layer = layui.layer;

        // 数据表格
        table.render({
            elem: '#currentTableId',
            url: '/api/production/plan/materials',
            toolbar: '#toolbarDemo',
            defaultToolbar: ['filter', 'exports', 'print'],
            parseData: function(result) {
                return {
                    "code": (result.code === 0 ? '0' : result.code),
                    "data": result.data.records || result.data,
                    "count": result.data.total || 0
                }
            },
            cols: [[
                {type: "checkbox", width: 50},
                {field: 'planNo', width: 120, title: '计划编号', sort: true},
                {field: 'planDate', width: 120, title: '计划日期', sort: true},
                {field: 'materialCode', width: 150, title: '物料编码'},
                {field: 'materialName', width: 200, title: '物料名称'},
                {field: 'requiredQuantity', width: 120, title: '需求数量', sort: true},
                {field: 'priority', width: 80, title: '优先级', sort: true},
                {field: 'status', width: 100, title: '状态', templet: '#statusTpl'},
                {field: 'isHot', width: 80, title: '是否热门', templet: '#isHotTpl'},
                {field: 'relocationBatchNo', width: 150, title: '移库批次号'},
                {field: 'createTime', width: 160, title: '创建时间', sort: true},
                {title: '操作', minWidth: 150, toolbar: '#currentTableBar', align: "center"}
            ]],
            limits: [10, 15, 20, 25, 50, 100],
            limit: 15,
            page: true,
            skin: 'line'
        });

        // 监听搜索操作
        form.on('submit(data-search-btn)', function (data) {
            var result = JSON.stringify(data.field);
            // 执行搜索重载
            table.reload('currentTableId', {
                page: {
                    curr: 1
                },
                where: data.field
            }, 'data');
            return false;
        });

        // 监听表格复选框选择
        table.on('checkbox(currentTableFilter)', function (obj) {
            console.log(obj)
        });

        // 监听工具条
        table.on('tool(currentTableFilter)', function (obj) {
            var data = obj.data;
            if (obj.event === 'edit') {
                openEditDialog(data);
            } else if (obj.event === 'delete') {
                layer.confirm('真的删除这条数据吗？', function (index) {
                    deleteMaterial(data.id);
                    layer.close(index);
                });
            }
        });

        // 监听头工具栏事件
        table.on('toolbar(currentTableFilter)', function (obj) {
            var checkStatus = table.checkStatus('currentTableId');
            switch (obj.event) {
                case 'add':
                    openAddDialog();
                    break;
                case 'analyzeHot':
                    analyzeHotMaterials();
                    break;
                case 'relocation':
                    if (checkStatus.data.length === 0) {
                        layer.msg('请选择要移库的数据');
                        return;
                    }
                    startRelocation(checkStatus.data);
                    break;
                case 'delete':
                    if (checkStatus.data.length === 0) {
                        layer.msg('请选择要删除的数据');
                        return;
                    }
                    layer.confirm('真的删除这些数据吗？', function (index) {
                        batchDelete(checkStatus.data);
                        layer.close(index);
                    });
                    break;
                case 'refresh':
                    table.reload('currentTableId');
                    layer.msg('数据已刷新');
                    break;
            }
        });

        // 打开新增对话框
        function openAddDialog() {
            layer.open({
                type: 2,
                title: '新增生产计划',
                content: '/pages/business/relocation/planMaterial/planMaterialInfo.html',
                maxmin: true,
                area: ['90%', '80%'],
                btn: ['保存', '取消'],
                yes: function(index, layero) {
                    var iframeWindow = window['layui-layer-iframe' + index];
                    
                    // 调用子页面的验证方法
                    if (iframeWindow.validateFormData && iframeWindow.validateFormData()) {
                        var formData = iframeWindow.collectFormData();
                        savePlanData(formData);
                        layer.close(index);
                    }
                },
                btn2: function(index, layero) {
                    layer.close(index);
                }
            });
        }

        // 打开编辑对话框
        function openEditDialog(data) {
            layer.open({
                type: 2,
                title: '编辑生产计划物料',
                content: '/pages/business/relocation/planMaterial/planMaterialInfo.html?id=' + data.id,
                maxmin: true,
                area: ['800px', '600px'],
                btn: ['确定', '取消'],
                yes: function (index, layero) {
                    var iframeWindow = window['layui-layer-iframe' + index];
                    var submitID = 'LAY-user-front-submit';
                    iframeWindow.layui.form.on('submit(' + submitID + ')', function (data) {
                        updateMaterial(data.field);
                        layer.close(index);
                    });
                    iframeWindow.layui.$("#" + submitID).trigger('click');
                }
            });
        }

        // 分析热门物料
        function analyzeHotMaterials() {
            var planNo = $("input[name='planNo']").val();
            if (!planNo) {
                layer.msg('请先输入计划编号');
                return;
            }
            
            layer.load(1);
            $.post('/api/production/plan/analyze-hot-materials/' + planNo, function (result) {
                layer.closeAll('loading');
                if (result.success) {
                    layer.msg('热门物料分析完成');
                    table.reload('currentTableId');
                } else {
                    layer.msg('分析失败：' + result.message);
                }
            });
        }

        // 开始移库操作
        function startRelocation(data) {
            var planNos = [...new Set(data.map(item => item.planNo))];
            if (planNos.length > 1) {
                layer.msg('请选择同一个计划的物料进行移库');
                return;
            }

            layer.confirm('确定要对选中的物料执行移库操作吗？', {
                icon: 3,
                title: '移库确认'
            }, function (index) {
                var materialCodes = data.map(item => item.materialCode);
                executeRelocation(planNos[0], materialCodes);
                layer.close(index);
            });
        }

        // 执行移库
        function executeRelocation(planNo, materialCodes) {
            layer.load(1);
            $.post('/api/relocation/task/generate', {
                planNo: planNo,
                operatorId: 'admin',
                selectedMaterials: materialCodes,
                mode: 2
            }, function (result) {
                layer.closeAll('loading');
                if (result.success) {
                    layer.alert('移库任务生成成功！<br>批次号：' + result.data.batchNo + 
                              '<br>任务数量：' + result.data.taskCount + 
                              '<br>预计完成时间：' + result.data.estimatedMinutes + '分钟', {
                        icon: 1,
                        title: '移库成功'
                    });
                    table.reload('currentTableId');
                } else {
                    layer.msg('移库失败：' + result.message);
                }
            });
        }

        // 保存生产计划数据
        function savePlanData(data) {
            // 验证数据格式
            if (!data.planNo || !data.planDate || !data.materials || data.materials.length === 0) {
                layer.msg('请完善计划信息和物料清单');
                return;
            }

            layer.load(1);
            
            // 发送到正确的接口
            $.ajax({
                url: '/api/production/plan/receive',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(data),
                success: function(result) {
                    layer.closeAll('loading');
                    if (result.success) {
                        layer.msg('生产计划保存成功！物料数量：' + data.materials.length + ' 个', {
                            icon: 1,
                            time: 2000
                        });
                        table.reload('currentTableId');
                    } else {
                        layer.msg('保存失败：' + result.message, {icon: 2});
                    }
                },
                error: function() {
                    layer.closeAll('loading');
                    layer.msg('网络错误，请重试', {icon: 2});
                }
            });
        }

        // 保存单个物料（用于编辑模式）
        function saveMaterial(data) {
            $.post('/api/production/plan/update', data, function (result) {
                if (result.success) {
                    layer.msg('保存成功');
                    table.reload('currentTableId');
                } else {
                    layer.msg('保存失败：' + result.message);
                }
            });
        }

        // 更新物料
        function updateMaterial(data) {
            $.post('/api/production/plan/update', data, function (result) {
                if (result.success) {
                    layer.msg('更新成功');
                    table.reload('currentTableId');
                } else {
                    layer.msg('更新失败：' + result.message);
                }
            });
        }

        // 删除物料
        function deleteMaterial(id) {
            $.post('/api/production/plan/delete', {id: id}, function (result) {
                if (result.success) {
                    layer.msg('删除成功');
                    table.reload('currentTableId');
                } else {
                    layer.msg('删除失败：' + result.message);
                }
            });
        }

        // 批量删除
        function batchDelete(data) {
            var ids = data.map(item => item.id);
            $.post('/api/production/plan/batch-delete', {ids: ids}, function (result) {
                if (result.success) {
                    layer.msg('删除成功');
                    table.reload('currentTableId');
                } else {
                    layer.msg('删除失败：' + result.message);
                }
            });
        }

    });
</script>
</body>
</html>
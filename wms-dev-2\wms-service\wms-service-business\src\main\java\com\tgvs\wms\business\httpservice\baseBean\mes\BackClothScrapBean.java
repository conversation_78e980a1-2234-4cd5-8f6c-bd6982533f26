package com.tgvs.wms.business.httpservice.baseBean.mes;

import com.alibaba.fastjson.annotation.JSONField;

public class BackClothScrapBean {
    @J<PERSON><PERSON>ield(name = "ClothNo")
    private String ClothNo;

    @JSONField(name = "UserNo")
    private String UserNo;

    public void setClothNo(String ClothNo) {
        this.ClothNo = ClothNo;
    }

    public void setUserNo(String UserNo) {
        this.UserNo = UserNo;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof BackClothScrapBean))
            return false;
        BackClothScrapBean other = (BackClothScrapBean)o;
        if (!other.canEqual(this))
            return false;
        Object this$ClothNo = getClothNo(), other$ClothNo = other.getClothNo();
        if ((this$ClothNo == null) ? (other$ClothNo != null) : !this$ClothNo.equals(other$ClothNo))
            return false;
        Object this$UserNo = getUserNo(), other$UserNo = other.getUserNo();
        return !((this$UserNo == null) ? (other$UserNo != null) : !this$UserNo.equals(other$UserNo));
    }

    protected boolean canEqual(Object other) {
        return other instanceof BackClothScrapBean;
    }

    public int hashCode() {
        int PRIME = 59,
        result = 1;
        Object $ClothNo = getClothNo();
        result = result * 59 + (($ClothNo == null) ? 43 : $ClothNo.hashCode());
        Object $UserNo = getUserNo();
        return result * 59 + (($UserNo == null) ? 43 : $UserNo.hashCode());
    }

    public String toString() {
        return "BackClothScrapBean(ClothNo=" + getClothNo() + ", UserNo=" + getUserNo() + ")";
    }

    public String getClothNo() {
        return this.ClothNo;
    }

    public String getUserNo() {
        return this.UserNo;
    }
}

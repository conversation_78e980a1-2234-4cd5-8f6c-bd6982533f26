package com.tgvs.wms.common.util;

import org.apache.shiro.SecurityUtils;
import org.apache.shiro.crypto.SecureRandomNumberGenerator;
import org.apache.shiro.session.Session;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.subject.SimplePrincipalCollection;
import org.apache.shiro.subject.Subject;

import com.tgvs.wms.common.core.domain.LoginUser;
import com.tgvs.wms.common.util.bean.BeanUtils;

/**
 * shiro 工具类
 * 
 * <AUTHOR>
 */
public class ShiroUtils
{
    public static Subject getSubject()
    {
        return SecurityUtils.getSubject();
    }

    public static Session getSession()
    {
        return SecurityUtils.getSubject().getSession();
    }

    public static void logout()
    {
        getSubject().logout();
    }

    public static LoginUser getLoginUser()
    {
        LoginUser user = null;
        Object obj = getSubject().getPrincipal();
        if (StringUtils.isNotNull(obj))
        {
            user = new LoginUser();
            BeanUtils.copyBeanProp(user, obj);
        }
        return user;
    }

    public static void setSysUser(LoginUser user)
    {
        Subject subject = getSubject();
        PrincipalCollection principalCollection = subject.getPrincipals();
        String realmName = principalCollection.getRealmNames().iterator().next();
        PrincipalCollection newPrincipalCollection = new SimplePrincipalCollection(user, realmName);
        // 重新加载Principal
        subject.runAs(newPrincipalCollection);
    }
    public static void setMultiModule(boolean multiModule)
    {
        LoginUser user = getLoginUser();
        if(user != null){
            user.setMultiModule(multiModule);
            setSysUser(user);
        }
    }

    public static String getUserId()
    {
        LoginUser user = getLoginUser();
        return user != null ? user.getUserId() : null;
    }

    public static String getLoginName()
    {
        LoginUser user = getLoginUser();
        return user != null ? user.getUsername() : null;
    }

    /**
     * 安全获取当前登录用户名，如果未登录则返回默认值
     * @param defaultValue 默认值
     * @return 用户名或默认值
     */
    public static String getLoginNameSafely(String defaultValue)
    {
        String loginName = getLoginName();
        return loginName != null ? loginName : defaultValue;
    }

    /**
     * 安全获取当前登录用户名，如果未登录则返回"system"
     * @return 用户名或"system"
     */
    public static String getLoginNameSafely()
    {
        return getLoginNameSafely("system");
    }

    public static String getIp()
    {
        try {
            Subject subject = getSubject();
            if (subject != null && subject.getSession(false) != null) {
                return subject.getSession().getHost();
            }
        } catch (Exception e) {
            // 会话可能已过期，忽略异常
        }
        return null;
    }

    public static String getSessionId()
    {
        try {
            Subject subject = getSubject();
            if (subject != null && subject.getSession(false) != null) {
                return String.valueOf(subject.getSession().getId());
            }
        } catch (Exception e) {
            // 会话可能已过期，忽略异常
        }
        return null;
    }

    /**
     * 生成随机盐
     */
    public static String randomSalt()
    {
        // 一个Byte占两个字节，此处生成的3字节，字符串长度为6
        SecureRandomNumberGenerator secureRandom = new SecureRandomNumberGenerator();
        String hex = secureRandom.nextBytes(3).toHex();
        return hex;
    }
}

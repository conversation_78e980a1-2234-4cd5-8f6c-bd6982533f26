package com.tgvs.wms.business.modules.msg.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tgvs.wms.base.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TableName("wms_msg_abb")
@ApiModel(value = "wms_msg_abb对象", description = "abb消息")
@Data
public class MsgAbb  extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("消息类型")
    private String msgType;

    @ApiModelProperty("消息内容")
    private String msgContent;

    @ApiModelProperty("state")
    private Integer state;
}

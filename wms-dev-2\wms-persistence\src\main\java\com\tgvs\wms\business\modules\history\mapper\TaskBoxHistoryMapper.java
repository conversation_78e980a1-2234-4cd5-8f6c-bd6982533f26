package com.tgvs.wms.business.modules.history.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tgvs.wms.business.modules.history.entity.TaskBoxHistory;
import com.tgvs.wms.business.modules.task.entity.TaskBoxChart;

public interface TaskBoxHistoryMapper extends BaseMapper<TaskBoxHistory> {
    void updateTreeNodeStatus(@Param("id") String paramString1, @Param("status") String paramString2);

    List<TaskBoxChart> selectListCount();
}

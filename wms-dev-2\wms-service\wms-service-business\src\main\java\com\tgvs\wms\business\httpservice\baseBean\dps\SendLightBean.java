package com.tgvs.wms.business.httpservice.baseBean.dps;


import java.util.List;

import com.alibaba.fastjson.annotation.JSONField;

public class SendLightBean {
    @J<PERSON>NField(name = "Light")
    private List<LightBean> Light;

    @J<PERSON>NField(name = "DisplayScreen")
    private List<LightBean> DisplayScreen;

    @J<PERSON>NField(name = "Singal")
    private List<SingalBean> Singal;

    @JSO<PERSON>ield(name = "ControllerID")
    private int ControllerID;

    public void setLight(List<LightBean> Light) {
        this.Light = Light;
    }

    public void setDisplayScreen(List<LightBean> DisplayScreen) {
        this.DisplayScreen = DisplayScreen;
    }

    public void setSingal(List<SingalBean> Singal) {
        this.Singal = Singal;
    }

    public void setControllerID(int ControllerID) {
        this.ControllerID = ControllerID;
    }

    public void setAsyn(boolean Asyn) {
        this.Asyn = Asyn;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof SendLightBean))
            return false;
        SendLightBean other = (SendLightBean)o;
        if (!other.canEqual(this))
            return false;
        List<LightBean> this$Light = (List<LightBean>)getLight(), other$Light = (List<LightBean>)other.getLight();
        if ((this$Light == null) ? (other$Light != null) : !this$Light.equals(other$Light))
            return false;
        List<LightBean> this$DisplayScreen = (List<LightBean>)getDisplayScreen(), other$DisplayScreen = (List<LightBean>)other.getDisplayScreen();
        if ((this$DisplayScreen == null) ? (other$DisplayScreen != null) : !this$DisplayScreen.equals(other$DisplayScreen))
            return false;
        List<SingalBean> this$Singal = (List<SingalBean>)getSingal(), other$Singal = (List<SingalBean>)other.getSingal();
        return ((this$Singal == null) ? (other$Singal != null) : !this$Singal.equals(other$Singal)) ? false : ((getControllerID() != other.getControllerID()) ? false : (!(isAsyn() != other.isAsyn())));
    }

    protected boolean canEqual(Object other) {
        return other instanceof SendLightBean;
    }

    public int hashCode() {
        int PRIME = 59,
        result = 1;
        List<LightBean> $Light = (List<LightBean>)getLight();
        result = result * 59 + (($Light == null) ? 43 : $Light.hashCode());
        List<LightBean> $DisplayScreen = (List<LightBean>)getDisplayScreen();
        result = result * 59 + (($DisplayScreen == null) ? 43 : $DisplayScreen.hashCode());
        List<SingalBean> $Singal = (List<SingalBean>)getSingal();
        result = result * 59 + (($Singal == null) ? 43 : $Singal.hashCode());
        result = result * 59 + getControllerID();
        return result * 59 + (isAsyn() ? 79 : 97);
    }

    public String toString() {
        return "SendLightBean(Light=" + getLight() + ", DisplayScreen=" + getDisplayScreen() + ", Singal=" + getSingal() + ", ControllerID=" + getControllerID() + ", Asyn=" + isAsyn() + ")";
    }

    public List<LightBean> getLight() {
        return this.Light;
    }

    public List<LightBean> getDisplayScreen() {
        return this.DisplayScreen;
    }

    public List<SingalBean> getSingal() {
        return this.Singal;
    }

    public int getControllerID() {
        return this.ControllerID;
    }

    @JSONField(name = "Asyn")
    private boolean Asyn = false;

    public boolean isAsyn() {
        return this.Asyn;
    }
}

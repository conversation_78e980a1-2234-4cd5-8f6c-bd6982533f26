package com.tgvs.wms.framework.shiro.service;

import java.util.concurrent.atomic.AtomicInteger;

import javax.annotation.PostConstruct;

import org.apache.shiro.cache.Cache;
import org.apache.shiro.cache.CacheManager;
import org.apache.shiro.crypto.hash.Md5Hash;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.tgvs.wms.common.constant.CommonConstant;
import com.tgvs.wms.common.constant.Constants;
import com.tgvs.wms.common.constant.ShiroConstants;
import com.tgvs.wms.common.exception.user.UserPasswordNotMatchException;
import com.tgvs.wms.common.exception.user.UserPasswordRetryLimitExceedException;
import com.tgvs.wms.common.util.MessageUtils;
import com.tgvs.wms.common.util.PasswordUtil;
import com.tgvs.wms.common.util.StringUtils;
import com.tgvs.wms.framework.manager.AsyncManager;
import com.tgvs.wms.framework.manager.factory.AsyncFactory;
import com.tgvs.wms.system.entity.SysUser;

/**
 * 登录密码方法
 * 
 * <AUTHOR>
 */
@Component
public class SysPasswordService
{
    @Autowired
    private CacheManager cacheManager;

    private Cache<String, AtomicInteger> loginRecordCache;

    @Value(value = "${user.password.maxRetryCount:5}")
    private String maxRetryCount;

    @PostConstruct
    public void init()
    {
        loginRecordCache = cacheManager.getCache(ShiroConstants.LOGINRECORDCACHE);
    }

    public void validate(SysUser user, String password)
    {
        String loginName = user.getUsername();

        AtomicInteger retryCount = loginRecordCache.get(loginName);

        if (retryCount == null)
        {
            retryCount = new AtomicInteger(0);
            loginRecordCache.put(loginName, retryCount);
        }
        if (retryCount.incrementAndGet() > Integer.valueOf(maxRetryCount).intValue())
        {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(loginName, Constants.LOGIN_FAIL, MessageUtils.message("user.password.retry.limit.exceed", maxRetryCount)));
            throw new UserPasswordRetryLimitExceedException(Integer.valueOf(maxRetryCount).intValue());
        }

        if (!matches(user, password))
        {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(loginName, Constants.LOGIN_FAIL, MessageUtils.message("user.password.retry.limit.count", retryCount)));
            loginRecordCache.put(loginName, retryCount);
            throw new UserPasswordNotMatchException();
        }
        else
        {
            clearLoginRecordCache(loginName);
        }
    }

    public boolean matches(SysUser user, String newPassword){
        return StringUtils.equalsIgnoreCase(user.getPassword(), PasswordUtil.encrypt(user.getUsername(), newPassword, user.getSalt()));
    }

    public void clearLoginRecordCache(String loginName)
    {
        loginRecordCache.remove(loginName);
    }

}

package com.tgvs.wms.business.modules.auxiliaryInventory.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tgvs.wms.business.modules.auxiliaryInventory.entity.WmsAuxiliaryInventory;
import com.tgvs.wms.business.modules.auxiliaryInventory.entity.WmsAuxiliaryInventoryDetail;
import com.tgvs.wms.common.core.domain.QueryModel;

import java.util.List;
import java.util.Map;

/**
 * 辅料盘点单服务接口
 * 
 * <AUTHOR>
 */
public interface IWmsAuxiliaryInventoryService extends IService<WmsAuxiliaryInventory> {

    /**
     * 分页查询 (符合BaseController标准)
     */
    IPage<WmsAuxiliaryInventory> pageList(QueryModel queryModel);

    /**
     * 创建盘点单
     */
    boolean createInventory(WmsAuxiliaryInventory inventory);

    /**
     * 更新盘点单
     */
    boolean updateInventory(WmsAuxiliaryInventory inventory);

    /**
     * 删除盘点单 (兼容String[]参数)
     */
    boolean deleteInventory(String[] ids);

    /**
     * 从库存创建盘点任务
     */
    String createInventoryFromStock(Map<String, Object> request);

    /**
     * 获取盘点明细
     */
    List<WmsAuxiliaryInventoryDetail> getInventoryDetails(String inventoryId);

    /**
     * 创建AGV出库任务
     */
    String createAgvOutboundTask(String detailId);

    /**
     * 提交盘点结果
     */
    boolean submitInventoryCount(Map<String, Object> request);

    /**
     * 查询AGV任务状态
     */
    Map<String, Object> getAgvTaskStatus(String taskId);

    /**
     * 开始盘点
     */
    boolean startInventory(String inventoryId);

    /**
     * 完成盘点
     */
    boolean completeInventory(String inventoryId);

    /**
     * 重置盘点明细
     */
    boolean resetInventoryDetail(String detailId);

    /**
     * 删除盘点明细
     */
    boolean deleteInventoryDetail(String detailId);

    /**
     * 批量删除盘点明细
     */
    boolean deleteInventoryDetails(String[] detailIds);

    /**
     * 向盘点单添加库存
     */
    boolean addStockToInventory(String inventoryId, List<com.tgvs.wms.business.modules.auxiliaryInventory.entity.WmsAuxiliaryInventoryDetail> details);
}
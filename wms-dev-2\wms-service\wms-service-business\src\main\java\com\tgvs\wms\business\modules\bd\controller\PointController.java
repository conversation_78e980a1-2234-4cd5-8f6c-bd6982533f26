package com.tgvs.wms.business.modules.bd.controller;

import java.util.Arrays;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tgvs.wms.base.controller.BaseController;
import com.tgvs.wms.business.modules.bd.entity.Point;
import com.tgvs.wms.business.modules.bd.service.IPointService;
import com.tgvs.wms.business.enums.enumPointType;
import com.tgvs.wms.business.wmsservice.server.AgvService;
import com.tgvs.wms.common.annotation.AutoLog;
import com.tgvs.wms.common.constant.Result;
import com.tgvs.wms.common.core.domain.QueryModel;
import com.tgvs.wms.common.query.QueryGenerator;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Api(tags = {"设备节点管理"})
@RestController
@RequestMapping({"/bd/point"})
@Slf4j
public class PointController extends BaseController<Point, IPointService> {
    @Autowired
    private IPointService pointService;

    private String TAG = "tag123";

    @ApiOperation(value = "设备节点管理-分页列表查询", notes = "设备节点管理-分页列表查询")
    @PostMapping({"/list"})
    public Result<?> queryPageList(@RequestBody(required = false) QueryModel queryModel) {
        IPage<Point> list = pointService.pageList(queryModel);
        Result result = Result.ok(list.getRecords());
        result.setTotal(list.getTotal());
        return result;
    }

    @AutoLog("设备节点管理-添加")
    @ApiOperation(value = "设备节点管理-添加", notes = "设备节点管理-添加")
    @RequiresPermissions({"point:add"})
    @PostMapping({"/add"})
    public Result<?> add(@RequestBody Point point) {
        this.pointService.save(point);
        return Result.OK("添加成功！");
    }

    @AutoLog("设备节点管理-编辑")
    @ApiOperation(value = "设备节点管理-编辑", notes = "设备节点管理-编辑")
    @RequiresPermissions({"point:edit"})
    @PostMapping({"/edit"})
    public Result<?> edit(@RequestBody Point point) {
        Point pointold = (Point)this.pointService.getById(point.getId());
        if (null != pointold)
            if (!pointold.getLocked().equals(point.getLocked()))
                if (point.getPointType().equals(enumPointType.cutting.getValue()))
                    AgvService.setLathetoAgv(point);
        this.pointService.updateById(point);
        return Result.OK("编辑成功!");
    }

    @AutoLog("设备节点管理-通过id删除")
    @ApiOperation(value = "设备节点管理-通过id删除", notes = "设备节点管理-通过id删除")
    @RequiresPermissions({"point:delete"})
    @PostMapping({"/delete"})
    public Result<?> delete(@RequestBody String id) {
        this.pointService.removeById(id);
        return Result.OK("删除成功!");
    }

    @AutoLog("设备节点管理-批量删除")
    @ApiOperation(value = "设备节点管理-批量删除", notes = "设备节点管理-批量删除")
    @RequiresPermissions({"point:deleteBatch"})
    @PostMapping({"/deleteBatch"})
    public Result<?> deleteBatch(@RequestBody String ids) {
        this.pointService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    @AutoLog("设备节点管理-通过id查询")
    @ApiOperation(value = "设备节点管理-通过id查询", notes = "设备节点管理-通过id查询")
    @PostMapping({"/queryById"})
    public Result<?> queryById(@RequestBody String id) {
        Point point = (Point)this.pointService.getById(id);
        if (point == null)
            return Result.error("未找到对应数据");
        return Result.OK(point);
    }

    @AutoLog("设备节点管理-通过pointNo查询")
    @ApiOperation(value = "设备节点管理-通过pointNo查询", notes = "设备节点管理-通过pointNo查询")
    @RequestMapping({"/queryByPointNo"})
    public Result<?> queryByPointNo(@RequestBody String pointno) {
        log.info(this.TAG + pointno);
        Point point = this.pointService.getPointByNo(pointno);
        log.info("sss789s:" + JSON.toJSONString(point));
        log.info("iiii:" + JSON.toJSONString(point));
        if (point == null)
            return Result.error("未找到对应数据");
        return Result.OK(point);
    }

    @RequestMapping({"/exportXls"})
    public ModelAndView exportXls(HttpServletRequest request, Point point) {
        return exportXls(request, point, Point.class, "设备节点管理");
    }

    @RequiresPermissions({"point:importExcel"})
    @RequestMapping(value = {"/importExcel"}, method = {RequestMethod.POST})
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return importExcel(request, response, Point.class);
    }
}

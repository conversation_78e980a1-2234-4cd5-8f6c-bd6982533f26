<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tgvs.wms.business.modules.history.mapper.RobotDispatchHistoryMapper">

	<update id="updateTreeNodeStatus" parameterType="java.lang.String">
		update wms_robot_dispatch set has_child = #{status} where id = #{id}
	</update>

	<select id="selectByTaskId" parameterType="java.lang.Integer" resultType="com.tgvs.wms.business.modules.dispatch.entity.RobotDispatch">
		select * from wms_robot_dispatch where task_id = #{taskId} LIMIT 1
	</select>
</mapper>
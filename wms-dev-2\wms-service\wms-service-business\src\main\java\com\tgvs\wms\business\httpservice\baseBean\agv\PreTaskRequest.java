package com.tgvs.wms.business.httpservice.baseBean.agv;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class PreTaskRequest implements Serializable{
    /**
     * 站点编码
     */
    @NotNull(message = "站点编码不能为空")
    private String siteCode;
    /**
     * 预调度时间
     */
    @NotNull(message = "预调度时间不能为空，默认60秒")
    private Integer nextTaskTime=180;
    /**
     * 机器人类型
     */
    private String robotType;

}

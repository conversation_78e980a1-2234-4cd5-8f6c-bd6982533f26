package com.tgvs.wms.business.httpservice.baseBean.agv;


import com.alibaba.fastjson.annotation.J<PERSON>NField;

public class transferDolly {
    @J<PERSON><PERSON>ield(name = "TaskID")
    private String TaskID;

    @J<PERSON><PERSON><PERSON>(name = "FromSite")
    private String FromSite;

    @<PERSON><PERSON><PERSON><PERSON>(name = "ToSite")
    private String ToSite;

    @J<PERSON><PERSON><PERSON>(name = "<PERSON>No")
    private String DollyNo;

    @<PERSON><PERSON><PERSON><PERSON>(name = "ScrapFlag")
    private String ScrapFlag;

    @J<PERSON>NField(name = "Content")
    private String Content;

    @J<PERSON><PERSON>ield(name = "SysCode")
    private String SysCode;

    public void setTaskID(String TaskID) {
        this.TaskID = TaskID;
    }

    public void setFromSite(String FromSite) {
        this.FromSite = FromSite;
    }

    public void setToSite(String ToSite) {
        this.ToSite = ToSite;
    }

    public void setDollyNo(String DollyNo) {
        this.DollyNo = DollyNo;
    }

    public void setScrapFlag(String ScrapFlag) {
        this.ScrapFlag = ScrapFlag;
    }

    public void setContent(String Content) {
        this.Content = Content;
    }

    public void setSysCode(String SysCode) {
        this.SysCode = SysCode;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof transferDolly))
            return false;
        transferDolly other = (transferDolly)o;
        if (!other.canEqual(this))
            return false;
        Object this$TaskID = getTaskID(), other$TaskID = other.getTaskID();
        if ((this$TaskID == null) ? (other$TaskID != null) : !this$TaskID.equals(other$TaskID))
            return false;
        Object this$FromSite = getFromSite(), other$FromSite = other.getFromSite();
        if ((this$FromSite == null) ? (other$FromSite != null) : !this$FromSite.equals(other$FromSite))
            return false;
        Object this$ToSite = getToSite(), other$ToSite = other.getToSite();
        if ((this$ToSite == null) ? (other$ToSite != null) : !this$ToSite.equals(other$ToSite))
            return false;
        Object this$DollyNo = getDollyNo(), other$DollyNo = other.getDollyNo();
        if ((this$DollyNo == null) ? (other$DollyNo != null) : !this$DollyNo.equals(other$DollyNo))
            return false;
        Object this$ScrapFlag = getScrapFlag(), other$ScrapFlag = other.getScrapFlag();
        if ((this$ScrapFlag == null) ? (other$ScrapFlag != null) : !this$ScrapFlag.equals(other$ScrapFlag))
            return false;
        Object this$Content = getContent(), other$Content = other.getContent();
        if ((this$Content == null) ? (other$Content != null) : !this$Content.equals(other$Content))
            return false;
        Object this$SysCode = getSysCode(), other$SysCode = other.getSysCode();
        return !((this$SysCode == null) ? (other$SysCode != null) : !this$SysCode.equals(other$SysCode));
    }

    protected boolean canEqual(Object other) {
        return other instanceof transferDolly;
    }

    public int hashCode() {
        int PRIME = 59, result = 1;
        Object $TaskID = getTaskID();
        result = result * 59 + (($TaskID == null) ? 43 : $TaskID.hashCode());
        Object $FromSite = getFromSite();
        result = result * 59 + (($FromSite == null) ? 43 : $FromSite.hashCode());
        Object $ToSite = getToSite();
        result = result * 59 + (($ToSite == null) ? 43 : $ToSite.hashCode());
        Object $DollyNo = getDollyNo();
        result = result * 59 + (($DollyNo == null) ? 43 : $DollyNo.hashCode());
        Object $ScrapFlag = getScrapFlag();
        result = result * 59 + (($ScrapFlag == null) ? 43 : $ScrapFlag.hashCode());
        Object $Content = getContent();
        result = result * 59 + (($Content == null) ? 43 : $Content.hashCode());
        Object $SysCode = getSysCode();
        return result * 59 + (($SysCode == null) ? 43 : $SysCode.hashCode());
    }

    public String toString() {
        return "transferDolly(TaskID=" + getTaskID() + ", FromSite=" + getFromSite() + ", ToSite=" + getToSite() + ", DollyNo=" + getDollyNo() + ", ScrapFlag=" + getScrapFlag() + ", Content=" + getContent() + ", SysCode=" + getSysCode() + ")";
    }

    public String getTaskID() {
        return this.TaskID;
    }

    public String getFromSite() {
        return this.FromSite;
    }

    public String getToSite() {
        return this.ToSite;
    }

    public String getDollyNo() {
        return this.DollyNo;
    }

    public String getScrapFlag() {
        return this.ScrapFlag;
    }

    public String getContent() {
        return this.Content;
    }

    public String getSysCode() {
        return this.SysCode;
    }
}

package com.tgvs.wms.business.modules.bd.controller;

import java.util.Arrays;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tgvs.wms.base.controller.BaseController;
import com.tgvs.wms.business.modules.bd.entity.TaskOutConfig;
import com.tgvs.wms.business.modules.bd.service.ITaskOutConfigService;
import com.tgvs.wms.business.wmsservice.MainService;
import com.tgvs.wms.common.annotation.AutoLog;
import com.tgvs.wms.common.constant.Result;
import com.tgvs.wms.common.query.QueryGenerator;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Api(tags = {"箱线数量设置"})
@RestController
@RequestMapping({"/base/taskOutConfig"})
@Slf4j
public class TaskOutConfigController extends BaseController<TaskOutConfig, ITaskOutConfigService> {

    @Autowired
    private ITaskOutConfigService taskOutConfigService;

    @AutoLog("箱线数量设置-分页列表查询")
    @ApiOperation(value = "箱线数量设置-分页列表查询", notes = "箱线数量设置-分页列表查询")
    @PostMapping({"/list"})
    public Result<?> queryPageList(TaskOutConfig taskOutConfig, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<TaskOutConfig> queryWrapper = QueryGenerator.initQueryWrapper(taskOutConfig, req.getParameterMap());
        Page<TaskOutConfig> page = new Page(pageNo.intValue(), pageSize.intValue());
        IPage<TaskOutConfig> pageList = this.taskOutConfigService.page((IPage)page, (Wrapper)queryWrapper);
        return Result.OK(pageList);
    }

    @AutoLog("箱线数量设置-添加")
    @ApiOperation(value = "箱线数量设置-添加", notes = "箱线数量设置-添加")
    @RequiresPermissions({"taskOutConfig:add"})
    @PostMapping({"/add"})
    public Result<?> add(@RequestBody TaskOutConfig taskOutConfig) {
        this.taskOutConfigService.save(taskOutConfig);
        return Result.OK("添加成功！");
    }

    @AutoLog("箱线数量设置-编辑")
    @ApiOperation(value = "箱线数量设置-编辑", notes = "箱线数量设置-编辑")
    @RequiresPermissions({"taskOutConfig:edit"})
    @PostMapping({"/edit"})
    public Result<?> edit(@RequestBody TaskOutConfig taskOutConfig) {
        this.taskOutConfigService.updateById(taskOutConfig);
        try {
            if (taskOutConfig.getType().equals(Integer.valueOf(1))) {
                MainService.emptybox_line_count = taskOutConfig.getValue();
            } else {
                MainService.picking_line_count = taskOutConfig.getValue();
            }
        } catch (Exception exception) {}
        return Result.OK("编辑成功!");
    }

    @AutoLog("箱线数量设置-通过id删除")
    @ApiOperation(value = "箱线数量设置-通过id删除", notes = "箱线数量设置-通过id删除")
    @RequiresPermissions({"taskOutConfig:delete"})
    @PostMapping({"/delete"})
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        this.taskOutConfigService.removeById(id);
        return Result.OK("删除成功!");
    }

    @AutoLog("箱线数量设置-批量删除")
    @ApiOperation(value = "箱线数量设置-批量删除", notes = "箱线数量设置-批量删除")
    @RequiresPermissions({"taskOutConfig:deleteBatch"})
    @PostMapping({"/deleteBatch"})
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.taskOutConfigService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    @AutoLog("箱线数量设置-通过id查询")
    @ApiOperation(value = "箱线数量设置-通过id查询", notes = "箱线数量设置-通过id查询")
    @PostMapping({"/queryById"})
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        TaskOutConfig taskOutConfig = (TaskOutConfig)this.taskOutConfigService.getById(id);
        if (taskOutConfig == null)
            return Result.error("未找到对应数据");
        return Result.OK(taskOutConfig);
    }

    @RequestMapping({"/exportXls"})
    public ModelAndView exportXls(HttpServletRequest request, TaskOutConfig taskOutConfig) {
        return exportXls(request, taskOutConfig, TaskOutConfig.class, "箱线数量设置");
    }

    @RequiresPermissions({"taskOutConfig:importExcel"})
    @RequestMapping(value = {"/importExcel"}, method = {RequestMethod.POST})
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return importExcel(request, response, TaskOutConfig.class);
    }
}

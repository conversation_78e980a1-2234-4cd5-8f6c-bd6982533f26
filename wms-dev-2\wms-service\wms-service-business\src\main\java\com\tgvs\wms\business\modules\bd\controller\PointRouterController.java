package com.tgvs.wms.business.modules.bd.controller;

import java.util.Arrays;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tgvs.wms.base.controller.BaseController;
import com.tgvs.wms.business.modules.bd.entity.Point;
import com.tgvs.wms.business.modules.bd.entity.PointRouter;
import com.tgvs.wms.business.modules.bd.service.IPointRouterService;
import com.tgvs.wms.business.modules.bd.service.IPointService;
import com.tgvs.wms.common.annotation.AutoLog;
import com.tgvs.wms.common.constant.Result;
import com.tgvs.wms.common.core.domain.QueryModel;
import com.tgvs.wms.common.query.QueryGenerator;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Api(tags = {"路由配置管理"})
@RestController
@RequestMapping({"/bd/pointRouter"})
@Slf4j
public class PointRouterController extends BaseController<PointRouter, IPointRouterService> {

    @Autowired
    private IPointRouterService pointRouterService;

    @Autowired
    private IPointService pointService;

    @ApiOperation(value = "路由配置管理-分页列表查询", notes = "路由配置管理-分页列表查询")
    @PostMapping({"/list"})
    public Result<?> queryPageList(@RequestBody(required = false) QueryModel queryModel) {
        IPage<PointRouter> list = pointRouterService.pageList(queryModel);
        Result result = Result.ok(list.getRecords());
        result.setTotal(list.getTotal());
        return result;
    }

    @AutoLog("路由配置管理-添加")
    @ApiOperation(value = "路由配置管理-添加", notes = "路由配置管理-添加")
    @RequiresPermissions({"pointRouter:add"})
    @PostMapping({"/add"})
    public Result<?> add(@RequestBody PointRouter pointRouter) {
        this.pointRouterService.save(pointRouter);
        return Result.OK("添加成功！");
    }

    @AutoLog("路由配置管理-编辑")
    @ApiOperation(value = "路由配置管理-编辑", notes = "路由配置管理-编辑")
    @RequiresPermissions({"pointRouter:edit"})
    @PostMapping({"/edit"})
    public Result<?> edit(@RequestBody PointRouter pointRouter) {
        this.pointRouterService.updateById(pointRouter);
        return Result.OK("编辑成功!");
    }

    @AutoLog("路由配置管理-通过id删除")
    @ApiOperation(value = "路由配置管理-通过id删除", notes = "路由配置管理-通过id删除")
    @RequiresPermissions({"pointRouter:delete"})
    @PostMapping({"/delete"})
    public Result<?> delete(@RequestBody String id) {
        this.pointRouterService.removeById(id);
        return Result.OK("删除成功!");
    }

    @AutoLog("路由配置管理-批量删除")
    @ApiOperation(value = "路由配置管理-批量删除", notes = "路由配置管理-批量删除")
    @RequiresPermissions({"pointRouter:deleteBatch"})
    @PostMapping({"/deleteBatch"})
    public Result<?> deleteBatch(@RequestBody String ids) {
        this.pointRouterService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    @AutoLog("路由配置管理-通过id查询")
    @ApiOperation(value = "路由配置管理-通过id查询", notes = "路由配置管理-通过id查询")
    @PostMapping({"/queryById"})
    public Result<?> queryById(@RequestBody String id) {
        PointRouter pointRouter = (PointRouter)this.pointRouterService.getById(id);
        if (pointRouter == null)
            return Result.error("未找到对应数据");
        return Result.OK(pointRouter);
    }

    @AutoLog("设备节点管理-通过pointNo查询")
    @ApiOperation(value = "设备节点管理-通过pointNo查询", notes = "设备节点管理-通过pointNo查询")
    @PostMapping({"/queryByNo"})
    public Result<?> queryByNo(@RequestBody String pointno) {
        log.info("2222222222222222222222:", pointno);
        log.info("sssssssssssssss:" + JSON.toJSONString(this.pointService));
        Point point = this.pointService.getPointByNo("S1001");
        log.info("ssss:", point);
        log.info("iiii:" + JSON.toJSONString(point));
        if (point == null)
            return Result.error("未找到对应数据");
        return Result.OK(point);
    }

    @RequestMapping({"/exportXls"})
    public ModelAndView exportXls(HttpServletRequest request, PointRouter pointRouter) {
        return exportXls(request, pointRouter, PointRouter.class, "路由配置管理");
    }

    @RequiresPermissions({"pointRouter:importExcel"})
    @RequestMapping(value = {"/importExcel"}, method = {RequestMethod.POST})
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return importExcel(request, response, PointRouter.class);
    }
}

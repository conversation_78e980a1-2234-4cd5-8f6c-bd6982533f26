package com.tgvs.wms.business.modules.auxiliaryInventory.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tgvs.wms.business.modules.auxiliaryInventory.entity.WmsAuxiliaryDifferenceSource;
import com.tgvs.wms.common.core.domain.QueryModel;

import java.util.List;

/**
 * 辅料差异数据源 Service接口
 * 
 * <AUTHOR> Generated
 * @date 2025-01-24
 */
public interface IWmsAuxiliaryDifferenceSourceService extends IService<WmsAuxiliaryDifferenceSource> {

    /**
     * 分页查询差异数据源列表
     * 
     * @param queryModel 查询条件
     * @return 分页结果
     */
    IPage<WmsAuxiliaryDifferenceSource> pageDifferenceSourceList(QueryModel queryModel);

    /**
     * 查询待处理的差异数据列表
     * 
     * @return 待处理的差异数据列表
     */
    List<WmsAuxiliaryDifferenceSource> listPendingDifference();

    /**
     * 差异数据统计
     * 
     * @return 统计结果 Map
     */
    java.util.Map<String, Object> getDifferenceStatistics();

    /**
     * 批量导入差异数据
     * 
     * @param differenceList 差异数据列表
     * @param sourceBatch 导入批次号
     * @param sourceSystem 来源系统
     * @return 导入结果
     */
    boolean batchImportDifferenceData(List<WmsAuxiliaryDifferenceSource> differenceList, String sourceBatch, String sourceSystem);

    /**
     * 批量更新状态
     * 
     * @param ids ID列表
     * @param status 新状态
     * @param updateBy 更新人
     * @return 更新结果
     */
    boolean batchUpdateStatus(List<String> ids, String status, String updateBy);

    /**
     * 根据ID列表获取差异数据(用于创建盘点单)
     * 
     * @param ids ID列表
     * @return 差异数据列表
     */
    List<WmsAuxiliaryDifferenceSource> listByIds(List<String> ids);
}
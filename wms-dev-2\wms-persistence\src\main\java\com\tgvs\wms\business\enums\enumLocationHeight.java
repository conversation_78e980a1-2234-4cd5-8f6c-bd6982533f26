package com.tgvs.wms.business.enums;

import com.tgvs.wms.common.util.oConvertUtils;

public enum enumLocationHeight {
    unknown(Integer.valueOf(0), "unknown", "未知"),
    normal(Integer.valueOf(1), "normal", "矮"),
    high(Integer.valueOf(2), "high", "高"),
    highest(Integer.valueOf(3), "highest", "最高");

    private Integer value;

    private String code;

    private String text;

    enumLocationHeight(Integer value, String code, String text) {
        this.value = value;
        this.code = code;
        this.text = text;
    }

    public static enumLocationHeight getByValue(int value) {
        if (oConvertUtils.isEmpty(Integer.valueOf(value)))
            return null;
        for (enumLocationHeight val : values()) {
            if (val.getValue().equals(Integer.valueOf(value)))
                return val;
        }
        return null;
    }

    public Integer getValue() {
        return this.value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getText() {
        return this.text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public static enumLocationHeight toEnum(Integer Value) {
        for (enumLocationHeight e : values()) {
            if (e.getValue().equals(Value))
                return e;
        }
        return null;
    }
}

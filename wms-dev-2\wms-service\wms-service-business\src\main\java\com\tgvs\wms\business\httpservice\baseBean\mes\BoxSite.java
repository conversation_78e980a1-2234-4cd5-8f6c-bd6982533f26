package com.tgvs.wms.business.httpservice.baseBean.mes;


import com.alibaba.fastjson.annotation.JSONField;

public class BoxSite {
    @J<PERSON><PERSON>ield(name = "BoxNo")
    private String BoxNo;

    @J<PERSON>NField(name = "IsEmpty")
    private Integer IsEmpty;

    @J<PERSON><PERSON>ield(name = "SiteNo")
    private String SiteNo;

    @JSONField(name = "BoxType")
    private Integer BoxType;

    @JSONField(name = "BookNo")
    private String BookNo;

    @JSONField(name = "DestOrderNo")
    private String DestOrderNo;

    public void setBoxNo(String BoxNo) {
        this.BoxNo = BoxNo;
    }

    public void setIsEmpty(Integer IsEmpty) {
        this.IsEmpty = IsEmpty;
    }

    public void setSiteNo(String SiteNo) {
        this.SiteNo = SiteNo;
    }

    public void setBoxType(Integer BoxType) {
        this.BoxType = BoxType;
    }

    public void setBookNo(String BookNo) {
        this.BookNo = BookNo;
    }

    public void setDestOrderNo(String DestOrderNo) {
        this.DestOrderNo = DestOrderNo;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof BoxSite))
            return false;
        BoxSite other = (BoxSite)o;
        if (!other.canEqual(this))
            return false;
        Object this$BoxNo = getBoxNo(), other$BoxNo = other.getBoxNo();
        if ((this$BoxNo == null) ? (other$BoxNo != null) : !this$BoxNo.equals(other$BoxNo))
            return false;
        Object this$IsEmpty = getIsEmpty(), other$IsEmpty = other.getIsEmpty();
        if ((this$IsEmpty == null) ? (other$IsEmpty != null) : !this$IsEmpty.equals(other$IsEmpty))
            return false;
        Object this$SiteNo = getSiteNo(), other$SiteNo = other.getSiteNo();
        if ((this$SiteNo == null) ? (other$SiteNo != null) : !this$SiteNo.equals(other$SiteNo))
            return false;
        Object this$BoxType = getBoxType(), other$BoxType = other.getBoxType();
        if ((this$BoxType == null) ? (other$BoxType != null) : !this$BoxType.equals(other$BoxType))
            return false;
        Object this$BookNo = getBookNo(), other$BookNo = other.getBookNo();
        if ((this$BookNo == null) ? (other$BookNo != null) : !this$BookNo.equals(other$BookNo))
            return false;
        Object this$DestOrderNo = getDestOrderNo(), other$DestOrderNo = other.getDestOrderNo();
        return !((this$DestOrderNo == null) ? (other$DestOrderNo != null) : !this$DestOrderNo.equals(other$DestOrderNo));
    }

    protected boolean canEqual(Object other) {
        return other instanceof BoxSite;
    }

    public int hashCode() {
        int PRIME = 59,
        result = 1;
        Object $BoxNo = getBoxNo();
        result = result * 59 + (($BoxNo == null) ? 43 : $BoxNo.hashCode());
        Object $IsEmpty = getIsEmpty();
        result = result * 59 + (($IsEmpty == null) ? 43 : $IsEmpty.hashCode());
        Object $SiteNo = getSiteNo();
        result = result * 59 + (($SiteNo == null) ? 43 : $SiteNo.hashCode());
        Object $BoxType = getBoxType();
        result = result * 59 + (($BoxType == null) ? 43 : $BoxType.hashCode());
        Object $BookNo = getBookNo();
        result = result * 59 + (($BookNo == null) ? 43 : $BookNo.hashCode());
        Object $DestOrderNo = getDestOrderNo();
        return result * 59 + (($DestOrderNo == null) ? 43 : $DestOrderNo.hashCode());
    }

    public String toString() {
        return "BoxSite(BoxNo=" + getBoxNo() + ", IsEmpty=" + getIsEmpty() + ", SiteNo=" + getSiteNo() + ", BoxType=" + getBoxType() + ", BookNo=" + getBookNo() + ", DestOrderNo=" + getDestOrderNo() + ")";
    }

    public String getBoxNo() {
        return this.BoxNo;
    }

    public Integer getIsEmpty() {
        return this.IsEmpty;
    }

    public String getSiteNo() {
        return this.SiteNo;
    }

    public Integer getBoxType() {
        return this.BoxType;
    }

    public String getBookNo() {
        return this.BookNo;
    }

    public String getDestOrderNo() {
        return this.DestOrderNo;
    }
}

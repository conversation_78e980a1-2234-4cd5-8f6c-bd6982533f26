package com.tgvs.wms.business.modules.bd.controller;

import java.util.Arrays;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tgvs.wms.base.controller.BaseController;
import com.tgvs.wms.business.modules.bd.entity.PtlConfig;
import com.tgvs.wms.business.modules.bd.service.IPtlConfigService;
import com.tgvs.wms.business.wmsservice.server.DpsService;
import com.tgvs.wms.common.annotation.AutoLog;
import com.tgvs.wms.common.constant.Result;
import com.tgvs.wms.common.query.QueryGenerator;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Api(tags = {"电子标签显示配置"})
@RestController
@RequestMapping({"/bd/ptlConfig"})
@Slf4j
public class PtlConfigController extends BaseController<PtlConfig, IPtlConfigService> {

    @Autowired
    private IPtlConfigService ptlConfigService;

    @AutoLog("电子标签显示配置-分页列表查询")
    @ApiOperation(value = "电子标签显示配置-分页列表查询", notes = "电子标签显示配置-分页列表查询")
    @PostMapping({"/list"})
    public Result<?> queryPageList(PtlConfig ptlConfig, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<PtlConfig> queryWrapper = QueryGenerator.initQueryWrapper(ptlConfig, req.getParameterMap());
        Page<PtlConfig> page = new Page(pageNo.intValue(), pageSize.intValue());
        IPage<PtlConfig> pageList = this.ptlConfigService.page((IPage)page, (Wrapper)queryWrapper);
        return Result.OK(pageList);
    }

    @AutoLog("电子标签显示配置-添加")
    @ApiOperation(value = "电子标签显示配置-添加", notes = "电子标签显示配置-添加")
    @RequiresPermissions({"ptlConfig:add"})
    @PostMapping({"/add"})
    public Result<?> add(@RequestBody PtlConfig ptlConfig) {
        this.ptlConfigService.save(ptlConfig);
        return Result.OK("添加成功！");
    }

    @AutoLog("电子标签显示配置-编辑")
    @ApiOperation(value = "电子标签显示配置-编辑", notes = "电子标签显示配置-编辑")
    @RequiresPermissions({"ptlConfig:edit"})
    @PostMapping({"/edit"})
    public Result<?> edit(@RequestBody PtlConfig ptlConfig) {
        this.ptlConfigService.updateById(ptlConfig);
        if (null != ptlConfig)
            try {
                DpsService.collor_put = ptlConfig.getColorValue();
                DpsService.collor_pick = ptlConfig.getColorValue2();
                DpsService.show_modle = ptlConfig.getShowType();
            } catch (Exception exception) {}
        return Result.OK("编辑成功!");
    }

    @AutoLog("电子标签显示配置-通过id删除")
    @ApiOperation(value = "电子标签显示配置-通过id删除", notes = "电子标签显示配置-通过id删除")
    @RequiresPermissions({"ptlConfig:delete"})
    @PostMapping({"/delete"})
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        this.ptlConfigService.removeById(id);
        return Result.OK("删除成功!");
    }

    @AutoLog("电子标签显示配置-批量删除")
    @ApiOperation(value = "电子标签显示配置-批量删除", notes = "电子标签显示配置-批量删除")
    @RequiresPermissions({"ptlConfig:deleteBatch"})
    @PostMapping({"/deleteBatch"})
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.ptlConfigService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    @AutoLog("电子标签显示配置-通过id查询")
    @ApiOperation(value = "电子标签显示配置-通过id查询", notes = "电子标签显示配置-通过id查询")
    @PostMapping({"/queryById"})
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        PtlConfig ptlConfig = (PtlConfig)this.ptlConfigService.getById(id);
        if (ptlConfig == null)
            return Result.error("未找到对应数据");
        return Result.OK(ptlConfig);
    }

    @RequestMapping({"/exportXls"})
    public ModelAndView exportXls(HttpServletRequest request, PtlConfig ptlConfig) {
        return exportXls(request, ptlConfig, PtlConfig.class, "电子标签显示配置");
    }

    @RequiresPermissions({"ptlConfig:importExcel"})
    @RequestMapping(value = {"/importExcel"}, method = {RequestMethod.POST})
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return importExcel(request, response, PtlConfig.class);
    }
}

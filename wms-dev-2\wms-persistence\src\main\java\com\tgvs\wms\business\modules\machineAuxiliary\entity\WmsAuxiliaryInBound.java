package com.tgvs.wms.business.modules.machineAuxiliary.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.tgvs.wms.base.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 辅料入库实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wms_auxiliary_inbound")
public class WmsAuxiliaryInBound extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("入库单号")
    private String inStoreNumber;

    /**
     * 入库类型（1.采购入库，2.一般入库，3.产线退料回库;）
     */
    @ApiModelProperty("入库类型（1.采购入库，2.一般入库，3.产线退料回库）")
    private Integer taskType;

    /**
     * 状态(0:待处理,1:处理中,2:已完成,3:已取消)
     */
    @ApiModelProperty("状态(0:待处理,1:处理中,2:已完成,3:已取消)")
    private Integer status;

    private Integer deleteFlag;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remarks;
} 
package com.tgvs.wms.business.modules.task.entity;


import java.io.Serializable;
import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.tgvs.wms.base.entity.BaseEntity;
import com.tgvs.wms.common.annotation.Dict;
import com.tgvs.wms.common.annotation.Excel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("wms_prepare_task")
@ApiModel(value = "wms_prepare_task对象", description = "备货任务")
public class PrepareTask extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("id")
    private String id;

    @Excel(name = "拉布单号", width = 15.0D)
    @ApiModelProperty("拉布单号")
    private String billNo;

    @Excel(name = "对应铺床号", width = 15.0D)
    @ApiModelProperty("对应铺床号")
    private String latheNo;

    @Excel(name = "铺布计划编号", width = 15.0D)
    @ApiModelProperty("铺布计划编号")
    private String pubuPlan;

    @Excel(name = " 对应区域", width = 15.0D, dictTable = "wms_base_area", dicText = "text", dicCode = "code")
    @Dict(dictTable = "wms_base_area", dicText = "text", dicCode = "code")
    @ApiModelProperty(" 对应区域")
    private String area;

    @Excel(name = "布匹总数", width = 15.0D)
    @ApiModelProperty("布匹总数")
    private Integer clothCount;

    @Excel(name = "完成数量", width = 15.0D)
    @ApiModelProperty("完成数量")
    private Integer completedCount;

    @Excel(name = "合约号", width = 15.0D)
    @ApiModelProperty("合约号")
    private String contractNo;

    @Excel(name = "计划日期", width = 15.0D, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("计划日期")
    private Date planTime;

    @Excel(name = "发布时间", width = 20.0D, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("发布更新日期")
    private Date publishTime;

    @Excel(name = "执行状态", width = 15.0D, dictTable = "wms_prepare_state", dicText = "text", dicCode = "value")
    @Dict(enumType = "PrepareStatus", dicText = "text", dicCode = "value")
    @ApiModelProperty("执行状态")
    private Integer state;

    @Excel(name = "完成标志", width = 15.0D, dicCode = "yn")
    @Dict(dicCode = "yn")
    @ApiModelProperty("完成标志")
    private Integer completed;

    @Excel(name = "计划执行顺序号", width = 15.0D)
    @ApiModelProperty("计划执行顺序号")
    private Integer planSort;

    @Excel(name = "优先级", width = 15.0D)
    @ApiModelProperty("优先级")
    private Integer priority;

    @Excel(name = "允许备货", width = 15.0D, dicCode = "yn")
    @Dict(dicCode = "yn")
    @ApiModelProperty("允许备货")
    private Integer allow;

    @ApiModelProperty("创建人")
    private String createBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("更新时间")
    private Date updateTime;

    @Excel(name = "删除状态", width = 15.0D)
    @ApiModelProperty("删除状态")
    private Integer delFlag;

    @Excel(name = "AGV搬运标志", width = 15.0D, dicCode = "agv_carry")
    @Dict(dicCode = "agv_carry")
    @ApiModelProperty("AGV搬运标志")
    private Integer agvCompleted;

    @ApiModelProperty("提示信息")
    private String msg;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("执行时间")
    private Date startTime;

    @ApiModelProperty("源铺床")
    private String depLatheno;
}

package com.tgvs.wms.business.enums;

import com.tgvs.wms.common.util.oConvertUtils;

public enum enumLocationLevel {
    zero(Integer.valueOf(0), "zero", "0层"),
    four(Integer.valueOf(4), "four", "4层"),
    five(Integer.valueOf(5), "five", "5层");

    private Integer value;

    private String code;

    private String text;

    enumLocationLevel(Integer value, String code, String text) {
        this.value = value;
        this.code = code;
        this.text = text;
    }

    public static enumLocationLevel getByValue(int value) {
        if (oConvertUtils.isEmpty(Integer.valueOf(value)))
            return null;
        for (enumLocationLevel val : values()) {
            if (val.getValue().equals(Integer.valueOf(value)))
                return val;
        }
        return null;
    }

    public Integer getValue() {
        return this.value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getText() {
        return this.text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public static enumLocationLevel toEnum(Integer Value) {
        for (enumLocationLevel e : values()) {
            if (e.getValue().equals(Value))
                return e;
        }
        return null;
    }
}

package com.tgvs.wms.business.modules.auxiliaryInventory.vo;

import lombok.Data;

import java.util.List;

/**
 * 盘点任务创建请求
 */
@Data
public class InventoryCreateRequest {
    
    /**
     * 盘点单名称
     */
    private String inventoryName;
    
    /**
     * 盘点类型：PART-抽盘，DIFF-差异盘点
     */
    private String inventoryType;
    
    /**
     * 仓库编码
     */
    private String warehouseCode;
    
    /**
     * 仓库名称
     */
    private String warehouseName;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 库存数据列表
     */
    private List<StockData> stockList;
    
    /**
     * 库存数据
     */
    @Data
    public static class StockData {
        private String materialCode;          // 物料编码
        private String materialName;          // 物料名称
        private String code;                  // 库位编码
        private String boxNo;                 // 容器号
        private java.math.BigDecimal materialQuantity; // 库存数量
        private String materialColor;         // 物料颜色
        private String materialSize;          // 尺码
        private String materialModel;         // 规格
        private String unit;                  // 单位
    }
}
package com.tgvs.wms.business.httpservice.baseBean.agv;

import java.util.ArrayList;
import java.util.List;

import com.tgvs.wms.business.enums.enumYesOrNo;

public class ContentBean {
    private Integer isHave;

    private Integer Layer;

    private String ClothNo;

    public void setIsHave(Integer isHave) {
        this.isHave = isHave;
    }

    public void setLayer(Integer Layer) {
        this.Layer = Layer;
    }

    public void setClothNo(String ClothNo) {
        this.ClothNo = ClothNo;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof ContentBean))
            return false;
        ContentBean other = (ContentBean)o;
        if (!other.canEqual(this))
            return false;
        Object this$isHave = getIsHave(), other$isHave = other.getIsHave();
        if ((this$isHave == null) ? (other$isHave != null) : !this$isHave.equals(other$isHave))
            return false;
        Object this$Layer = getLayer(), other$Layer = other.getLayer();
        if ((this$Layer == null) ? (other$Layer != null) : !this$Layer.equals(other$Layer))
            return false;
        Object this$ClothNo = getClothNo(), other$ClothNo = other.getClothNo();
        return !((this$ClothNo == null) ? (other$ClothNo != null) : !this$ClothNo.equals(other$ClothNo));
    }

    protected boolean canEqual(Object other) {
        return other instanceof ContentBean;
    }

    public int hashCode() {
        int PRIME = 59, result = 1;
        Object $isHave = getIsHave();
        result = result * 59 + (($isHave == null) ? 43 : $isHave.hashCode());
        Object $Layer = getLayer();
        result = result * 59 + (($Layer == null) ? 43 : $Layer.hashCode());
        Object $ClothNo = getClothNo();
        return result * 59 + (($ClothNo == null) ? 43 : $ClothNo.hashCode());
    }

    public String toString() {
        return "ContentBean(isHave=" + getIsHave() + ", Layer=" + getLayer() + ", ClothNo=" + getClothNo() + ")";
    }

    public Integer getIsHave() {
        return this.isHave;
    }

    public Integer getLayer() {
        return this.Layer;
    }

    public String getClothNo() {
        return this.ClothNo;
    }

    public ContentBean() {}

    public ContentBean(String str) {
        if (null != str && !str.equals("")) {
            String[] list = str.split("@");
            if (null != list && list.length > 0)
                if (list.length == 2) {
                    setLayer(Integer.valueOf(Integer.parseInt(list[0])));
                    setClothNo(list[1]);
                } else {
                    setLayer(Integer.valueOf(Integer.parseInt(list[0])));
                }
        }
    }

    public static List<ContentBean> getListContent(Integer LayerQty, String strContent) {
        String strsplist = "|#|";
        List<ContentBean> list = new ArrayList<>();
        for (int i = 1; i <= LayerQty.intValue(); i++) {
            ContentBean contentBean = new ContentBean();
            contentBean.setLayer(Integer.valueOf(i));
            contentBean.setIsHave(enumYesOrNo.no.getValue());
            list.add(contentBean);
        }
        if (null == strContent || !strContent.equals(""));
        strContent = strContent.replace("|", "");
        String[] layerlist = strContent.split("#");
        if (null != layerlist && layerlist.length > 0)
            for (ContentBean contentBean : list) {
                for (String str : layerlist) {
                    if (!str.equals("") && contentBean.getLayer().toString().equals(str.substring(0, 1))) {
                        contentBean.setClothNo(getContent(str).getClothNo());
                        contentBean.setIsHave(enumYesOrNo.yes.getValue());
                        break;
                    }
                }
            }
        return list;
    }

    public static List<String> getListContentByCloth(String strContent) {
        String strsplist = "|#|";
        List<String> list = new ArrayList<>();
        if (null == strContent || !strContent.equals(""));
        strContent = strContent.replace("|", "");
        String[] layerlist = strContent.split("#");
        if (null != layerlist && layerlist.length > 0)
            for (String str : layerlist) {
                if (!str.equals("") && !str.substring(2).equals(""))
                    list.add(getContent(str).getClothNo());
            }
        return list;
    }

    public static ContentBean getContent(String str) {
        ContentBean contentBean = new ContentBean();
        if (null != str && !str.equals("")) {
            String[] list = str.split("@");
            if (null != list && list.length > 0)
                if (list.length == 2) {
                    contentBean.setLayer(Integer.valueOf(Integer.parseInt(list[0])));
                    contentBean.setClothNo(list[1]);
                } else {
                    contentBean.setLayer(Integer.valueOf(Integer.parseInt(list[0])));
                }
            return contentBean;
        }
        return contentBean;
    }
}

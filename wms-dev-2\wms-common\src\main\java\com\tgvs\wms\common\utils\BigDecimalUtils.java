package com.tgvs.wms.common.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * BigDecimal工具类
 * 提供常用的BigDecimal运算方法，确保数量计算的精确性
 */
public class BigDecimalUtils {

    /**
     * 默认精度
     */
    public static final int DEFAULT_SCALE = 2;

    /**
     * 默认舍入模式
     */
    public static final RoundingMode DEFAULT_ROUNDING_MODE = RoundingMode.HALF_UP;

    /**
     * 安全的加法运算
     * @param a 被加数
     * @param b 加数
     * @return 和
     */
    public static BigDecimal safeAdd(BigDecimal a, BigDecimal b) {
        if (a == null) a = BigDecimal.ZERO;
        if (b == null) b = BigDecimal.ZERO;
        return a.add(b);
    }

    /**
     * 安全的减法运算
     * @param a 被减数
     * @param b 减数
     * @return 差
     */
    public static BigDecimal safeSubtract(BigDecimal a, BigDecimal b) {
        if (a == null) a = BigDecimal.ZERO;
        if (b == null) b = BigDecimal.ZERO;
        return a.subtract(b);
    }

    /**
     * 安全的乘法运算
     * @param a 被乘数
     * @param b 乘数
     * @return 积
     */
    public static BigDecimal safeMultiply(BigDecimal a, BigDecimal b) {
        if (a == null) a = BigDecimal.ZERO;
        if (b == null) b = BigDecimal.ZERO;
        return a.multiply(b);
    }

    /**
     * 安全的除法运算
     * @param a 被除数
     * @param b 除数
     * @param scale 精度
     * @param roundingMode 舍入模式
     * @return 商
     */
    public static BigDecimal safeDivide(BigDecimal a, BigDecimal b, int scale, RoundingMode roundingMode) {
        if (a == null) a = BigDecimal.ZERO;
        if (b == null || b.compareTo(BigDecimal.ZERO) == 0) {
            throw new ArithmeticException("除数不能为null或零");
        }
        return a.divide(b, scale, roundingMode);
    }

    /**
     * 安全的除法运算（使用默认精度和舍入模式）
     * @param a 被除数
     * @param b 除数
     * @return 商
     */
    public static BigDecimal safeDivide(BigDecimal a, BigDecimal b) {
        return safeDivide(a, b, DEFAULT_SCALE, DEFAULT_ROUNDING_MODE);
    }

    /**
     * 安全的比较运算
     * @param a 比较数1
     * @param b 比较数2
     * @return 比较结果 (-1, 0, 1)
     */
    public static int safeCompare(BigDecimal a, BigDecimal b) {
        if (a == null) a = BigDecimal.ZERO;
        if (b == null) b = BigDecimal.ZERO;
        return a.compareTo(b);
    }

    /**
     * 判断是否相等
     * @param a 数值1
     * @param b 数值2
     * @return 是否相等
     */
    public static boolean isEqual(BigDecimal a, BigDecimal b) {
        return safeCompare(a, b) == 0;
    }

    /**
     * 判断是否大于
     * @param a 数值1
     * @param b 数值2
     * @return a > b
     */
    public static boolean isGreaterThan(BigDecimal a, BigDecimal b) {
        return safeCompare(a, b) > 0;
    }

    /**
     * 判断是否大于等于
     * @param a 数值1
     * @param b 数值2
     * @return a >= b
     */
    public static boolean isGreaterThanOrEqual(BigDecimal a, BigDecimal b) {
        return safeCompare(a, b) >= 0;
    }

    /**
     * 判断是否小于
     * @param a 数值1
     * @param b 数值2
     * @return a < b
     */
    public static boolean isLessThan(BigDecimal a, BigDecimal b) {
        return safeCompare(a, b) < 0;
    }

    /**
     * 判断是否小于等于
     * @param a 数值1
     * @param b 数值2
     * @return a <= b
     */
    public static boolean isLessThanOrEqual(BigDecimal a, BigDecimal b) {
        return safeCompare(a, b) <= 0;
    }

    /**
     * 安全转换为Integer
     * @param value BigDecimal值
     * @return Integer值
     */
    public static Integer toInteger(BigDecimal value) {
        if (value == null) {
            return 0;
        }
        return value.setScale(0, DEFAULT_ROUNDING_MODE).intValue();
    }

    /**
     * 安全转换为Long
     * @param value BigDecimal值
     * @return Long值
     */
    public static Long toLong(BigDecimal value) {
        if (value == null) {
            return 0L;
        }
        return value.setScale(0, DEFAULT_ROUNDING_MODE).longValue();
    }

    /**
     * 从Integer安全转换为BigDecimal
     * @param value Integer值
     * @return BigDecimal值
     */
    public static BigDecimal fromInteger(Integer value) {
        if (value == null) {
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(value);
    }

    /**
     * 从Long安全转换为BigDecimal
     * @param value Long值
     * @return BigDecimal值
     */
    public static BigDecimal fromLong(Long value) {
        if (value == null) {
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(value);
    }

    /**
     * 判断是否为零或null
     * @param value 数值
     * @return 是否为零或null
     */
    public static boolean isZeroOrNull(BigDecimal value) {
        return value == null || value.compareTo(BigDecimal.ZERO) == 0;
    }

    /**
     * 判断是否为正数
     * @param value 数值
     * @return 是否为正数
     */
    public static boolean isPositive(BigDecimal value) {
        return value != null && value.compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * 判断是否为负数
     * @param value 数值
     * @return 是否为负数
     */
    public static boolean isNegative(BigDecimal value) {
        return value != null && value.compareTo(BigDecimal.ZERO) < 0;
    }

    /**
     * 获取非null值
     * @param value 原值
     * @return 非null的BigDecimal值
     */
    public static BigDecimal nullToZero(BigDecimal value) {
        return value == null ? BigDecimal.ZERO : value;
    }

    /**
     * 安全获取值（别名方法，与nullToZero功能相同）
     * @param value 原值
     * @return 非null的BigDecimal值
     */
    public static BigDecimal safeValue(BigDecimal value) {
        return nullToZero(value);
    }

    /**
     * 判断是否为零
     * @param value 数值
     * @return 是否为零
     */
    public static boolean isZero(BigDecimal value) {
        return value != null && value.compareTo(BigDecimal.ZERO) == 0;
    }

    /**
     * 获取两个数中的最小值
     * @param a 数值1
     * @param b 数值2
     * @return 最小值
     */
    public static BigDecimal min(BigDecimal a, BigDecimal b) {
        if (a == null) a = BigDecimal.ZERO;
        if (b == null) b = BigDecimal.ZERO;
        return a.compareTo(b) <= 0 ? a : b;
    }

    /**
     * 获取两个数中的最大值
     * @param a 数值1
     * @param b 数值2
     * @return 最大值
     */
    public static BigDecimal max(BigDecimal a, BigDecimal b) {
        if (a == null) a = BigDecimal.ZERO;
        if (b == null) b = BigDecimal.ZERO;
        return a.compareTo(b) >= 0 ? a : b;
    }

    /**
     * 设置精度
     * @param value 原值
     * @param scale 精度
     * @param roundingMode 舍入模式
     * @return 设置精度后的值
     */
    public static BigDecimal setScale(BigDecimal value, int scale, RoundingMode roundingMode) {
        if (value == null) {
            return BigDecimal.ZERO.setScale(scale, roundingMode);
        }
        return value.setScale(scale, roundingMode);
    }

    /**
     * 设置精度（使用默认舍入模式）
     * @param value 原值
     * @param scale 精度
     * @return 设置精度后的值
     */
    public static BigDecimal setScale(BigDecimal value, int scale) {
        return setScale(value, scale, DEFAULT_ROUNDING_MODE);
    }
}

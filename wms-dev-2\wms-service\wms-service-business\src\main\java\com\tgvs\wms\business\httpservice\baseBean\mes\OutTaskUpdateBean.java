package com.tgvs.wms.business.httpservice.baseBean.mes;


import com.alibaba.fastjson.annotation.JSONField;

public class OutTaskUpdateBean {
    @J<PERSON>NField(name = "SiteNo")
    private String SiteNo;

    @J<PERSON>NField(name = "OrderNo")
    private String OrderNo;

    @JSONField(name = "OrderOperate")
    private int OrderOperate;

    @JSONField(name = "UserNo")
    private String UserNo;

    public void setSiteNo(String SiteNo) {
        this.SiteNo = SiteNo;
    }

    public void setOrderNo(String OrderNo) {
        this.OrderNo = OrderNo;
    }

    public void setOrderOperate(int OrderOperate) {
        this.OrderOperate = OrderOperate;
    }

    public void setUserNo(String UserNo) {
        this.UserNo = UserNo;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof OutTaskUpdateBean))
            return false;
        OutTaskUpdateBean other = (OutTaskUpdateBean)o;
        if (!other.canEqual(this))
            return false;
        Object this$SiteNo = getSiteNo(), other$SiteNo = other.getSiteNo();
        if ((this$SiteNo == null) ? (other$SiteNo != null) : !this$SiteNo.equals(other$SiteNo))
            return false;
        Object this$OrderNo = getOrderNo(), other$OrderNo = other.getOrderNo();
        if ((this$OrderNo == null) ? (other$OrderNo != null) : !this$OrderNo.equals(other$OrderNo))
            return false;
        if (getOrderOperate() != other.getOrderOperate())
            return false;
        Object this$UserNo = getUserNo(), other$UserNo = other.getUserNo();
        return !((this$UserNo == null) ? (other$UserNo != null) : !this$UserNo.equals(other$UserNo));
    }

    protected boolean canEqual(Object other) {
        return other instanceof OutTaskUpdateBean;
    }

    public int hashCode() {
        int PRIME = 59,
        result = 1;
        Object $SiteNo = getSiteNo();
        result = result * 59 + (($SiteNo == null) ? 43 : $SiteNo.hashCode());
        Object $OrderNo = getOrderNo();
        result = result * 59 + (($OrderNo == null) ? 43 : $OrderNo.hashCode());
        result = result * 59 + getOrderOperate();
        Object $UserNo = getUserNo();
        return result * 59 + (($UserNo == null) ? 43 : $UserNo.hashCode());
    }

    public String toString() {
        return "OutTaskUpdateBean(SiteNo=" + getSiteNo() + ", OrderNo=" + getOrderNo() + ", OrderOperate=" + getOrderOperate() + ", UserNo=" + getUserNo() + ")";
    }

    public String getSiteNo() {
        return this.SiteNo;
    }

    public String getOrderNo() {
        return this.OrderNo;
    }

    public int getOrderOperate() {
        return this.OrderOperate;
    }

    public String getUserNo() {
        return this.UserNo;
    }
}

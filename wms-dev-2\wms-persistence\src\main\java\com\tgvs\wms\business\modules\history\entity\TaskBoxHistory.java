package com.tgvs.wms.business.modules.history.entity;


import java.io.Serializable;
import java.util.Date;

import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.tgvs.wms.base.entity.BaseEntity;
import com.tgvs.wms.common.annotation.Dict;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TableName("wms_task_box_history")
@ApiModel(value = "wms_task_box_history对象", description = "作业任务历史记录")
@Data
public class TaskBoxHistory  extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键")
    private String id;

    @Excel(name = "原主键", width = 22.0D)
    @ApiModelProperty("原主键")
    private String sourceId;

    @Excel(name = "任务号", width = 15.0D)
    @ApiModelProperty("任务号")
    private Integer taskId;

    @Excel(name = "父任务号", width = 15.0D)
    @ApiModelProperty("父任务号")
    private Integer taskPid;

    @Excel(name = "源位置", width = 15.0D)
    @Dict(dictTable = "wms_point", dicText = "point_name", dicCode = "point_no")
    @ApiModelProperty("源位置")
    private String fromSite;

    @Excel(name = "途径位置", width = 15.0D)
    @ApiModelProperty("途径位置")
    private String passBy;

    @Excel(name = "目的位置", width = 15.0D)
    @Dict(dictTable = "wms_point", dicText = "point_name", dicCode = "point_no")
    @ApiModelProperty("目的位置")
    private String toSite;

    @Excel(name = "任务类型", width = 20.0D, dictTable = "wms_task_type", dicText = "text", dicCode = "value")
    @Dict(dictTable = "wms_task_type", dicText = "text", dicCode = "value")
    @ApiModelProperty("任务类型")
    private Integer type;

    @Excel(name = "途径区域", width = 15.0D)
    @ApiModelProperty("途径区域")
    private String passArea;

    @Excel(name = "源区域", width = 15.0D)
    @ApiModelProperty("源区域")
    private String fromArea;

    @Excel(name = "目的区域", width = 15.0D)
    @ApiModelProperty("目的区域")
    private String toArea;

    @Excel(name = "优先级", width = 15.0D)
    @ApiModelProperty("优先级")
    private Integer priority;

    @Excel(name = "任务锁定", width = 15.0D, dicCode = "locked_status")
    @Dict(dicCode = "locked_status")
    @ApiModelProperty("任务锁定")
    private Integer locked;

    @Excel(name = "任务状态", width = 15.0D, dictTable = "wms_task_state", dicText = "text", dicCode = "value")
    @Dict(dictTable = "wms_task_state", dicText = "text", dicCode = "value")
    @ApiModelProperty("任务状态")
    private Integer state;

    @Excel(name = "WCS执行状态", width = 15.0D, dicCode = "mfcexec_status")
    @Dict(dicCode = "mfcexec_status")
    @ApiModelProperty("WCS执行状态")
    private Integer wcsstate;

    @Excel(name = "关联容器号", width = 15.0D)
    @ApiModelProperty("关联容器号")
    private String mainBoxNo;

    @Excel(name = "容器号", width = 15.0D)
    @ApiModelProperty("容器号")
    private String boxNo;

    @Excel(name = "容器类型", width = 15.0D, dicCode = "box_type")
    @Dict(dicCode = "box_type")
    @ApiModelProperty("容器类型")
    private Integer boxType;

    @Excel(name = "空实状态", width = 15.0D, dicCode = "box_empty_status")
    @Dict(dicCode = "box_empty_status")
    @ApiModelProperty("容器空实状态")
    private Integer boxEmpty;

    @Excel(name = "容器高度", width = 15.0D, dicCode = "box_height")
    @Dict(dicCode = "box_height")
    @ApiModelProperty("容器高度")
    private Integer boxHeight;

    @Excel(name = "容器容量", width = 15.0D, dicCode = "box_volume")
    @Dict(dicCode = "box_volume")
    @ApiModelProperty("容器容量")
    private Integer boxVolume;

    @Excel(name = "排序出库", width = 20.0D)
    @ApiModelProperty("合约号")
    private String contractNo;

    @Excel(name = "单编号", width = 30.0D)
    @ApiModelProperty("订单编号")
    private String bookNo;

    @Excel(name = "分组", width = 15.0D)
    @ApiModelProperty("批次号")
    private String batch;

    @Excel(name = "存储货位", width = 15.0D)
    @ApiModelProperty("存储货位")
    private Integer location;

    @Excel(name = "货架层", width = 15.0D)
    @ApiModelProperty("存储货架层")
    private Integer level;

    @Excel(name = "货架区域", width = 15.0D)
    @ApiModelProperty("存储货架区域")
    private String shelfarea;

    @ApiModelProperty("创建人")
    private String createBy;

    @Excel(name = "创建时间", width = 20.0D, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("更新时间")
    private Date updateTime;

    @Excel(name = "结束时间", width = 20.0D, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("结束时间")
    private Date endTime;

    @ApiModelProperty("删除状态")
    private Integer delFlag;

    @ApiModelProperty("父级节点")
    private String pid;

    @Dict(dicCode = "yn")
    @ApiModelProperty("是否有子节点")
    private String hasChild;
}

package com.tgvs.wms.business.httpservice.baseBean.warehouse;

import java.util.Date;
import java.util.List;

import com.tgvs.wms.common.util.DateUtils;

public class MFCSystemStatus {
    private Integer id;

    private String messageName;

    private List<MFCSystemSub> boxlift;

    private List<MFCSystemSub> pd;

    private List<MFCSystemSub> lift;

    private Date updatetime;

    public void setId(Integer id) {
        this.id = id;
    }

    public void setMessageName(String messageName) {
        this.messageName = messageName;
    }

    public void setBoxlift(List<MFCSystemSub> boxlift) {
        this.boxlift = boxlift;
    }

    public void setPd(List<MFCSystemSub> pd) {
        this.pd = pd;
    }

    public void setLift(List<MFCSystemSub> lift) {
        this.lift = lift;
    }

    public void setUpdatetime(Date updatetime) {
        this.updatetime = updatetime;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof MFCSystemStatus))
            return false;
        MFCSystemStatus other = (MFCSystemStatus)o;
        if (!other.canEqual(this))
            return false;
        Object this$id = getId(), other$id = other.getId();
        if ((this$id == null) ? (other$id != null) : !this$id.equals(other$id))
            return false;
        Object this$messageName = getMessageName(), other$messageName = other.getMessageName();
        if ((this$messageName == null) ? (other$messageName != null) : !this$messageName.equals(other$messageName))
            return false;
        List<MFCSystemSub> this$boxlift = (List<MFCSystemSub>)getBoxlift(), other$boxlift = (List<MFCSystemSub>)other.getBoxlift();
        if ((this$boxlift == null) ? (other$boxlift != null) : !this$boxlift.equals(other$boxlift))
            return false;
        List<MFCSystemSub> this$pd = (List<MFCSystemSub>)getPd(), other$pd = (List<MFCSystemSub>)other.getPd();
        if ((this$pd == null) ? (other$pd != null) : !this$pd.equals(other$pd))
            return false;
        List<MFCSystemSub> this$lift = (List<MFCSystemSub>)getLift(), other$lift = (List<MFCSystemSub>)other.getLift();
        if ((this$lift == null) ? (other$lift != null) : !this$lift.equals(other$lift))
            return false;
        Object this$updatetime = getUpdatetime(), other$updatetime = other.getUpdatetime();
        return !((this$updatetime == null) ? (other$updatetime != null) : !this$updatetime.equals(other$updatetime));
    }

    protected boolean canEqual(Object other) {
        return other instanceof MFCSystemStatus;
    }

    public int hashCode() {
        int PRIME = 59,
        result = 1;
        Object $id = getId();
        result = result * 59 + (($id == null) ? 43 : $id.hashCode());
        Object $messageName = getMessageName();
        result = result * 59 + (($messageName == null) ? 43 : $messageName.hashCode());
        List<MFCSystemSub> $boxlift = (List<MFCSystemSub>)getBoxlift();
        result = result * 59 + (($boxlift == null) ? 43 : $boxlift.hashCode());
        List<MFCSystemSub> $pd = (List<MFCSystemSub>)getPd();
        result = result * 59 + (($pd == null) ? 43 : $pd.hashCode());
        List<MFCSystemSub> $lift = (List<MFCSystemSub>)getLift();
        result = result * 59 + (($lift == null) ? 43 : $lift.hashCode());
        Object $updatetime = getUpdatetime();
        return result * 59 + (($updatetime == null) ? 43 : $updatetime.hashCode());
    }

    public String toString() {
        return "MFCSystemStatus(id=" + getId() + ", messageName=" + getMessageName() + ", boxlift=" + getBoxlift() + ", pd=" + getPd() + ", lift=" + getLift() + ", updatetime=" + getUpdatetime() + ")";
    }

    public Integer getId() {
        return this.id;
    }

    public String getMessageName() {
        return this.messageName;
    }

    public List<MFCSystemSub> getBoxlift() {
        return this.boxlift;
    }

    public List<MFCSystemSub> getPd() {
        return this.pd;
    }

    public List<MFCSystemSub> getLift() {
        return this.lift;
    }

    public Date getUpdatetime() {
        return this.updatetime;
    }

    public boolean getDate() {
        boolean isok = false;
        if (null != this.updatetime) {
            if (new Date().getTime() - this.updatetime.getTime() > 300000L)
                isok = true;
        } else {
            isok = true;
        }
        return isok;
    }
}

package com.tgvs.wms.business.modules.history.entity;


import java.io.Serializable;
import java.util.Date;

import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.tgvs.wms.base.entity.BaseEntity;
import com.tgvs.wms.common.annotation.Dict;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TableName("wms_agv_dolly_history")
@ApiModel(value = "wms_agv_dolly_history对象", description = "松布架搬运记录")
@Data
public class AgvDollyHistory  extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键")
    private String id;

    @Excel(name = "松布架号", width = 15.0D)
    @ApiModelProperty("松布架号")
    private String dollyNo;

    @Excel(name = "站点编码", width = 15.0D)
    @ApiModelProperty("站点编码")
    private String siteCode;

    @Excel(name = "松布架层数", width = 15.0D)
    @ApiModelProperty("松布架层数")
    private Integer dollyLayers;

    @Excel(name = "松布架类型", width = 15.0D, dicCode = "dolly_type")
    @Dict(dicCode = "dolly_type")
    @ApiModelProperty("松布架类型")
    private Integer dollyType;

    @Excel(name = "区域", width = 15.0D)
    @ApiModelProperty("区域")
    private String area;

    @Excel(name = "调度类型", width = 15.0D, dictTable = "wms_task_agv_type", dicText = "text", dicCode = "value")
    @Dict(dictTable = "wms_task_agv_type", dicText = "text", dicCode = "value")
    @ApiModelProperty("调度类型")
    private Integer dispatchType;

    @Excel(name = "方向", width = 15.0D, dicCode = "bound_site")
    @Dict(dicCode = "bound_site")
    @ApiModelProperty("方向")
    private Integer priority;

    @Excel(name = "内容信息", width = 15.0D)
    @ApiModelProperty("内容信息")
    private String content;

    @Excel(name = "余布标记", width = 15.0D)
    @ApiModelProperty("余布标记")
    private Integer scrapFlag;

    @ApiModelProperty("创建人")
    private String createBy;

    @Excel(name = "创建时间", width = 15.0D, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @Excel(name = "退回时间", width = 15.0D, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("更新时间")
    private Date updateTime;

    @Excel(name = "删除状态", width = 15.0D)
    @ApiModelProperty("删除状态")
    private Integer delFlag;
}

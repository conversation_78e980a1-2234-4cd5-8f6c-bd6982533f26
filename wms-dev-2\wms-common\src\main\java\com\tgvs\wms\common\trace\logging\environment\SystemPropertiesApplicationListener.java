package com.tgvs.wms.common.trace.logging.environment;

import org.springframework.boot.context.event.ApplicationEnvironmentPreparedEvent;
import org.springframework.boot.context.logging.LoggingApplicationListener;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.event.GenericApplicationListener;
import org.springframework.core.ResolvableType;

public class SystemPropertiesApplicationListener implements GenericApplicationListener {
    public static final int DEFAULT_ORDER = LoggingApplicationListener.DEFAULT_ORDER -1;

    public static final String SYSTEM_ID_PROPERTY = "SYSTEM_ID";
    public static final String SYSTEM_ID_CONFIG_PROPERTY = "wms.system.id";

    public static final String LOG_FORMAT_PROPERTY = "LOG_FORMAT";
    public static final String LOG_FORMAT_CONFIG_PROPERTY = "wms.logging.format";

    public static final String LOG_FILE_PREFIX_NAME = "LOG_FILE_PREFIX_NAME";

    @Override
    public boolean supportsEventType(ResolvableType eventType) {
        Class<?> type = eventType.getRawClass();
        if(type == null){
            return false;
        }
        return ApplicationEnvironmentPreparedEvent.class.isAssignableFrom(type);
    }

    @Override
    public void onApplicationEvent(ApplicationEvent applicationEvent) {
        if(applicationEvent instanceof ApplicationEnvironmentPreparedEvent){
            ApplicationEnvironmentPreparedEvent event = (ApplicationEnvironmentPreparedEvent)applicationEvent;

            String systemId = System.getProperty(SYSTEM_ID_PROPERTY);
            if(systemId == null || systemId.trim().equals("")){
                systemId = event.getEnvironment().getProperty(SYSTEM_ID_CONFIG_PROPERTY);

                if(systemId != null && !systemId.trim().equals("")){
                    System.setProperty(SYSTEM_ID_PROPERTY, systemId);
                }
            }
            if(systemId != null){
                System.setProperty(LOG_FILE_PREFIX_NAME, systemId);
            }

            String logFormat = System.getProperty(LOG_FORMAT_PROPERTY);
            if(logFormat == null || logFormat.trim().equals("")){
                logFormat = event.getEnvironment().getProperty(LOG_FORMAT_CONFIG_PROPERTY);
                if(logFormat != null && !logFormat.trim().equals("")){
                    System.setProperty(LOG_FORMAT_PROPERTY, logFormat);
                }
            }
        }
    }

    @Override
    public int getOrder() {
        return DEFAULT_ORDER;
    }
}

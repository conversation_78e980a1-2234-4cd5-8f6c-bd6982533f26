package com.tgvs.wms.business.modules.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tgvs.wms.base.entity.BaseEntity;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.io.Serializable;
import lombok.Data;
import java.util.Date;


@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName ( "sys_log" )
@Data
public class SysLog extends BaseEntity implements Serializable {

	private static final long serialVersionUID =  6453415860730361773L;

	/**
	 * 日志类型（1登录日志，2操作日志,，3接口日志）
	 */
   	@TableField(value = "log_type")
	private Integer logType;

	/**
	 * 日志内容
	 */
   	@TableField(value = "log_content")
	private String logContent;

	/**
	 * 操作类型
	 */
   	@TableField(value = "operate_type")
	private String operateType;

	/**
	 * 操作用户账号
	 */
   	@TableField(value = "userid")
	private String userid;

	/**
	 * 操作用户名称
	 */
   	@TableField(value = "username")
	private String username;

	/**
	 * IP
	 */
   	@TableField(value = "ip")
	private String ip;

	/**
	 * 请求java方法
	 */
   	@TableField(value = "method")
	private String method;

	/**
	 * 请求路径
	 */
   	@TableField(value = "request_url")
	private String requestUrl;

	/**
	 * 请求参数
	 */
   	@TableField(value = "request_param")
	private String requestParam;

	/**
	 * 请求类型
	 */
   	@TableField(value = "request_type")
	private String requestType;

	/**
	 * 请求结果：1.成功；2.失败
	 */
	@TableField(value = "request_result")
	private Integer requestResult;

	/**
	 * 请求返回消息
	 */
	@TableField(value = "result_msg")
	private String resultMsg;

	/**
	 * 耗时
	 */
   	@TableField(value = "cost_time")
	private Long costTime;

	/**
	 * 1.OA系统；2.RCS系统；3.i9系统
	 */
	@TableField(value = "system_type")
	private Integer systemType;
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tgvs.wms.business.modules.machineAuxiliary.mapper.WmsAuxiliaryOutboundMapper">

    <!-- AuxiliaryOutListDto 的 ResultMap -->
    <resultMap id="AuxiliaryOutListDtoMap" type="com.tgvs.wms.business.modules.machineAuxiliary.dto.AuxiliaryOutListDto">
        <!-- WmsAuxiliaryOutBound 父类字段 -->
        <result column="outbound_id" property="id"/>
        <result column="out_store_number" property="outStoreNumber"/>
        <result column="out_type" property="outType"/>
        <result column="status" property="status"/>
        <result column="delete_flag" property="deleteFlag"/>
        <result column="remarks" property="remarks"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>

        <!-- AuxiliaryOutListDto 特有字段 -->
        <result column="out_store_number" property="refNumber"/>
        <result column="operation_type" property="operationType"/>
        <result column="contract_no" property="contractNo"/>
        <result column="item_no" property="itemNo"/>
        <result column="req_list_id" property="reqListId"/>
        <result column="material_code" property="materialCode"/>
        <result column="material_name" property="materialName"/>
        <result column="material_color" property="materialColor"/>
        <result column="material_color_code" property="materialColorCode"/>
        <result column="material_model" property="materialModel"/>
        <result column="quantity" property="quantity"/>
        <result column="material_unit" property="materialUnit"/>
        <result column="priority" property="priority"/>
        <result column="material_type" property="materialType"/>
        <!-- 新增PO号字段支持 -->
        <result column="po_number" property="poNumber"/>
        <!-- 明细预装状态字段映射 -->
        <result column="detailStatus" property="detailStatus"/>
        <result column="isSmartWarehouse" property="isSmartWarehouse"/>
        <result column="actual_quantity" property="actualQuantity"/>
        
        <!-- wms_box_item 物料明细字段映射（当materialType=1时使用） -->
        <result column="boxItemId" property="boxItemId"/>
        <result column="box_no" property="boxNo"/>
        <result column="grid_id" property="gridId"/>
        <result column="material_quantity" property="materialQuantity"/>
        <result column="material_size" property="materialSize"/>
        <result column="box_type" property="boxType"/>
        <result column="grid_status" property="gridStatus"/>
        <result column="material_property" property="materialProperty"/>
    </resultMap>

        <!-- 分页查询辅料出库信息（直接返回DTO） - 优化关联逻辑确保数据完整性 -->
    <select id="selectPage" resultMap="AuxiliaryOutListDtoMap">
        SELECT
        a.id as outbound_id,
        a.out_store_number,
        a.out_type,
        a.status,
        a.delete_flag,
        a.remarks,
        a.create_by,
        a.create_time,
        a.update_by,
        a.update_time,
        d.id as detail_id,
        d.operation_type,
        d.contract_no,
        d.item_no,
        d.req_list_id,
        d.material_code,
        d.material_name,
        d.material_color,
        d.material_color_code,
        d.material_model,
        d.quantity,
        d.material_unit,
        d.priority,
        d.prebox_status as detailStatus,
        ai.material_type,
        ai.is_store as isSmartWarehouse,
        bi.contract_no_po as po_number,
        -- wms_box_item 物料明细字段（只对materialType=1的材料有效）
        bi.Id as boxItemId,
        bi.box_no,
        bi.grid_id,
        bi.material_quantity,
        bi.material_size,
        bi.box_type,
        bi.grid_status,
        bi.material_property
        FROM wms_auxiliary_outbound a
        INNER JOIN wms_auxiliary_detail d ON a.out_store_number = d.ref_number AND d.operation_type = 1
        LEFT JOIN wms_auxiliary_info ai ON d.material_code = ai.material_code
        LEFT JOIN wms_box_item bi ON (
            d.contract_no = bi.contract_no 
            AND d.material_code = bi.material_code
            AND (d.item_no IS NULL OR d.item_no = '' OR bi.item_no = d.item_no)
            AND (d.material_color IS NULL OR d.material_color = '' OR bi.material_color = d.material_color)
            AND (d.material_model IS NULL OR d.material_model = '' OR bi.material_model = d.material_model)
            AND ai.material_type = 1
            AND bi.delete_flag = 0
        )
        <where>
            a.delete_flag = 0 and d.delete_flag = 0 and d.prebox_status in(0,1)
            <if test="queryModel.searchParams.materialType != null">
                AND ai.material_type = #{queryModel.searchParams.materialType}
            </if>
            <if test="queryModel.searchParams.outStoreNumber != null and queryModel.searchParams.outStoreNumber != ''">
                AND a.out_store_number LIKE CONCAT('%', #{queryModel.searchParams.outStoreNumber}, '%')
            </if>
            <if test="queryModel.searchParams.outType != null">
                AND a.out_type = #{queryModel.searchParams.outType}
            </if>
            <if test="queryModel.searchParams.status != null">
                AND a.status = #{queryModel.searchParams.status}
            </if>
            <if test="queryModel.searchParams.contractNo != null and queryModel.searchParams.contractNo != ''">
                AND d.contract_no LIKE CONCAT('%', #{queryModel.searchParams.contractNo}, '%')
            </if>
            <if test="queryModel.searchParams.itemNo != null and queryModel.searchParams.itemNo != ''">
                AND d.item_no LIKE CONCAT('%', #{queryModel.searchParams.itemNo}, '%')
            </if>
            <if test="queryModel.searchParams.materialCode != null and queryModel.searchParams.materialCode != ''">
                AND d.material_code LIKE CONCAT('%', #{queryModel.searchParams.materialCode}, '%')
            </if>
            <if test="queryModel.searchParams.materialName != null and queryModel.searchParams.materialName != ''">
                AND d.material_name LIKE CONCAT('%', #{queryModel.searchParams.materialName}, '%')
            </if>
            <if test="queryModel.searchParams.materialModel != null and queryModel.searchParams.materialModel != ''">
                AND d.material_model LIKE CONCAT('%', #{queryModel.searchParams.materialModel}, '%')
            </if>
            <if test="queryModel.searchParams.materialColor != null and queryModel.searchParams.materialColor != ''">
                AND d.material_color LIKE CONCAT('%', #{queryModel.searchParams.materialColor}, '%')
            </if>
        </where>
        <if test="queryModel.sortList != null and queryModel.sortList.size() > 0">
            ORDER BY
            <foreach collection="queryModel.sortList" item="sort" separator=",">
                <choose>
                    <when test="sort.column == 'createTime'">a.create_time</when>
                    <when test="sort.column == 'updateTime'">a.update_time</when>
                    <when test="sort.column == 'outStoreNumber'">a.out_store_number</when>
                    <when test="sort.column == 'status'">a.status</when>
                    <when test="sort.column == 'materialCode'">d.material_code</when>
                    <when test="sort.column == 'materialName'">d.material_name</when>
                    <when test="sort.column == 'quantity'">d.quantity</when>
                    <otherwise>a.create_time</otherwise>
                </choose>
                <choose>
                    <when test="sort.sortType == 1">ASC</when>
                    <otherwise>DESC</otherwise>
                </choose>
            </foreach>
        </if>
        <if test="queryModel.sortList == null or queryModel.sortList.size() == 0">
            ORDER BY a.create_time DESC
        </if>
    </select>

    <!-- 分页查询辅料出库信息（返回实体类） -->
    <select id="selectPageEntity" resultType="com.tgvs.wms.business.modules.machineAuxiliary.entity.WmsAuxiliaryOutBound">
        SELECT 
            a.*,
            b.category,
            b.unit,
            b.capacity,
            b.priority_container_type,
            b.material_type,
            b.is_store
        FROM wms_auxiliary_outbound a
        INNER JOIN wms_auxiliary_detail d on a.out_store_number=d.ref_number
        LEFT JOIN wms_auxiliary_info b ON d.material_code = b.material_code
        <where>
            <if test="queryModel.searchParams.outStoreNumber != null and queryModel.searchParams.outStoreNumber != ''">
                AND a.out_store_number LIKE CONCAT('%', #{queryModel.searchParams.outStoreNumber}, '%')
            </if>
            <if test="queryModel.searchParams.contractNo != null and queryModel.searchParams.contractNo != ''">
                AND a.contract_no LIKE CONCAT('%', #{queryModel.searchParams.contractNo}, '%')
            </if>
            <if test="queryModel.searchParams.outType != null">
                AND a.out_type = #{queryModel.searchParams.outType}
            </if>
            <if test="queryModel.searchParams.itemNo != null and queryModel.searchParams.itemNo != ''">
                AND d.item_no LIKE CONCAT('%', #{queryModel.searchParams.itemNo}, '%')
            </if>
            <if test="queryModel.searchParams.materialName != null and queryModel.searchParams.materialName != ''">
                AND d.material_name LIKE CONCAT('%', #{queryModel.searchParams.materialName}, '%')
            </if>
            <if test="queryModel.searchParams.materialCode != null and queryModel.searchParams.materialCode != ''">
                AND d.material_code LIKE CONCAT('%', #{queryModel.searchParams.materialCode}, '%')
            </if>
            <if test="queryModel.searchParams.materialModel != null and queryModel.searchParams.materialModel != ''">
                AND d.material_model LIKE CONCAT('%', #{queryModel.searchParams.materialModel}, '%')
            </if>
            <if test="queryModel.searchParams.materialColor != null and queryModel.searchParams.materialColor != ''">
                AND d.material_color LIKE CONCAT('%', #{queryModel.searchParams.materialColor}, '%')
            </if>
            <if test="queryModel.searchParams.materialType != null">
                AND b.material_type = #{queryModel.searchParams.materialType}
            </if>
        </where>
        ORDER BY a.create_time DESC
    </select>

    <!-- 历史查询DTO的ResultMap -->
    <resultMap id="AuxiliaryOutBoundHistoryDtoMap" type="com.tgvs.wms.business.modules.machineAuxiliary.dto.AuxiliaryOutBoundHistoryDto">
        <result column="outStoreNumber" property="outStoreNumber"/>
        <result column="outType" property="outType"/>
        <result column="status" property="status"/>
        <result column="remarks" property="remarks"/>
        <result column="createBy" property="createBy"/>
        <result column="createTime" property="createTime"/>
        <result column="updateBy" property="updateBy"/>
        <result column="updateTime" property="updateTime"/>
        <result column="deleteFlag" property="deleteFlag"/>
        <result column="id" property="id"/>
        <result column="operationType" property="operationType"/>
        <result column="contractNo" property="contractNo"/>
        <result column="itemNo" property="itemNo"/>
        <result column="reqListId" property="reqListId"/>
        <result column="materialCode" property="materialCode"/>
        <result column="materialName" property="materialName"/>
        <result column="materialColor" property="materialColor"/>
        <result column="materialColorCode" property="materialColorCode"/>
        <result column="materialModel" property="materialModel"/>
        <result column="quantity" property="quantity"/>
        <result column="materialUnit" property="materialUnit"/>
        <result column="priority" property="priority"/>
        <result column="materialType" property="materialType"/>
        <result column="isStore" property="isStore"/>
        <result column="actualOutboundQty" property="actualOutboundQty"/>
        <result column="itemNo" property="itemNo"/>
        <result column="taskStartTime" property="taskStartTime"/>
        <result column="taskEndTime" property="taskEndTime"/>
        <result column="taskOrder" property="taskOrder"/>
        <result column="containerType" property="containerType"/>
    </resultMap>

    <!-- 辅料出库历史查询（关联任务表） - 根据用户提供的SQL进行优化 -->
    <select id="selectHistoryDtoPage" resultMap="AuxiliaryOutBoundHistoryDtoMap">
        SELECT
            outStoreNumber, outType, remarks, createBy, createTime, updateBy, updateTime,
            deleteFlag, id, operationType, contractNo, itemNo, reqListId, materialCode,
            materialName, materialColor, materialColorCode, materialModel, quantity,
            materialUnit, priority, status, materialType, isStore, actualOutboundQty,
            taskStartTime, taskEndTime, taskOrder, boxNo, containerType
        FROM (
            SELECT
                a.out_store_number AS outStoreNumber,
                a.out_type AS outType,
                a.remarks AS remarks,
                a.create_by AS createBy,
                a.create_time AS createTime,
                a.update_by AS updateBy,
                a.update_time AS updateTime,
                a.delete_flag AS deleteFlag,
                d.id AS id,
                d.operation_type AS operationType,
                d.contract_no AS contractNo,
                d.item_no AS itemNo,
                d.req_list_id AS reqListId,
                d.material_code AS materialCode,
                d.material_name AS materialName,
                d.material_color AS materialColor,
                d.material_color_code AS materialColorCode,
                d.material_model AS materialModel,
                d.quantity AS quantity,
                d.material_unit AS materialUnit,
                d.priority AS priority,
                d.prebox_status AS status,
                i.material_type AS materialType,
                i.is_store AS isStore,
                x.actual_quantity AS actualOutboundQty,
                t.create_time AS taskStartTime,
                t.update_time AS taskEndTime,
                t.task_order AS taskOrder,
                x.box_no AS boxNo,
                x.container_type AS containerType,
                ROW_NUMBER() OVER (
                    PARTITION BY d.id
                    ORDER BY COALESCE(x.create_time, '1900-01-01 00:00:00') DESC,
                             COALESCE(t.create_time, '1900-01-01 00:00:00') DESC
                ) as rn
            FROM
                wms_auxiliary_outbound a
                INNER JOIN wms_auxiliary_detail d ON a.out_store_number = d.ref_number
                INNER JOIN wms_auxiliary_outprebox x ON a.id = x.outbound_id AND d.ref_number = x.outbound_no AND d.material_code = x.material_code
                LEFT JOIN wms_auxiliary_info i ON x.material_code = i.material_code
                LEFT JOIN wms_box_task_list t ON x.task_order = t.task_order AND x.box_no = t.box_no
                ${ew.customSqlSegment}
        ) ranked_data
        WHERE
            rn = 1
    </select>

</mapper>
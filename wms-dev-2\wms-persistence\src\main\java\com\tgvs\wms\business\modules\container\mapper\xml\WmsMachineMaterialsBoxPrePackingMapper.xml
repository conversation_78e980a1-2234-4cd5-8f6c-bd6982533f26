<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tgvs.wms.business.modules.container.mapper.WmsMachineMaterialsBoxPrePackingMapper">
    <!--   根据机物料编码获取单个机物料库存信息-->
    <select id="selectMaterialCode" resultType="com.tgvs.wms.business.modules.container.entity.WmsMachineMaterialsBoxPrePacking">
        select *
        from wms_machinematerials_box_pre_packing
        <where>
            <if test="containerType != null and containerType != ''">
                AND container_type = #{containerType}
            </if>
            <if test="materialCode != null and materialCode != ''">
                AND material_code = #{materialCode}
            </if>
        </where>
        AND delete_flag=0
        AND status &lt; 2
        order by create_time desc
    </select>
</mapper>
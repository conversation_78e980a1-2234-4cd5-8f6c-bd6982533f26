package com.tgvs.wms.business.modules.production.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tgvs.wms.base.entity.BaseEntity;
import com.tgvs.wms.common.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.jeecgframework.poi.excel.annotation.Excel;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 生产计划物料实体类
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("wms_production_plan_material")
@ApiModel(value = "wms_production_plan_material对象", description = "生产计划物料")
public class ProductionPlanMaterial extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 计划编号
     */
    @Excel(name = "计划编号", width = 15.0D)
    @ApiModelProperty("计划编号")
    @TableField("plan_no")
    private String planNo;

    /**
     * 计划日期
     */
    @Excel(name = "计划日期", width = 15.0D, format = "yyyy-MM-dd")
    @ApiModelProperty("计划日期")
    @TableField("plan_date")
    private Date planDate;

@ApiModelProperty("合约号")
@NotBlank(message = "物料编码不能为空")
private String contractNo;

@ApiModelProperty("款号")
@NotBlank(message = "物料编码不能为空")
private String itemNo;

    /**
     * 物料编码
     */
    @Excel(name = "物料编码", width = 15.0D)
    @ApiModelProperty("物料编码")
    @TableField("material_code")
    private String materialCode;

    /**
     * 物料名称
     */
    @Excel(name = "物料名称", width = 20.0D)
    @ApiModelProperty("物料名称")
    @TableField("material_name")
    private String materialName;

@ApiModelProperty("物料颜色")
private String materialColor;

@ApiModelProperty("物料颜色编码")
private String materialColorCode;

@ApiModelProperty("物料规格/型号")
private String materialModel;

private String materialSize;

    /**
     * 需求数量
     */
    @Excel(name = "需求数量", width = 15.0D)
    @ApiModelProperty("需求数量")
    @TableField("required_quantity")
    private BigDecimal requiredQuantity;

    /**
     * 优先级（数值越大优先级越高）
     */
    @Excel(name = "优先级", width = 10.0D)
    @ApiModelProperty("优先级（数值越大优先级越高）")
    @TableField("priority")
    private Integer priority;

    /**
     * 状态：0-待处理，1-已处理，2-移库中，3-移库完成
     */
    @Excel(name = "状态", width = 10.0D, dicCode = "plan_material_status")
    @Dict(dicCode = "plan_material_status")
    @ApiModelProperty("状态：0-待处理，1-已处理，2-移库中，3-移库完成")
    @TableField("status")
    private Integer status;

    /**
     * 是否为热门物料：0-否，1-是
     */
    @Excel(name = "是否热门", width = 10.0D, dicCode = "yes_no")
    @Dict(dicCode = "yes_no")
    @ApiModelProperty("是否为热门物料：0-否，1-是")
    @TableField("is_hot")
    private Integer isHot;

    /**
     * 移库批次号
     */
    @ApiModelProperty("移库批次号")
    @TableField("relocation_batch_no")
    private String relocationBatchNo;

    /**
     * 备注
     */
    @Excel(name = "备注", width = 30.0D)
    @ApiModelProperty("备注")
    @TableField("remark")
    private String remark;

    /**
     * 删除标志：0-正常，1-删除
     */
    @ApiModelProperty("删除标志：0-正常，1-删除")
    @TableField("delete_flag")
    private Integer deleteFlag;
}
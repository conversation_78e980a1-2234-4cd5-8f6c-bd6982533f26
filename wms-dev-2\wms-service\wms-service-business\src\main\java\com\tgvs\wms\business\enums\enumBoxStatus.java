package com.tgvs.wms.business.enums;

import com.tgvs.wms.common.util.oConvertUtils;

public enum enumBoxStatus {

    normal(Integer.valueOf(1), "true", "是"),
    free(Integer.valueOf(0), "false", "否");

    private Integer value;

    private String code;

    private String text;

    enumBoxStatus(Integer value, String code, String text) {
        this.value = value;
        this.code = code;
        this.text = text;
    }

    public static enumBoxStatus getByValue(int value) {
        if (oConvertUtils.isEmpty(Integer.valueOf(value)))
            return null;
        for (enumBoxStatus val : values()) {
            if (val.getValue().equals(Integer.valueOf(value)))
                return val;
        }
        return null;
    }

    public Integer getValue() {
        return this.value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getText() {
        return this.text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public static enumBoxStatus toEnum(Integer Value) {
        for (enumBoxStatus e : values()) {
            if (e.getValue().equals(Value))
                return e;
        }
        return null;
    }
}

package com.tgvs.wms.business.modules.bd.service.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tgvs.wms.base.service.impl.BaseServiceImpl;
import com.tgvs.wms.business.modules.bd.entity.Point;
import com.tgvs.wms.business.modules.bd.entity.PointRouter;
import com.tgvs.wms.business.modules.bd.mapper.PointMapper;
import com.tgvs.wms.business.modules.bd.mapper.PointRouterMapper;
import com.tgvs.wms.business.modules.bd.service.IPointRouterService;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class PointRouterServiceImpl extends BaseServiceImpl<PointRouterMapper, PointRouter> implements IPointRouterService {

    @Resource
    public PointMapper pointMapper;

    public Point getPointByNo(String pointno) {
        log.info("pointno======:", pointno);
        return this.pointMapper.getPointByNo(pointno);
    }
}

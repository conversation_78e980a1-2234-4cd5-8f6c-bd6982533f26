package com.tgvs.wms.business.httpservice.baseBean.agv;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

/**
 * 海康AGV预调度响应实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PreTaskResponse extends AgvBaseResponse {

    /**
     * 业务数据
     */
    private PreTaskResponseData data;

    /**
     * 预调度接口响应数据
     */
    @Data
    public static class PreTaskResponseData {
        
        /**
         * 站点编码
         */
        private String siteCode;
        
        /**
         * 机器人编号
         */
        private String robotCode;
        
        /**
         * 预计到达时间（秒）
         */
        private Integer estimatedArrivalTime;
        
        /**
         * 自定义扩展字段
         */
        private Map<String, Object> extra;
    }
} 
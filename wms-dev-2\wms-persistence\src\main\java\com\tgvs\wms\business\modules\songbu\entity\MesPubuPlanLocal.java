package com.tgvs.wms.business.modules.songbu.entity;

import java.io.Serializable;
import java.util.Date;

import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.tgvs.wms.common.annotation.Dict;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TableName("mes_pubu_plan")
@ApiModel(value = "mes_pubu_plan对象", description = "铺床计划主表")
@Data
public class MesPubuPlanLocal implements Serializable {
    private static final long serialVersionUID = 1L;

    @Excel(name = "拉布单号", width = 15.0D)
    @ApiModelProperty("拉布单号")
    private String billNo;

    @Excel(name = "对应铺床", width = 15.0D)
    @ApiModelProperty("对应铺床")
    private String latheNo;

    @Excel(name = "布匹总数", width = 15.0D)
    @ApiModelProperty("布匹总数")
    private Integer clothCount;

    @Excel(name = "合约号", width = 15.0D)
    @ApiModelProperty("合约号")
    private String contractNo;

    @Excel(name = "铺布计划", width = 15.0D)
    @ApiModelProperty("铺布计划")
    private String pubuPlan;

    @Excel(name = "松布计划日期", width = 20.0D, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("松布计划日期")
    private Date planTime;

    @Excel(name = "发布更新日期", width = 20.0D, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("发布更新日期")
    private Date publishTime;

    @Excel(name = "优先级", width = 15.0D)
    @ApiModelProperty("优先级")
    private Integer priority;

    @Excel(name = "计划执行顺序号", width = 15.0D)
    @ApiModelProperty("计划执行顺序号")
    private Integer planSort;

    @Excel(name = "允许备货", width = 15.0D, dicCode = "yn")
    @Dict(dicCode = "yn")
    @ApiModelProperty("允许备货")
    private Integer allow;
}

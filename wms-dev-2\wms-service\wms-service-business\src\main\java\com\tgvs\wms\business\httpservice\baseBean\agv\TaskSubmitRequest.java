package com.tgvs.wms.business.httpservice.baseBean.agv;


import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 海康AGV任务下发请求实体类
 */
@Data
public class TaskSubmitRequest {

    /**
     * 任务类型
     * 预制枚举值: TRANSPORT (搬运,一至数个执行步骤)
     * 也支持: A2, A3 等自定义类型
     */
    private String taskType;
    
    /**
     * 执行步骤集合
     * 本次任务机器人需要执行的关键路径,如货架起点至出库工作台B
     */
    private List<TargetRoute> targetRoute;
    
    /**
     * 任务执行的初始优先顺序
     * 数值越大,优先级越高
     * 范围:1~120
     */
    private Integer initPriority;
    
    /**
     * 任务截止时间
     * 格式:秒精度 (例如: 2023-01-01T12:00:00+08:00)
     */
    private String deadline;
    
    /**
     * 要求调度系统仅在当前指定的范围内选择机器人执行该任务
     * 固定枚举值: GROUPS (机器人资源组编号), ROBOTS (机器人编号)
     */
    private String robotType;
    
    /**
     * 与robotType匹配的资源类型唯一标识
     * 支持单个和多个编号
     */
    private List<String> robotCode;
    
    /**
     * 能否打断
     * 1:可打断 (该货架中途有其他任务时,打断当前任务)
     * 0:不可打断 (该货架中途有其他任务时,不能打断当前任务)
     * 默认不可打断
     */
    private Integer interrupt;
    
    /**
     * 外部任务唯一编号
     * 如果为空,系统生成任务号并返回
     */
    private String robotTaskCode;
    
    /**
     * 任务组编号,全局唯一
     */
    private String groupCode;
    
    /**
     * 自定义扩展字段
     * 可随业务的差异而传入不同的扩展内容
     */
    private Map<String, Object> extra;
    
    /**
     * 目标路径
     */
    @Data
    public static class TargetRoute {
        
        /**
         * 目标路径序列。从0开始
         */
        private Integer seq;
        
        /**
         * 目标类型
         * 预制枚举值包括：ZONE (目标所处区域编号), SITE (站点别名), STORAGE (存储位置)等
         */
        private String type;
        
        /**
         * 与type对应的目标编号
         */
        private String code;
        
        /**
         * 自动开始标志
         * 1:自动开始
         * 0:不自动开始
         */
        private Integer autoStart;
        
        /**
         * 机器人到达目标位置后的操作
         * 预制枚举值: COLLECT (取货), DELIVERY (送货), ROTATE (旋转)
         */
        private String operation;
        
        /**
         * 要求调度系统仅在当前指定的范围内选择机器人执行该步骤
         * 固定枚举值: GROUPS (机器人资源组编号), ROBOTS (机器人编号)
         */
        private String robotType;
        
        /**
         * 与robotType匹配的资源类型唯一标识
         */
        private List<String> robotCode;
        
        /**
         * 自定义扩展字段
         */
        private Map<String, Object> extra;
        
        /**
         * 角度信息
         */
        private AngleInfo angleInfo;
        
        /**
         * 载具信息
         */
        private List<CarrierInfo> carrierInfo;
    }
    
    /**
     * 角度信息
     */
    @Data
    public static class AngleInfo {
        
        /**
         * 角度类型
         * 预制枚举值: ABSOLUTE (绝对角度)
         */
        private String type;
        
        /**
         * 角度值
         * 例如 "90"
         */
        private String code;
    }
    
    /**
     * 载具信息
     */
    @Data
    public static class CarrierInfo {
        
        /**
         * 载具类型
         */
        private String carrierType;
        
        /**
         * 载具编号
         */
        private String carrierCode;
        
        /**
         * 层号:从0开始、从下往上编号
         */
        private Integer layer;
    }
} 
package com.tgvs.wms.business.modules.storage.entity;

import java.io.Serializable;
import java.util.Date;

import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.tgvs.wms.common.annotation.Dict;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TableName("wms_dps_location")
@ApiModel(value = "wms_dps_location对象", description = "电子标签货位")
@Data
public class DpsLocation implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键")
    private String id;

    @Excel(name = "所属系统", width = 15.0D)
    @ApiModelProperty("所属系统")
    private String sysid;

    @Excel(name = "站点编码", width = 15.0D, dictTable = "wms_point", dicText = "point_name", dicCode = "point_no")
    @Dict(dictTable = "wms_point", dicText = "point_name", dicCode = "point_no")
    @ApiModelProperty("站点编码")
    private String siteCode;

    @Excel(name = "控制器", width = 15.0D, dictTable = "wms_dps_controllerinfo", dicText = "description", dicCode = "controllerid")
    @Dict(dictTable = "wms_dps_controllerinfo", dicText = "description", dicCode = "controllerid")
    @ApiModelProperty("控制器")
    private Integer controllerid;

    @Excel(name = "货位", width = 15.0D)
    @ApiModelProperty("货位")
    private String location;

    @Excel(name = "区域", width = 15.0D, dictTable = "wms_base_area", dicText = "text", dicCode = "code")
    @Dict(dictTable = "wms_base_area", dicText = "text", dicCode = "code")
    @ApiModelProperty("区域")
    private String area;

    @Excel(name = "花片编号", width = 15.0D)
    @ApiModelProperty("花片编号")
    private String pieceNo;

    @Excel(name = "标签地址", width = 15.0D)
    @ApiModelProperty("标签地址")
    private String address;

    @Excel(name = "总线地址", width = 15.0D)
    @ApiModelProperty("总线地址")
    private Integer profibus;

    @Excel(name = "信号灯地址", width = 15.0D)
    @ApiModelProperty("信号灯地址")
    private String light;

    @Excel(name = "实际寻址", width = 15.0D)
    @ApiModelProperty("实际寻址")
    private Integer ioSort;

    @Excel(name = "扫码枪地址", width = 15.0D)
    @ApiModelProperty("扫码枪地址")
    private String scaner;

    @Excel(name = "层位", width = 15.0D)
    @ApiModelProperty("层位")
    private Integer level;

    @Excel(name = "行位", width = 15.0D)
    @ApiModelProperty("行位")
    private Integer rowNum;

    @Excel(name = "列位", width = 15.0D)
    @ApiModelProperty("列位")
    private Integer columnNumber;

    @Excel(name = "楼层", width = 15.0D)
    @ApiModelProperty("楼层")
    private Integer floor;

    @Excel(name = "优先级", width = 15.0D)
    @ApiModelProperty("优先级")
    private Integer priority;

    @Excel(name = "货位状态", width = 15.0D, dicCode = "location_status")
    @Dict(dicCode = "location_status")
    @ApiModelProperty("货位状态")
    private Integer state;

    @Excel(name = "锁定状态", width = 15.0D, dicCode = "locked_status")
    @Dict(dicCode = "locked_status")
    @ApiModelProperty("锁定状态")
    private Integer locked;

    @Excel(name = "货位类型", width = 15.0D, dicCode = "location_type")
    @Dict(dicCode = "location_type")
    @ApiModelProperty("货位类型")
    private Integer type;

    @Excel(name = "物料编码", width = 15.0D)
    @ApiModelProperty("物料编码")
    private String goodsCode;

    @Excel(name = "订单编号", width = 15.0D)
    @ApiModelProperty("订单编号")
    private String orderCode;

    @Excel(name = "工单号", width = 15.0D)
    @ApiModelProperty("工单号")
    private String workNo;

    @ApiModelProperty("创建人")
    private String createBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("更新时间")
    private Date updateTime;

    @Excel(name = "删除状态", width = 15.0D)
    @ApiModelProperty("删除状态")
    private Integer delFlag;
}

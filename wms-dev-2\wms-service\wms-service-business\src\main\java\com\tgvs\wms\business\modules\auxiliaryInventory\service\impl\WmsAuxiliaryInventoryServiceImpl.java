package com.tgvs.wms.business.modules.auxiliaryInventory.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tgvs.wms.business.modules.auxiliaryInventory.entity.WmsAuxiliaryInventory;
import com.tgvs.wms.business.modules.auxiliaryInventory.entity.WmsAuxiliaryInventoryDetail;
import com.tgvs.wms.business.modules.auxiliaryInventory.mapper.WmsAuxiliaryInventoryDetailMapper;
import com.tgvs.wms.business.modules.auxiliaryInventory.mapper.WmsAuxiliaryInventoryMapper;
import com.tgvs.wms.business.modules.auxiliaryInventory.service.IWmsAuxiliaryInventoryService;
import com.tgvs.wms.business.modules.container.entity.Container;
import com.tgvs.wms.business.modules.container.mapper.ContainerMapper;
import com.tgvs.wms.business.modules.task.entity.WmsBoxTaskList;
import com.tgvs.wms.business.modules.task.service.IWmsBoxTaskListService;
import com.tgvs.wms.common.core.domain.QueryModel;
import com.tgvs.wms.common.query.QueryGenerator;
import com.tgvs.wms.common.util.ShiroUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

/**
 * 辅料盘点服务实现类 (简化版 - 符合WMS框架标准)
 */
@Service
public class WmsAuxiliaryInventoryServiceImpl extends ServiceImpl<WmsAuxiliaryInventoryMapper, WmsAuxiliaryInventory> 
        implements IWmsAuxiliaryInventoryService {

    private static final Logger log = LoggerFactory.getLogger(WmsAuxiliaryInventoryServiceImpl.class);

    @Autowired
    private WmsAuxiliaryInventoryDetailMapper detailMapper;
    
    @Autowired
    private ContainerMapper containerMapper;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;


    @Autowired
    private IWmsBoxTaskListService taskListService;

    @Override
    public IPage<WmsAuxiliaryInventory> pageList(QueryModel queryModel) {

        // 构建查询条件
       QueryWrapper<WmsAuxiliaryInventory> queryWrapper = QueryGenerator.initQueryWrapper(queryModel.getSearchParams());
        // 构建分页对象
        Page<WmsAuxiliaryInventory> page = new Page<>(queryModel.getPage(), queryModel.getLimit());

        return this.page(page, queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createInventory(WmsAuxiliaryInventory inventory) {
        // 生成盘点单号
        inventory.setInventoryNo(generateInventoryNo());
        inventory.setStatus("DRAFT");
        inventory.setTotalItems(0);
        inventory.setCountedItems(0);
        inventory.setDiffItems(0);
        inventory.setCreateBy(ShiroUtils.getLoginName());
        inventory.setCreateTime(new Date());
        
        return save(inventory);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateInventory(WmsAuxiliaryInventory inventory) {
        // 状态控制：只有草稿状态的盘点单才能编辑
        WmsAuxiliaryInventory existingInventory = getById(inventory.getId());
        if (existingInventory == null) {
            throw new RuntimeException("盘点单不存在");
        }
        
        if (!"DRAFT".equals(existingInventory.getStatus())) {
            throw new RuntimeException("只能编辑草稿状态的盘点单");
        }
        
        inventory.setUpdateBy(ShiroUtils.getLoginName());
        inventory.setUpdateTime(new Date());
        return updateById(inventory);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteInventory(String[] ids) {
        for (String id : ids) {
            // 状态控制：只有草稿状态的盘点单才能删除
            WmsAuxiliaryInventory inventory = getById(id);
            if (inventory == null) {
                log.warn("盘点单不存在，跳过删除: {}", id);
                continue;
            }
            
            if (!"DRAFT".equals(inventory.getStatus())) {
                throw new RuntimeException("只能删除草稿状态的盘点单: " + inventory.getInventoryName());
            }
            
            // 删除明细
            LambdaQueryWrapper<WmsAuxiliaryInventoryDetail> detailWrapper = new LambdaQueryWrapper<>();
            detailWrapper.eq(WmsAuxiliaryInventoryDetail::getInventoryId, id);
            int deletedDetails = detailMapper.delete(detailWrapper);
            log.info("删除盘点明细记录数: {}", deletedDetails);
            
            // 删除主表
            removeById(id);
            log.info("删除盘点单: {}", id);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createInventoryFromStock(Map<String, Object> request) {
        log.info("开始创建盘点任务，请求参数: {}", request);
        
        String inventoryType = (String) request.get("inventoryType");
        
        // 1. 创建盘点单
        WmsAuxiliaryInventory inventory = new WmsAuxiliaryInventory();
        inventory.setInventoryName((String) request.get("inventoryName"));
        inventory.setInventoryType(inventoryType);
        inventory.setWarehouseCode((String) request.get("warehouseCode"));
        inventory.setWarehouseName((String) request.get("warehouseName"));
        inventory.setRemark((String) request.get("remark"));
        
        createInventory(inventory);
        
        String inventoryId = inventory.getId();
        log.info("盘点单创建成功，ID: {}, 盘点类型: {}", inventoryId, inventoryType);
        
        if (inventoryId == null || inventoryId.trim().isEmpty()) {
            throw new RuntimeException("盘点单ID生成失败");
        }
        
        // 2. 根据盘点类型创建盘点明细
        List<WmsAuxiliaryInventoryDetail> details = new ArrayList<>();
        
        if ("DIFF".equals(inventoryType)) {
            // 差异盘点：基于差异数据源创建明细
            log.info("创建差异盘点明细");
            createDifferenceInventoryDetails(request, inventoryId, details);
        } else {
            // 抽盘盘点：基于库存数据创建明细
            log.info("创建抽盘盘点明细");
            createPartInventoryDetails(request, inventoryId, details);
        }
        
        // 3. 批量保存盘点明细
        if (!details.isEmpty()) {
            for (WmsAuxiliaryInventoryDetail detail : details) {
                detailMapper.insert(detail);
            }
            log.info("盘点明细创建完成，共创建{}条明细", details.size());
            
            // 4. 更新盘点单统计
            updateInventoryStatistics(inventoryId);
        } else {
            log.warn("没有创建任何盘点明细");
        }
        
        return inventoryId;
    }

    /**
     * 创建抽盘盘点明细（基于库存数据）
     */
    private void createPartInventoryDetails(Map<String, Object> request, String inventoryId, List<WmsAuxiliaryInventoryDetail> details) {
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> stockList = (List<Map<String, Object>>) request.get("stockList");
        
        if (stockList == null || stockList.isEmpty()) {
            throw new RuntimeException("库存数据列表不能为空");
        }
        
        stockList.forEach(stock -> {
            WmsAuxiliaryInventoryDetail detail = new WmsAuxiliaryInventoryDetail();
            detail.setInventoryId(inventoryId);
            detail.setMaterialCode((String) stock.get("materialCode"));
            detail.setMaterialName((String) stock.get("materialName"));
            detail.setMaterialSpec((String) stock.get("materialModel")); // 规格型号
            detail.setMaterialColor((String) stock.get("materialColor")); // 物料颜色
            detail.setMaterialSize((String) stock.get("materialSize")); // 尺码
            detail.setUnit((String) stock.get("unit")); // 单位
            detail.setLocationCode((String) stock.get("code"));
            detail.setBoxNo((String) stock.get("boxNo"));
            detail.setSystemQty(new BigDecimal(stock.get("materialQuantity").toString()));
            detail.setStatus("PENDING");
            detail.setCreateBy(ShiroUtils.getLoginName());
            detail.setCreateTime(new Date());
            
            // 通过容器号查询容器类型
            String boxNo = (String) stock.get("boxNo");
            if (boxNo != null && !boxNo.trim().isEmpty()) {
                try {
                    LambdaQueryWrapper<Container> containerWrapper = new LambdaQueryWrapper<>();
                    containerWrapper.eq(Container::getBoxNo, boxNo);
                    Container container = containerMapper.selectOne(containerWrapper);
                    
                    if (container != null && container.getBoxType() != null) {
                        detail.setBoxType(container.getBoxType());
                        log.debug("查询到容器类型: boxNo={}, boxType={}", boxNo, container.getBoxType());
                    } else {
                        log.warn("未找到容器信息或容器类型为空: boxNo={}", boxNo);
                        // 设置默认容器类型为料箱
                        detail.setBoxType(1);
                    }
                } catch (Exception e) {
                    log.error("查询容器类型失败: boxNo={}, error={}", boxNo, e.getMessage());
                    // 设置默认容器类型为料箱
                    detail.setBoxType(1);
                }
            } else {
                log.warn("容器号为空，使用默认容器类型");
                // 设置默认容器类型为料箱
                detail.setBoxType(1);
            }
            
            details.add(detail);
        });
    }

    /**
     * 创建差异盘点明细（基于差异数据源）
     */
    private void createDifferenceInventoryDetails(Map<String, Object> request, String inventoryId, List<WmsAuxiliaryInventoryDetail> details) {
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> differenceList = (List<Map<String, Object>>) request.get("stockList");
        
        if (differenceList == null || differenceList.isEmpty()) {
            throw new RuntimeException("差异数据列表不能为空");
        }
        
        differenceList.forEach(difference -> {
            WmsAuxiliaryInventoryDetail detail = new WmsAuxiliaryInventoryDetail();
            detail.setInventoryId(inventoryId);
            detail.setMaterialCode((String) difference.get("materialCode"));
            detail.setMaterialName((String) difference.get("materialName"));
            detail.setMaterialSpec((String) difference.get("materialSpec")); // 规格型号
            detail.setMaterialColor((String) difference.get("materialColor")); // 物料颜色
            detail.setMaterialSize((String) difference.get("materialSize")); // 尺码
            detail.setUnit((String) difference.get("unit")); // 单位
            detail.setLocationCode((String) difference.get("locationCode"));
            detail.setBoxNo((String) difference.get("boxNo"));
            
            // 差异盘点使用WMS库存数量作为系统数量
            Object wmsQtyObj = difference.get("wmsQty");
            if (wmsQtyObj != null) {
                detail.setSystemQty(new BigDecimal(wmsQtyObj.toString()));
            } else {
                detail.setSystemQty(BigDecimal.ZERO);
            }
            
            detail.setStatus("PENDING");
            detail.setCreateBy(ShiroUtils.getLoginName());
            detail.setCreateTime(new Date());
            
            // 设置容器类型
            Object boxTypeObj = difference.get("boxType");
            if (boxTypeObj != null) {
                try {
                    detail.setBoxType(Integer.parseInt(boxTypeObj.toString()));
                } catch (NumberFormatException e) {
                    log.warn("容器类型转换失败，使用默认值: boxType={}", boxTypeObj);
                    detail.setBoxType(1); // 默认为料箱
                }
            } else {
                detail.setBoxType(1); // 默认为料箱
            }
            
            // 差异盘点备注，记录ERP和WMS数量对比信息
            Object erpQtyObj = difference.get("erpQty");
            Object diffQtyObj = difference.get("diffQty");
            Object diffTypeObj = difference.get("diffType");
            if (erpQtyObj != null && diffQtyObj != null && diffTypeObj != null) {
                String diffRemark = String.format("WMS数量: %s, ERP(i9)数量: %s, 差异: %s (%s)", 
                    wmsQtyObj, erpQtyObj, diffQtyObj, diffTypeObj);
                detail.setCountRemark(diffRemark);
            }
            
            details.add(detail);
            
            log.debug("创建差异盘点明细: materialCode={}, systemQty={}, diffType={}", 
                detail.getMaterialCode(), detail.getSystemQty(), diffTypeObj);
        });
        
        log.info("创建差异盘点明细完成，共 {} 条记录", details.size());
    }

    @Override
    public List<WmsAuxiliaryInventoryDetail> getInventoryDetails(String inventoryId) {
        log.info("查询盘点明细, inventoryId: {}", inventoryId);
        
        LambdaQueryWrapper<WmsAuxiliaryInventoryDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WmsAuxiliaryInventoryDetail::getInventoryId, inventoryId);
        wrapper.orderByAsc(WmsAuxiliaryInventoryDetail::getId);
        
        List<WmsAuxiliaryInventoryDetail> details = detailMapper.selectList(wrapper);
        log.info("查询到 {} 条盘点明细记录", details.size());
        
        return details;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createAgvOutboundTask(String detailId) {
        log.info("开始创建AGV盘点出库任务, detailId: {}", detailId);
        
        WmsAuxiliaryInventoryDetail detail = detailMapper.selectById(detailId);
        if (detail == null) {
            log.error("盘点明细不存在: {}", detailId);
            throw new RuntimeException("盘点明细不存在");
        }
        
        log.info("查询到盘点明细: materialCode={}, locationCode={}, boxNo={}, status={}", 
            detail.getMaterialCode(), detail.getLocationCode(), detail.getBoxNo(), detail.getStatus());
        
        if (!"PENDING".equals(detail.getStatus())) {
            log.error("只有待盘点状态的明细才能创建AGV任务，当前状态: {}", detail.getStatus());
            throw new RuntimeException("只有待盘点状态的明细才能创建AGV任务");
        }
        
        // 验证容器号是否存在
        if (detail.getBoxNo() == null || detail.getBoxNo().trim().isEmpty()) {
            log.error("盘点明细缺少容器号: detailId={}", detailId);
            throw new RuntimeException("盘点明细缺少容器号");
        }
        // 使用盘点明细中存储的容器类型
        Integer boxType = detail.getBoxType();
        if (boxType == null) {
            log.warn("盘点明细中容器类型为空，使用默认值: detailId={}", detailId);
            throw new RuntimeException("盘点明细中容器类型为空");
        }
        // AGV任务检查已简化 - 依赖缺失时跳过
//此处需要判断该容器是否有未完成的任务在进行中
        LambdaQueryWrapper<WmsBoxTaskList> taskWrapper = new LambdaQueryWrapper<>();
        taskWrapper.eq(WmsBoxTaskList::getBoxNo, detail.getBoxNo());
        taskWrapper.in(WmsBoxTaskList::getTaskStatus, 0,1,2,3);
        taskWrapper.eq(WmsBoxTaskList::getDeleteFlag, 0);
        int taskCount = taskListService.getBaseMapper().selectCount(taskWrapper);
        if (taskCount > 0) {
            log.error("容器已有未完成的任务，不能创建盘点出库任务: boxNo={}", detail.getBoxNo());
            throw new RuntimeException("容器已有未完成的任务，不能创建盘点出库任务");
        }
        String toSite=stringRedisTemplate.opsForValue().get("outbound");
         //判断容器类型如果是托盘，则需要取值agvCar
        if (detail.getBoxType()==2){
            toSite=stringRedisTemplate.opsForValue().get("agvCar");
        }

        // 创建AGV盘点出库任务 (遵循WMS标准模式)
        WmsBoxTaskList task = new WmsBoxTaskList();

        // 设置基本任务信息
        task.setFromSite(detail.getLocationCode());
        task.setToSite(toSite); // 盘点工位
        task.setBoxNo(detail.getBoxNo());
        task.setBoxType(boxType);
        task.setTaskType(10); // 盘点出库 (根据注释: 10.盘点出库)
        task.setMaterialType(2); // 辅料 (标准化: 2=辅料)
        task.setTaskStatus(0); // 创建状态
        task.setCreateBy(ShiroUtils.getLoginName());
        task.setCreateTime(new Date());

        log.info("准备创建AGV任务: fromSite={}, toSite={}, boxNo={}, boxType={}, taskType={}",
                task.getFromSite(), task.getToSite(), task.getBoxNo(), boxType, task.getTaskType());

        // 使用标准的createTask方法创建任务 (会自动生成任务号和分配库位)
        WmsBoxTaskList createdTask = taskListService.createTask(task);
        if (createdTask == null) {
            log.error("AGV任务创建失败: 服务返回null");
            throw new RuntimeException("AGV任务创建失败");
        }

        String taskId = createdTask.getId();
        String taskOrder = createdTask.getTaskOrder();
        log.info("AGV任务创建成功, taskId: {}, taskOrder: {}", taskId, taskOrder);

        // 更新盘点明细状态和关联任务ID
        detail.setOutboundTaskOrder(taskOrder);
        detail.setStatus("OUTBOUND");
        detail.setUpdateBy(ShiroUtils.getLoginName());
        detail.setUpdateTime(new Date());

        int updateResult = detailMapper.updateById(detail);
        if (updateResult <= 0) {
            log.error("更新盘点明细失败, detailId: {}", detailId);
            throw new RuntimeException("更新盘点明细失败");
        }
        log.info("AGV盘点出库任务创建完成, 任务单号: {}, 盘点明细ID: {}", taskOrder, detailId);

        log.info("AGV盘点出库任务创建完成, WMS任务ID: {}, 任务单号: {}, 盘点明细ID: {}",
                taskId, taskOrder, detailId);


        return taskOrder;
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean submitInventoryCount(Map<String, Object> request) {
        String inventoryDetailId = (String) request.get("inventoryDetailId");
        WmsAuxiliaryInventoryDetail detail = detailMapper.selectById(inventoryDetailId);
        if (detail == null) {
            throw new RuntimeException("盘点明细不存在");
        }
        
        // 更新实盘数量和差异
        BigDecimal actualQty = new BigDecimal(request.get("actualQty").toString());
        detail.setActualQty(actualQty);
        detail.setDiffQty(actualQty.subtract(detail.getSystemQty()));
        detail.setCountRemark((String) request.get("countRemark"));
        detail.setCountBy(ShiroUtils.getLoginName());
        detail.setCountTime(new Date());
        detail.setStatus("COMPLETED");
        detail.setUpdateTime(new Date());
        detailMapper.updateById(detail);
        
        // 创建AGV回库任务
        createAgvReturnTask(detail);
        
        // 更新盘点单统计
        updateInventoryStatistics(detail.getInventoryId());
        
        return true;
    }

    @Override
    public Map<String, Object> getAgvTaskStatus(String taskOrder) {
        Map<String, Object> result = new HashMap<>();


        log.info("查询AGV任务状态, 接收到的参数 taskId: {}", taskOrder);

        if (taskOrder == null || taskOrder.trim().isEmpty()) {
            result.put("taskOrder", taskOrder);
            result.put("status", "INVALID");
            result.put("message", "任务ID为空");
            log.warn("AGV任务ID为空");
            return result;
        }

        // 直接通过taskId查询任务
        LambdaQueryWrapper<WmsBoxTaskList> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WmsBoxTaskList::getTaskOrder, taskOrder);
        WmsBoxTaskList task = taskListService.getBaseMapper().selectOne(queryWrapper);
        // 简化AGV状态查询 - 返回模拟状态
        log.info("查询AGV任务状态, taskOrder: {}", taskOrder);

        if (task != null) {
            String status = "UNKNOWN";
            String message = "任务状态未知";

            Integer taskStatus = task.getTaskStatus();
            log.info("查询到任务，状态码: {}", taskStatus);

            if (taskStatus != null) {
                switch (taskStatus) {
                    case 0:
                        status = "CREATED";
                        message = "任务已创建";
                        break;
                    case 1:
                        status = "ISSUED";
                        message = "任务已下发";
                        break;
                    case 2:
                        status = "EXECUTING";
                        message = "正在搬运";
                        break;
                    case 3:
                        status = "TRANSPORTING";
                        message = "输送线运输中";
                        break;
                    case 4:
                        status = "DELIVERED";
                        message = "物料已送达盘点工位";
                        break;
                    case 5:
                        status = "COMPLETED";
                        message = "AGV任务已完成";
                        break;
                    default:
                        status = "UNKNOWN";
                        message = "任务状态未知: " + taskStatus;
                }
            }

            result.put("taskId", task.getId());
            result.put("taskOrder", task.getTaskOrder());
            result.put("status", status);
            result.put("message", message);
            result.put("taskStatus", taskStatus);
            result.put("fromSite", task.getFromSite());
            result.put("toSite", task.getToSite());
            result.put("boxNo", task.getBoxNo());

            // **关键修复：自动状态同步逻辑**
            // 当AGV任务完成时，自动更新相关盘点明细状态
            if (taskStatus != null && (taskStatus == 4 || taskStatus == 5)) {
                try {
                    // 查找关联的盘点明细（通过outboundTaskId关联，现在存储的是taskId）
                    LambdaQueryWrapper<WmsAuxiliaryInventoryDetail> detailWrapper = new LambdaQueryWrapper<>();
                    detailWrapper.eq(WmsAuxiliaryInventoryDetail::getOutboundTaskOrder, task.getTaskOrder());

                    List<WmsAuxiliaryInventoryDetail> details = detailMapper.selectList(detailWrapper);

                    for (WmsAuxiliaryInventoryDetail detail : details) {
                        // 只更新状态为OUTBOUND的明细
                        if ("OUTBOUND".equals(detail.getStatus())) {
                            detail.setStatus("COUNTING");
                            detail.setUpdateBy("SYSTEM");
                            detail.setUpdateTime(new Date());
                            detailMapper.updateById(detail);

                            log.info("自动更新盘点明细状态: detailId={}, 从OUTBOUND更新为COUNTING", detail.getId());
                            result.put("detailStatusUpdated", true);
                        }
                    }
                } catch (Exception e) {
                    log.error("自动更新盘点明细状态失败: {}", e.getMessage(), e);
                    // 不影响状态查询的主要功能
                }
            }

            log.info("AGV任务状态查询成功: {}", status);
        } else {
            result.put("taskOrder", taskOrder);
            result.put("status", "NOT_FOUND");
            result.put("message", "任务不存在，请检查任务ID是否正确");
            log.warn("任务不存在: {}", taskOrder);
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean startInventory(String inventoryId) {
        WmsAuxiliaryInventory inventory = getById(inventoryId);
        if (inventory == null) {
            throw new RuntimeException("盘点单不存在");
        }
        
        if (!"DRAFT".equals(inventory.getStatus())) {
            throw new RuntimeException("只能开始草稿状态的盘点单");
        }
        
        inventory.setStatus("COUNTING");
        inventory.setUpdateBy(ShiroUtils.getLoginName());
        inventory.setUpdateTime(new Date());
        
        return updateById(inventory);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean completeInventory(String inventoryId) {
        WmsAuxiliaryInventory inventory = getById(inventoryId);
        if (inventory == null) {
            throw new RuntimeException("盘点单不存在");
        }
        
        // 检查是否所有明细都已完成
        LambdaQueryWrapper<WmsAuxiliaryInventoryDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WmsAuxiliaryInventoryDetail::getInventoryId, inventoryId);
        wrapper.ne(WmsAuxiliaryInventoryDetail::getStatus, "COMPLETED");
        
        long pendingCount = detailMapper.selectCount(wrapper);
        if (pendingCount > 0) {
            throw new RuntimeException("还有未完成的盘点明细，无法完成盘点");
        }
        
        inventory.setStatus("COMPLETED");
        inventory.setUpdateBy(ShiroUtils.getLoginName());
        inventory.setUpdateTime(new Date());
        
        return updateById(inventory);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean resetInventoryDetail(String detailId) {
        WmsAuxiliaryInventoryDetail detail = detailMapper.selectById(detailId);
        if (detail == null) {
            throw new RuntimeException("盘点明细不存在");
        }
        
        // 重置状态和数量
        detail.setStatus("PENDING");
        detail.setActualQty(null);
        detail.setDiffQty(null);
        detail.setCountBy(null);
        detail.setCountTime(null);
        detail.setCountRemark(null);
        detail.setOutboundTaskOrder(null);
        detail.setInboundTaskOrder(null);
        detail.setUpdateTime(new Date());
        
        // 更新记录
        int result = detailMapper.updateById(detail);
        
        log.info("重置盘点明细: detailId={}, result={}", detailId, result);
        
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteInventoryDetail(String detailId) {
        WmsAuxiliaryInventoryDetail detail = detailMapper.selectById(detailId);
        if (detail == null) {
            throw new RuntimeException("盘点明细不存在");
        }
        
        // 检查盘点单状态
        WmsAuxiliaryInventory inventory = getById(detail.getInventoryId());
        if (inventory == null) {
            throw new RuntimeException("盘点单不存在");
        }
        
        if (!"DRAFT".equals(inventory.getStatus()) && !"COUNTING".equals(inventory.getStatus())) {
            throw new RuntimeException("只能删除草稿或盘点中状态的盘点明细");
        }
        
        // 检查明细状态 - 只有PENDING和OUTBOUND状态可以删除
        if ("COMPLETED".equals(detail.getStatus())) {
            throw new RuntimeException("已完成的盘点明细不能删除");
        }
        
        // 删除明细记录
        int result = detailMapper.deleteById(detailId);
        
        if (result > 0) {
            // 更新盘点单统计信息
            updateInventoryStatistics(detail.getInventoryId());
            log.info("删除盘点明细: detailId={}, inventoryId={}", detailId, detail.getInventoryId());
        }
        
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteInventoryDetails(String[] detailIds) {
        for (String detailId : detailIds) {
            deleteInventoryDetail(detailId);
        }
        return true;
    }

    /**
     * 更新盘点单统计信息
     */
    private void updateInventoryStatistics(String inventoryId) {
        LambdaQueryWrapper<WmsAuxiliaryInventoryDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WmsAuxiliaryInventoryDetail::getInventoryId, inventoryId);
        
        List<WmsAuxiliaryInventoryDetail> details = detailMapper.selectList(wrapper);
        
        int totalItems = details.size();
        int countedItems = 0;
        int diffItems = 0;
        
        for (WmsAuxiliaryInventoryDetail detail : details) {
            if ("COMPLETED".equals(detail.getStatus())) {
                countedItems++;
                if (detail.getDiffQty() != null && detail.getDiffQty().compareTo(BigDecimal.ZERO) != 0) {
                    diffItems++;
                }
            }
        }
        
        WmsAuxiliaryInventory inventory = getById(inventoryId);
        if (inventory != null) {
            inventory.setTotalItems(totalItems);
            inventory.setCountedItems(countedItems);
            inventory.setDiffItems(diffItems);
            inventory.setUpdateTime(new Date());
            inventory.setUpdateBy(ShiroUtils.getLoginName());
            updateById(inventory);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addStockToInventory(String inventoryId, List<WmsAuxiliaryInventoryDetail> details) {
        // 检查盘点单状态
        WmsAuxiliaryInventory inventory = getById(inventoryId);
        if (inventory == null) {
            throw new RuntimeException("盘点单不存在");
        }
        
        if (!"DRAFT".equals(inventory.getStatus())) {
            throw new RuntimeException("只能向草稿状态的盘点单添加库存");
        }
        
        // 批量插入明细
        for (WmsAuxiliaryInventoryDetail detail : details) {
            // 检查是否已存在相同的库存记录
            LambdaQueryWrapper<WmsAuxiliaryInventoryDetail> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(WmsAuxiliaryInventoryDetail::getInventoryId, inventoryId)
                   .eq(WmsAuxiliaryInventoryDetail::getMaterialCode, detail.getMaterialCode())
                   .eq(WmsAuxiliaryInventoryDetail::getLocationCode, detail.getLocationCode())
                   .eq(WmsAuxiliaryInventoryDetail::getBoxNo, detail.getBoxNo());
            
            WmsAuxiliaryInventoryDetail existing = detailMapper.selectOne(wrapper);
            if (existing != null) {
                log.warn("库存记录已存在，跳过: materialCode={}, boxNo={}", 
                        detail.getMaterialCode(), detail.getBoxNo());
                continue;
            }
            
            int result = detailMapper.insert(detail);
            log.info("插入盘点明细: inventoryId={}, materialCode={}, result={}", 
                    inventoryId, detail.getMaterialCode(), result);
        }
        
        // 更新统计信息
        updateInventoryStatistics(inventoryId);
        
        return true;
    }

    // ========== 私有辅助方法 ==========

    private String generateInventoryNo() {
        return "INV" + System.currentTimeMillis();
    }
    
    private void createAgvReturnTask(WmsAuxiliaryInventoryDetail detail) {
        log.info("开始创建AGV盘点回库任务, detailId: {}, boxNo: {}", detail.getId(), detail.getBoxNo());

        String fromSite=stringRedisTemplate.opsForValue().get("inbound");
        //判断容器类型如果是托盘，则需要取值agvCar
        if (detail.getBoxType()==2){
            fromSite=stringRedisTemplate.opsForValue().get("agvCar");
        }

        // 创建AGV盘点回库任务 (遵循WMS标准模式)
        WmsBoxTaskList returnTask = new WmsBoxTaskList();

        // 设置基本任务信息
        returnTask.setFromSite(fromSite); // 盘点工位
        // returnTask.setToSite(detail.getLocationCode()); // 回到原库位
        returnTask.setBoxNo(detail.getBoxNo());
        returnTask.setBoxType(detail.getBoxType()); // 料箱
        returnTask.setTaskType(12); // 盘点入库 (根据注释: 12.盘点入库)
        returnTask.setMaterialType(2); // 辅料 (标准化: 2=辅料)
        returnTask.setTaskStatus(0); // 创建状态
        returnTask.setPriority(5); // 中等优先级
        returnTask.setDeleteFlag(0); // 正常状态
        returnTask.setCreateBy(ShiroUtils.getLoginName());
        returnTask.setCreateTime(new Date());

        log.info("准备创建AGV回库任务: fromSite={}, toSite={}, boxNo={}, taskType={}",
                returnTask.getFromSite(), returnTask.getToSite(), returnTask.getBoxNo(), returnTask.getTaskType());

        // 使用标准的createTask方法创建任务 (会自动生成任务号)
        WmsBoxTaskList createdReturnTask = taskListService.createTask(returnTask);
        if (createdReturnTask == null) {
            log.error("AGV回库任务创建失败: 服务返回null");
            throw new RuntimeException("AGV回库任务创建失败");
        }

        String returnTaskId = createdReturnTask.getId();
        String returnTaskOrder = createdReturnTask.getTaskOrder();
        log.info("AGV回库任务创建成功, taskId: {}, taskOrder: {}", returnTaskId, returnTaskOrder);
        // 更新明细表关联的回库任务ID
        detail.setInboundTaskOrder(returnTaskOrder);
        detail.setUpdateBy(ShiroUtils.getLoginName());
        detail.setUpdateTime(new Date());
        detailMapper.updateById(detail);
        
        log.info("AGV盘点回库任务创建完成, 任务单号: {}, 盘点明细ID: {}", returnTaskOrder, detail.getId());
    }
    
}
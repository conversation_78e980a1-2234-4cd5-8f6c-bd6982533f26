package com.tgvs.wms.business.modules.auxiliaryInventory.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tgvs.wms.business.modules.auxiliaryInventory.entity.WmsAuxiliaryInventory;
import org.apache.ibatis.annotations.Param;

/**
 * 辅料盘点单Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-07-19
 */
public interface WmsAuxiliaryInventoryMapper extends BaseMapper<WmsAuxiliaryInventory> {

    /**
     * 分页查询盘点单列表
     * 
     * @param page 分页对象
     * @param inventory 查询条件
     * @return 分页结果
     */
    IPage<WmsAuxiliaryInventory> selectInventoryPage(Page<WmsAuxiliaryInventory> page, 
                                                         @Param("inventory") WmsAuxiliaryInventory inventory);

    /**
     * 根据盘点单号查询
     * 
     * @param inventoryNo 盘点单号
     * @return 盘点单信息
     */
    WmsAuxiliaryInventory selectByInventoryNo(@Param("inventoryNo") String inventoryNo);

    /**
     * 统计状态数量
     * 
     * @param status 状态
     * @return 数量
     */
    int countByStatus(@Param("status") String status);
}
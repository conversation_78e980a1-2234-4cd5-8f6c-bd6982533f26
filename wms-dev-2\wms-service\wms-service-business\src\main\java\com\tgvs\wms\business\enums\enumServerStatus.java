package com.tgvs.wms.business.enums;


import com.tgvs.wms.common.util.oConvertUtils;

public enum enumServerStatus {
    stop(Integer.valueOf(0), "stop", "停止"),
    run(Integer.valueOf(1), "run", "运行");

    private Integer value;

    private String code;

    private String text;

    enumServerStatus(Integer value, String code, String text) {
        this.value = value;
        this.code = code;
        this.text = text;
    }

    public static enumServerStatus getByValue(int value) {
        if (oConvertUtils.isEmpty(Integer.valueOf(value)))
            return null;
        for (enumServerStatus val : values()) {
            if (val.getValue().equals(Integer.valueOf(value)))
                return val;
        }
        return null;
    }

    public Integer getValue() {
        return this.value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getText() {
        return this.text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public static enumServerStatus toEnum(Integer Value) {
        for (enumServerStatus e : values()) {
            if (e.getValue().equals(Value))
                return e;
        }
        return null;
    }
}

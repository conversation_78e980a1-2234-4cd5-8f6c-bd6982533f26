package com.tgvs.wms.business.enums;

import com.tgvs.wms.common.util.oConvertUtils;

public enum enumDispatchType {
    plan(Integer.valueOf(1), "plan", "计划"),
    away_full_songbu(Integer.valueOf(2), "away_full_songbu", "松布区拉走满松布架"),
    call_scrap_songbu(Integer.valueOf(3), "call_scrap_songbu", "松布工位呼叫余布松布架"),
    call_full_cut(Integer.valueOf(4), "call_full_cut", "裁剪工位呼叫搬运满松布架"),
    away_empty_cut(Integer.valueOf(5), "away_empty_cut", "裁剪工位呼叫拉走空松布架"),
    away_full_cut(Integer.valueOf(6), "away_full_cut", "裁剪工位呼叫拉走满松布架"),
    transfer_dolly_cut(Integer.valueOf(7), "transfer_dolly_cut", "裁剪工位间搬运呼叫松布架"),
    call_scrap_cut(Integer.valueOf(8), "call_scrap_cut", "裁剪工位呼叫余布松布架"),
    call_empty_cut(Integer.valueOf(9), "call_empty_cut", "裁剪工位呼叫空松布架"),
    call_empty_songbu(Integer.valueOf(11), "call_empty_songbu", "松布工位呼叫空松布架"),
    away_full_songbu_nocall(Integer.valueOf(10), "away_full_songbu_nocall", "单项呼叫松布工位拉走满松布架");

    private Integer value;

    private String code;

    private String text;

    enumDispatchType(Integer value, String code, String text) {
        this.value = value;
        this.code = code;
        this.text = text;
    }

    public static enumDispatchType getByValue(int value) {
        if (oConvertUtils.isEmpty(Integer.valueOf(value)))
            return null;
        for (enumDispatchType val : values()) {
            if (val.getValue().equals(Integer.valueOf(value)))
                return val;
        }
        return null;
    }

    public Integer getValue() {
        return this.value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getText() {
        return this.text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public static enumDispatchType toEnum(Integer Value) {
        for (enumDispatchType e : values()) {
            if (e.getValue().equals(Value))
                return e;
        }
        return null;
    }
}

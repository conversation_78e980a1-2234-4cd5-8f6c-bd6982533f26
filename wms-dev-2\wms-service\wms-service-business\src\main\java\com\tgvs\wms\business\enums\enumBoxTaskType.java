package com.tgvs.wms.business.enums;

import com.tgvs.wms.common.util.oConvertUtils;

/**
 * 任务类型：0采购入库，1调拨入库，2生产退料回库，3领料出库，4调拨出库，5采购退货出库，6指定出库，7.指定入库，8.紧急出库。10.盘点出库
 */
public enum enumBoxTaskType {
    /** 任务类型：0采购入库，1、一般入库，2生产退料回库，3领料出库，4调拨出库，5采购退货出库，6指定出库，7.指定入库，8.紧急出库。9、调拨入库 10.盘点出库 */
    purchaseInbound(0, "purchaseInbound", "采购入库"),
    generalInbound(1, "generalInbound", "一般入库"),
    returnMaterialInbound(2, "returnMaterialInbound", "生产退料回库"),
    pickingOutbound(3, "pickingOutbound", "领料出库"),
    transferOutbound(4, "transferOutbound", "调拨出库"),
    purchaseReturnOutbound(5, "purchaseReturnOutbound", "采购退货出库"),
    manualOutbound(6, "manualOutbound", "指定出库"),
    manualInbound(7, "manualInbound", "指定入库"),
    emergencyOutbound(8, "emergencyOutbound", "紧急出库"),
    transferInbound(9, "transferInbound", "调拨入库"),
    inventoryOutbound(10, "inventoryOutbound", "盘点出库"),
    move(15, "move", "移库");



    //    任务类型：0.采购入库，1.调拨入库，2.生产退料回库，3.领料出库，4.调拨出库，5.采购退货出库，6.指定出库，7.盘点出库
    private Integer value;

    private String code;

    private String text;

    enumBoxTaskType(Integer value, String code, String text) {
        this.value = value;
        this.code = code;
        this.text = text;
    }

    public static enumBoxTaskType getByValue(int value) {
        if (oConvertUtils.isEmpty(Integer.valueOf(value)))
            return null;
        for (enumBoxTaskType val : values()) {
            if (val.getValue().equals(Integer.valueOf(value)))
                return val;
        }
        return null;
    }

    public Integer getValue() {
        return this.value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getText() {
        return this.text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public static enumBoxTaskType toEnum(Integer Value) {
        for (enumBoxTaskType e : values()) {
            if (e.getValue().equals(Value))
                return e;
        }
        return null;
    }

    public static enumBoxTaskType toEnum(String Value) {
        for (enumBoxTaskType e : values()) {
            if (e.getValue().toString().equals(Value))
                return e;
        }
        return null;
    }
}

package com.tgvs.wms.business.modules.auxiliaryInventory.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.tgvs.wms.base.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 辅料盘点明细实体类 (简化版)
 * 
 * <AUTHOR>
 * @since 2025-07-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wms_auxiliary_inventory_detail")
@ApiModel(value = "WmsAuxiliaryInventoryDetail对象", description = "辅料盘点明细")
public class WmsAuxiliaryInventoryDetail extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("盘点单ID")
    @TableField("inventory_id")
    private String inventoryId;

    @ApiModelProperty("物料编码")
    @TableField("material_code")
    private String materialCode;

    @ApiModelProperty("物料名称")
    @TableField("material_name")
    private String materialName;

    @ApiModelProperty("规格型号")
    @TableField("material_spec")
    private String materialSpec;

    @ApiModelProperty("物料颜色")
    @TableField("material_color")
    private String materialColor;

    @ApiModelProperty("尺码")
    @TableField("material_size")
    private String materialSize;

    @ApiModelProperty("单位")
    @TableField("unit")
    private String unit;

    @ApiModelProperty("库位编码")
    @TableField("location_code")
    private String locationCode;

    @ApiModelProperty("库位名称")
    @TableField("location_name")
    private String locationName;

    @ApiModelProperty("容器号")
    @TableField("box_no")
    private String boxNo;

    @ApiModelProperty("容器类型：1-料箱，2-托盘")
    @TableField("box_type")
    private Integer boxType;

    @ApiModelProperty("批次号")
    @TableField("batch_no")
    private String batchNo;

    @ApiModelProperty("系统数量")
    @TableField("system_qty")
    private BigDecimal systemQty;

    @ApiModelProperty("实盘数量")
    @TableField("actual_qty")
    private BigDecimal actualQty;

    @ApiModelProperty("差异数量")
    @TableField("diff_qty")
    private BigDecimal diffQty;

    @ApiModelProperty("出库任务order(关联wms_box_task_list)")
    @TableField("outbound_task_order")
    private String outboundTaskOrder;

    @ApiModelProperty("回库任务order(关联wms_box_task_list)")
    @TableField("inbound_task_order")
    private String inboundTaskOrder;

    @ApiModelProperty("状态：PENDING-待盘点，OUTBOUND-出库中，COUNTING-盘点中，COMPLETED-已完成")
    @TableField("status")
    private String status;

    @ApiModelProperty("盘点人")
    @TableField("count_by")
    private String countBy;

    @ApiModelProperty("盘点时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("count_time")
    private Date countTime;

    @ApiModelProperty("盘点备注")
    @TableField("count_remark")
    private String countRemark;

    @ApiModelProperty("差异原因")
    @TableField("diff_reason")
    private String diffReason;

    @ApiModelProperty("创建人")
    @TableField("create_by")
    private String createBy;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("create_time")
    private Date createTime;

    @ApiModelProperty("更新人")
    @TableField("update_by")
    private String updateBy;

    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("update_time")
    private Date updateTime;
}
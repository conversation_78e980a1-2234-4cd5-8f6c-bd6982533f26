package com.tgvs.wms.business.httpservice.baseBean.agv;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * 海康AGV任务继续接口请求实体
 */
@Data
public class ContinueTaskRequest implements Serializable {

    /**
     * 机器人任务编号，全局唯一
     */
    @JSONField(name = "robotTaskCode")
    private String robotTaskCode;
    @JSONField(name = "triggerType")
    private String triggerType;
    @JSONField(name = "triggerCode")
    private String triggerCode;
    /**
     * 自定义扩展字段
     */
    @JSONField(name = "extra")
    private Map<String, Object> extra;
}

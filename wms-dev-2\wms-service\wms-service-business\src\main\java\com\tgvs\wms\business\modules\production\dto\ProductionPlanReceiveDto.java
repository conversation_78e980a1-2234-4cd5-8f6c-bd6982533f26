package com.tgvs.wms.business.modules.production.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 生产计划接收DTO
 */
@Data
@ApiModel("生产计划接收DTO")
public class ProductionPlanReceiveDto {

    @ApiModelProperty("计划编号")
    @NotBlank(message = "计划编号不能为空")
    private String planNo;

    @ApiModelProperty("计划日期")
    @NotNull(message = "计划日期不能为空")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date planDate;

    @ApiModelProperty("物料清单")
    @NotEmpty(message = "物料清单不能为空")
    @Valid
    private List<PlanMaterialDto> materials;

    @Data
    @ApiModel("计划物料DTO")
    public static class PlanMaterialDto {

        @ApiModelProperty("合约号")
        @NotBlank(message = "物料编码不能为空")
        private String contractNo;

        @ApiModelProperty("款号")
        @NotBlank(message = "物料编码不能为空")
        private String itemNo;

        @ApiModelProperty("物料编码")
        @NotBlank(message = "物料编码不能为空")
        private String materialCode;

        @ApiModelProperty("物料名称")
        private String materialName;

        @ApiModelProperty("物料颜色")
        private String materialColor;

        @ApiModelProperty("物料颜色编码")
        private String materialColorCode;

        @ApiModelProperty("物料规格/型号")
        private String materialModel;

        private String materialSize;


        // 物料基本信息表字段

        @ApiModelProperty("物料属性（0自身属性，1款式属性）")
        private Integer materialType =0;


        @ApiModelProperty("需求数量")
        @NotNull(message = "需求数量不能为空")
        private BigDecimal requiredQuantity;

        @ApiModelProperty("优先级")
        private Integer priority = 0;

        @ApiModelProperty("备注")
        private String remark;
    }
}
package com.tgvs.wms.business.modules.history.mapper;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tgvs.wms.business.modules.dispatch.entity.RobotDispatch;
import com.tgvs.wms.business.modules.history.entity.RobotDispatchHistory;

public interface RobotDispatchHistoryMapper extends BaseMapper<RobotDispatchHistory> {
    void updateTreeNodeStatus(@Param("id") String paramString1, @Param("status") String paramString2);

    RobotDispatch selectByTaskId(@Param("taskId") Integer paramInteger);
}

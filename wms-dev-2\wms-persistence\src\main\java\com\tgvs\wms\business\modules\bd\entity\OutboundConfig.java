package com.tgvs.wms.business.modules.bd.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.tgvs.wms.base.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@TableName("wms_outbound_type")
@ApiModel(value = "wms_outbound_type对象", description = "出库策略管理")
public class OutboundConfig extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键")
    private String id;

    @Excel(name = "策略值", width = 15.0D)
    @ApiModelProperty("策略值")
    private Integer value;

    @Excel(name = "策略类型描述", width = 15.0D)
    @ApiModelProperty("策略类型描述")
    private String text;

    @Excel(name = "区域代码", width = 15.0D)
    @ApiModelProperty("策略类型代码")
    private String code;

    @ApiModelProperty("是否默认 0-否 1-是")
    private Boolean checked;


    @ApiModelProperty("创建人")
    private String createBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("更新时间")
    private Date updateTime;

    @ApiModelProperty("删除状态")
    private Integer delFlag;
}

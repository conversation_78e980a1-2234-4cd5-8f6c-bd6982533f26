package com.tgvs.wms.business.modules.machineMaterials.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 机物料入库表
 */
@Data
@TableName("wms_machine_in_list")
@JsonIgnoreProperties(ignoreUnknown = true)
public class WmsMachineInList implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 入库单号
     */
    private String inStoreNumber;
    
    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 收料数量
     */
    private BigDecimal recQuantity;
    /**
     * 入库数量
     */
    private BigDecimal inQuantity;
    
    /**
     * 资产类别
     */
    private String assetClass;
    
    /**
     * 资产规格
     */
    private String assetModel;
    
    /**
     * 品牌
     */
    private String brand;

    /**
     * 入库优先级，优先级的值越大，入库级别越高
     */
    private Integer priority;
    
    /**
     * 入库类型（1为采购入库）
     */
    private Integer taskType;

    /*
    * 入库记录唯一标识
     */
    private String ObjectId;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    
    /**
     * 删除标志(0:未删除，1:已删除)
     */
    private Integer deleteFlag;
    /**
     * 状态（0.待入库；1.已预装；2.已入库）
     */
    private Integer status;


} 
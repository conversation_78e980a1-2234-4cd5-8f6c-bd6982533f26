package com.tgvs.wms.business.modules.container.Dto;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class materialOutBoundDetailDTO implements Serializable {
    /**
     * 箱号/托盘号
     */
    @TableField(value = "box_no")
    private String boxNo;
    /**
     * 物料编码
     */
    @TableField(value = "material_code")
    private String materialCode;

    /**
     * 格子号
     */
    @TableField(value = "grid_id")
    private Integer gridId;   /**
     /**
     * 物料数量(每格)
     */
    @TableField(value = "material_quantity")
    private BigDecimal materialQuantity;
}

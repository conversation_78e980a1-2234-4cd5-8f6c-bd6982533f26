package com.tgvs.wms.business.httpservice.baseBean.dps;


import java.util.List;

import com.alibaba.fastjson.annotation.JSONField;

public class ConfirmBean {
    @JSONField(name = "MsgType")
    private int MsgType;

    @JSO<PERSON>ield(name = "Key")
    private int Key;

    @J<PERSON><PERSON>ield(name = "Msg")
    private String Msg;

    @JSO<PERSON>ield(name = "ControllerID")
    private int ControllerID;

    @JSONField(name = "IPAddress")
    private String IPAddress;

    @JSONField(name = "Port")
    private int Port;

    @JSONField(name = "AddrAndMsgs")
    private List<AddrAndMsg> AddrAndMsgs;

    @<PERSON><PERSON><PERSON>ield(name = "ReturnAddress")
    private String ReturnAddress;

    @JSO<PERSON>ield(name = "ReturnStatus")
    private String ReturnStatus;

    @JSONField(name = "ReturnData")
    private String ReturnData;

    @JSONField(name = "ReturnRFID")
    private String ReturnRFID;

    public void setMsgType(int MsgType) {
        this.MsgType = MsgType;
    }

    public void setKey(int Key) {
        this.Key = Key;
    }

    public void setMsg(String Msg) {
        this.Msg = Msg;
    }

    public void setControllerID(int ControllerID) {
        this.ControllerID = ControllerID;
    }

    public void setIPAddress(String IPAddress) {
        this.IPAddress = IPAddress;
    }

    public void setPort(int Port) {
        this.Port = Port;
    }

    public void setAddrAndMsgs(List<AddrAndMsg> AddrAndMsgs) {
        this.AddrAndMsgs = AddrAndMsgs;
    }

    public void setReturnAddress(String ReturnAddress) {
        this.ReturnAddress = ReturnAddress;
    }

    public void setReturnStatus(String ReturnStatus) {
        this.ReturnStatus = ReturnStatus;
    }

    public void setReturnData(String ReturnData) {
        this.ReturnData = ReturnData;
    }

    public void setReturnRFID(String ReturnRFID) {
        this.ReturnRFID = ReturnRFID;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof ConfirmBean))
            return false;
        ConfirmBean other = (ConfirmBean)o;
        if (!other.canEqual(this))
            return false;
        if (getMsgType() != other.getMsgType())
            return false;
        if (getKey() != other.getKey())
            return false;
        Object this$Msg = getMsg(), other$Msg = other.getMsg();
        if ((this$Msg == null) ? (other$Msg != null) : !this$Msg.equals(other$Msg))
            return false;
        if (getControllerID() != other.getControllerID())
            return false;
        Object this$IPAddress = getIPAddress(), other$IPAddress = other.getIPAddress();
        if ((this$IPAddress == null) ? (other$IPAddress != null) : !this$IPAddress.equals(other$IPAddress))
            return false;
        if (getPort() != other.getPort())
            return false;
        List<AddrAndMsg> this$AddrAndMsgs = (List<AddrAndMsg>)getAddrAndMsgs(), other$AddrAndMsgs = (List<AddrAndMsg>)other.getAddrAndMsgs();
        if ((this$AddrAndMsgs == null) ? (other$AddrAndMsgs != null) : !this$AddrAndMsgs.equals(other$AddrAndMsgs))
            return false;
        Object this$ReturnAddress = getReturnAddress(), other$ReturnAddress = other.getReturnAddress();
        if ((this$ReturnAddress == null) ? (other$ReturnAddress != null) : !this$ReturnAddress.equals(other$ReturnAddress))
            return false;
        Object this$ReturnStatus = getReturnStatus(), other$ReturnStatus = other.getReturnStatus();
        if ((this$ReturnStatus == null) ? (other$ReturnStatus != null) : !this$ReturnStatus.equals(other$ReturnStatus))
            return false;
        Object this$ReturnData = getReturnData(), other$ReturnData = other.getReturnData();
        if ((this$ReturnData == null) ? (other$ReturnData != null) : !this$ReturnData.equals(other$ReturnData))
            return false;
        Object this$ReturnRFID = getReturnRFID(), other$ReturnRFID = other.getReturnRFID();
        return !((this$ReturnRFID == null) ? (other$ReturnRFID != null) : !this$ReturnRFID.equals(other$ReturnRFID));
    }

    protected boolean canEqual(Object other) {
        return other instanceof ConfirmBean;
    }

    public int hashCode() {
        int PRIME = 59, result = 1;
        result = result * 59 + getMsgType();
        result = result * 59 + getKey();
        Object $Msg = getMsg();
        result = result * 59 + (($Msg == null) ? 43 : $Msg.hashCode());
        result = result * 59 + getControllerID();
        Object $IPAddress = getIPAddress();
        result = result * 59 + (($IPAddress == null) ? 43 : $IPAddress.hashCode());
        result = result * 59 + getPort();
        List<AddrAndMsg> $AddrAndMsgs = (List<AddrAndMsg>)getAddrAndMsgs();
        result = result * 59 + (($AddrAndMsgs == null) ? 43 : $AddrAndMsgs.hashCode());
        Object $ReturnAddress = getReturnAddress();
        result = result * 59 + (($ReturnAddress == null) ? 43 : $ReturnAddress.hashCode());
        Object $ReturnStatus = getReturnStatus();
        result = result * 59 + (($ReturnStatus == null) ? 43 : $ReturnStatus.hashCode());
        Object $ReturnData = getReturnData();
        result = result * 59 + (($ReturnData == null) ? 43 : $ReturnData.hashCode());
        Object $ReturnRFID = getReturnRFID();
        return result * 59 + (($ReturnRFID == null) ? 43 : $ReturnRFID.hashCode());
    }

    public String toString() {
        return "ConfirmBean(MsgType=" + getMsgType() + ", Key=" + getKey() + ", Msg=" + getMsg() + ", ControllerID=" + getControllerID() + ", IPAddress=" + getIPAddress() + ", Port=" + getPort() + ", AddrAndMsgs=" + getAddrAndMsgs() + ", ReturnAddress=" + getReturnAddress() + ", ReturnStatus=" + getReturnStatus() + ", ReturnData=" + getReturnData() + ", ReturnRFID=" + getReturnRFID() + ")";
    }

    public int getMsgType() {
        return this.MsgType;
    }

    public int getKey() {
        return this.Key;
    }

    public String getMsg() {
        return this.Msg;
    }

    public int getControllerID() {
        return this.ControllerID;
    }

    public String getIPAddress() {
        return this.IPAddress;
    }

    public int getPort() {
        return this.Port;
    }

    public List<AddrAndMsg> getAddrAndMsgs() {
        return this.AddrAndMsgs;
    }

    public String getReturnAddress() {
        return this.ReturnAddress;
    }

    public String getReturnStatus() {
        return this.ReturnStatus;
    }

    public String getReturnData() {
        return this.ReturnData;
    }

    public String getReturnRFID() {
        return this.ReturnRFID;
    }
}

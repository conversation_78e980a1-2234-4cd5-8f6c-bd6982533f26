package com.tgvs.wms.business.modules.bd.controller;


import java.util.Arrays;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tgvs.wms.base.controller.BaseController;
import com.tgvs.wms.business.modules.bd.entity.Area;
import com.tgvs.wms.business.modules.bd.service.IAreaService;
import com.tgvs.wms.common.annotation.AutoLog;
import com.tgvs.wms.common.constant.Result;
import com.tgvs.wms.common.core.domain.QueryModel;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Api(tags = {"作业区域管理"})
@RestController
@RequestMapping({"/bd/area"})
@Slf4j
public class AreaController extends BaseController<Area, IAreaService> {

    @Autowired
    private IAreaService areaService;

    @AutoLog("作业区域管理-分页列表查询")
    @ApiOperation(value = "作业区域管理-分页列表查询", notes = "作业区域管理-分页列表查询")
    @PostMapping({"/list"})
    public Result<?> queryPageList(@RequestBody(required = false) QueryModel queryModel) {
        IPage<Area> list = areaService.pageList(queryModel);
        Result result = Result.ok(list.getRecords());
        result.setTotal(list.getTotal());
        return result;
    }

    @AutoLog("作业区域管理-添加")
    @ApiOperation(value = "作业区域管理-添加", notes = "作业区域管理-添加")
    @RequiresPermissions({"area:add"})
    @PostMapping({"/add"})
    public Result<?> add(@RequestBody Area area) {
        this.areaService.save(area);
        return Result.OK("添加成功！");
    }

    @AutoLog("作业区域管理-编辑")
    @ApiOperation(value = "作业区域管理-编辑", notes = "作业区域管理-编辑")
    @RequiresPermissions({"area:edit"})
    @PostMapping({"/edit"})
    public Result<?> edit(@RequestBody Area area) {
        this.areaService.updateById(area);
        return Result.OK("编辑成功!");
    }

    @AutoLog("作业区域管理-通过id删除")
    @ApiOperation(value = "作业区域管理-通过id删除", notes = "作业区域管理-通过id删除")
    @RequiresPermissions({"area:delete"})
    @PostMapping({"/delete"})
    public Result<?> delete(@RequestBody String id) {
        this.areaService.removeById(id);
        return Result.OK("删除成功!");
    }

    @AutoLog("作业区域管理-批量删除")
    @ApiOperation(value = "作业区域管理-批量删除", notes = "作业区域管理-批量删除")
    @RequiresPermissions({"area:deleteBatch"})
    @PostMapping({"/deleteBatch"})
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.areaService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    @AutoLog("作业区域管理-通过id查询")
    @ApiOperation(value = "作业区域管理-通过id查询", notes = "作业区域管理-通过id查询")
    @PostMapping({"/queryById"})
    public Result<?> queryById(@RequestBody String id)  {
        Area area = (Area)this.areaService.getById(id);
        if (area == null)
            return Result.error("未找到对应数据");
        return Result.OK(area);
    }

    @RequestMapping({"/exportXls"})
    public ModelAndView exportXls(HttpServletRequest request, Area area) {
        return exportXls(request, area, Area.class, "作业区域管理");
    }

    @RequiresPermissions({"area:importExcel"})
    @RequestMapping(value = {"/importExcel"}, method = {RequestMethod.POST})
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return importExcel(request, response, Area.class);
    }
}

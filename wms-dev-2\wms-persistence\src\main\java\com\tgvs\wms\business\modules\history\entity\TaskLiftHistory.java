package com.tgvs.wms.business.modules.history.entity;


import java.io.Serializable;
import java.util.Date;

import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.tgvs.wms.common.annotation.Dict;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TableName("wms_task_lift_history")
@ApiModel(value = "wms_task_lift_history对象", description = "提升机任务历史记录")
@Data
public class TaskLiftHistory implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("原主键")
    private String sourceId;

    @Excel(name = "任务号", width = 15.0D)
    @ApiModelProperty("任务号")
    private Integer taskId;

    @Excel(name = "执行对象", width = 15.0D, dictTable = "wms_commncation_device", dicText = "deviceName", dicCode = "deviceNo")
    @Dict(dictTable = "wms_commncation_device", dicText = "deviceName", dicCode = "deviceNo")
    @ApiModelProperty("调度执行对象")
    private String dispatcher;

    @Excel(name = "源位置", width = 20.0D, dictTable = "wms_point", dicText = "point_name", dicCode = "point_no")
    @Dict(dictTable = "wms_point", dicText = "point_name", dicCode = "point_no")
    @ApiModelProperty("源位置")
    private String fromSite;

    @Excel(name = "源楼层", width = 15.0D)
    @ApiModelProperty("源楼层")
    private Integer fromLevel;

    @Dict(dictTable = "wms_base_area", dicText = "text", dicCode = "code")
    @ApiModelProperty("源区域")
    private String fromArea;

    @Excel(name = "目的位置", width = 20.0D, dictTable = "wms_point", dicText = "point_name", dicCode = "point_no")
    @Dict(dictTable = "wms_point", dicText = "point_name", dicCode = "point_no")
    @ApiModelProperty("目的位置")
    private String toSite;

    @Excel(name = "目的楼层", width = 15.0D)
    @ApiModelProperty("目的楼层")
    private Integer toLevel;

    @Dict(dictTable = "wms_base_area", dicText = "text", dicCode = "code")
    @ApiModelProperty("目的区域")
    private String toArea;

    @Dict(dictTable = "wms_task_type", dicText = "text", dicCode = "value")
    @ApiModelProperty("任务类型")
    private Integer type;

    @Excel(name = "优先级", width = 15.0D)
    @ApiModelProperty("优先级")
    private Integer priority;

    @Dict(dicCode = "locked_status")
    @ApiModelProperty("任务锁定")
    private Integer locked;

    @Excel(name = "任务状态", width = 15.0D, dictTable = "wms_task_state", dicText = "text", dicCode = "value")
    @Dict(dictTable = "wms_task_state", dicText = "text", dicCode = "value")
    @ApiModelProperty("任务状态")
    private Integer state;

    @Excel(name = "容器号", width = 15.0D)
    @ApiModelProperty("容器号")
    private String boxNo;

    @Excel(name = "消息文本", width = 50.0D)
    @ApiModelProperty("消息文本")
    private String msg;

    @ApiModelProperty("创建人")
    private String createBy;

    @Excel(name = "创建时间", width = 20.0D, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @Excel(name = "结束时间", width = 20.0D, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("结束时间")
    private Date endTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("更新时间")
    private Date updateTime;

    @ApiModelProperty("删除状态")
    private Integer delFlag;
}

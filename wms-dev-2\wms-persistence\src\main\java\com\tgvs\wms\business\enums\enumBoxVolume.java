package com.tgvs.wms.business.enums;

import com.tgvs.wms.common.util.oConvertUtils;

public enum enumBoxVolume {
    empty(Integer.valueOf(0), "empty", "空"),
    quarter(Integer.valueOf(1), "quarter", "四分之一满"),
    half(Integer.valueOf(2), "half", "半满"),
    three_quarters(Integer.valueOf(3), "three_quarters", "四分之三满"),
    full(Integer.valueOf(4), "full", "满");

    private Integer value;

    private String code;

    private String text;

    enumBoxVolume(Integer value, String code, String text) {
        this.value = value;
        this.code = code;
        this.text = text;
    }

    public static enumBoxVolume getByValue(int value) {
        if (oConvertUtils.isEmpty(Integer.valueOf(value)))
            return null;
        for (enumBoxVolume val : values()) {
            if (val.getValue().equals(Integer.valueOf(value)))
                return val;
        }
        return null;
    }

    public Integer getValue() {
        return this.value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getText() {
        return this.text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public static enumBoxVolume toEnum(Integer Value) {
        for (enumBoxVolume e : values()) {
            if (e.getValue().equals(Value))
                return e;
        }
        return null;
    }
}

package com.tgvs.wms.business.modules.machineAuxiliary.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tgvs.wms.business.modules.machineAuxiliary.dto.AuxiliaryOutListDto;
import com.tgvs.wms.business.modules.machineAuxiliary.dto.AuxiliaryOutBoundHistoryDto;
import com.tgvs.wms.business.modules.machineAuxiliary.entity.WmsAuxiliaryOutBound;
import com.tgvs.wms.common.core.domain.QueryModel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 辅料出库数据层
 */
@Mapper
public interface WmsAuxiliaryOutboundMapper extends BaseMapper<WmsAuxiliaryOutBound> {
    
    /**
     * 分页查询辅料出库信息（返回实体类）
     * @param page 分页参数
     * @param queryModel 查询条件
     * @return 分页结果
     */
    IPage<WmsAuxiliaryOutBound> selectPageEntity(IPage<WmsAuxiliaryOutBound> page, QueryModel queryModel);
    
    /**
     * 分页查询辅料出库信息（直接返回DTO）
     * @param page 分页参数
     * @param queryModel 查询条件
     * @return 分页结果
     */
    IPage<AuxiliaryOutListDto> selectPage(IPage<AuxiliaryOutListDto> page, QueryModel queryModel);

    /**
     * 查询辅料出库历史记录分页列表（关联任务表）
     * @param page 分页对象
     * @param queryWrapper 查询条件
     * @return 分页结果
     */
    IPage<AuxiliaryOutBoundHistoryDto> selectHistoryDtoPage(IPage<AuxiliaryOutBoundHistoryDto> page, @Param("ew") QueryWrapper<AuxiliaryOutBoundHistoryDto> queryWrapper);
}
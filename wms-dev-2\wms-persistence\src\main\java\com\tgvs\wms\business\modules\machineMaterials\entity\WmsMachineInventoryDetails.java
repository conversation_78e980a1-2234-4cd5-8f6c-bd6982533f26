package com.tgvs.wms.business.modules.machineMaterials.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 机物料盘点单表明细
 */
@Data
@TableName("wms_machine_inventory_details")
public class WmsMachineInventoryDetails implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 盘点单号
     */
    private String inStoreNumber;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 盘点数量
     */
    private Integer inventoryQuantity;

    /**
     * 盘点日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date inventoryDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 删除标志(0:未删除，1:已删除)
     */
    private Integer deleteFlag;

    /**
     * 记录唯一标识
     */
    @JsonFormat(pattern = "记录唯一标识")
    private String ObjectId;

    /**
     * 盘点类型：1.正常盘点；2.财务盘点；3：异常盘点
     */
    @JsonFormat(pattern = "盘点类型：1.正常盘点；2.财务盘点；3：异常盘点")
    private Integer inventoryType;
}

package com.tgvs.wms.business.enums;

import lombok.Getter;

/**
 * 格子状态枚举
 * 用于表示格子的占用状态,包括空、四分之一、半满、四分之三、满五种状态
 */
@Getter
public enum enumGridStatus {
    
    EMPTY(0, "空", "0%"),
    QUARTER(1, "四分之一", "25%"),
    HALF(2, "半满", "50%"),
    THREE_QUARTERS(3, "四分之三", "75%"),
    FULL(4, "满", "100%");
    
    /**
     * 状态值
     */
    private final int value;
    
    /**
     * 状态描述
     */
    private final String description;
    
    /**
     * 容量百分比
     */
    private final String percentage;
    
    enumGridStatus(int value, String description, String percentage) {
        this.value = value;
        this.description = description;
        this.percentage = percentage;
    }
    
    /**
     * 根据状态值获取枚举
     * @param value 状态值
     * @return 对应的枚举值,如果不存在则返回null
     */
    public static enumGridStatus getByValue(int value) {
        for (enumGridStatus status : values()) {
            if (status.getValue() == value) {
                return status;
            }
        }
        return null;
    }
    
    /**
     * 根据容量百分比获取状态
     * @param percentage 容量百分比(0-100)
     * @return 对应的状态枚举
     */
    public static enumGridStatus getByPercentage(int percentage) {
        if (percentage <= 0) {
            return EMPTY;
        } else if (percentage <= 25) {
            return QUARTER;
        } else if (percentage <= 50) {
            return HALF;
        } else if (percentage <= 75) {
            return THREE_QUARTERS;
        } else {
            return FULL;
        }
    }
    
    /**
     * 判断是否为空
     * @return true表示空,false表示非空
     */
    public boolean isEmpty() {
        return this == EMPTY;
    }
    
    /**
     * 判断是否为满
     * @return true表示满,false表示未满
     */
    public boolean isFull() {
        return this == FULL;
    }
    
    /**
     * 判断是否有剩余空间
     * @return true表示有剩余空间,false表示已满
     */
    public boolean hasSpace() {
        return this != FULL;
    }
    
    /**
     * 获取下一个状态
     * @return 下一个状态,如果当前是满状态则返回满状态
     */
    public enumGridStatus next() {
        if (this == FULL) {
            return FULL;
        }
        return getByValue(this.value + 1);
    }
    
    /**
     * 获取上一个状态
     * @return 上一个状态,如果当前是空状态则返回空状态
     */
    public enumGridStatus previous() {
        if (this == EMPTY) {
            return EMPTY;
        }
        return getByValue(this.value - 1);
    }

    /**
     * 判断当前状态是否大于等于指定状态
     * @param other 要比较的状态
     * @return true表示当前状态大于等于指定状态
     */
    public boolean isGreaterThanOrEqual(enumGridStatus other) {
        return this.value >= other.value;
    }

    /**
     * 判断当前状态是否小于等于指定状态
     * @param other 要比较的状态
     * @return true表示当前状态小于等于指定状态
     */
    public boolean isLessThanOrEqual(enumGridStatus other) {
        return this.value <= other.value;
    }

    /**
     * 获取当前状态的容量百分比数值
     * @return 容量百分比数值(0-100)
     */
    public int getPercentageValue() {
        return Integer.parseInt(this.percentage.replace("%", ""));
    }

    /**
     * 判断是否可以放入指定百分比容量的货物
     * @param percentage 要放入的货物容量百分比
     * @return true表示可以放入,false表示无法放入
     */
    public boolean canPut(int percentage) {
        return (this.getPercentageValue() + percentage) <= 100;
    }

    /**
     * 获取放入指定百分比容量后的新状态
     * @param percentage 要放入的货物容量百分比
     * @return 放入后的新状态
     */
    public enumGridStatus put(int percentage) {
        int newPercentage = this.getPercentageValue() + percentage;
        return getByPercentage(newPercentage);
    }

    /**
     * 获取取出指定百分比容量后的新状态
     * @param percentage 要取出的货物容量百分比
     * @return 取出后的新状态
     */
    public enumGridStatus take(int percentage) {
        int newPercentage = Math.max(0, this.getPercentageValue() - percentage);
        return getByPercentage(newPercentage);
    }
} 
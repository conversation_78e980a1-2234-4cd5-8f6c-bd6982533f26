package com.tgvs.wms.business.httpservice.baseBean.mes;


import com.alibaba.fastjson.annotation.JSONField;

public class BoxArriveBean {
    @J<PERSON>NField(name = "OrderNo")
    private Integer OrderNo;

    @J<PERSON>NField(name = "DepSiteNo")
    private String DepSiteNo;

    @JSONField(name = "DestSiteNo")
    private String DestSiteNo;

    @J<PERSON>NField(name = "BoxNo")
    private String BoxNo;

    @J<PERSON>NField(name = "IsEmpty")
    private int IsEmpty;

    public void setOrderNo(Integer OrderNo) {
        this.OrderNo = OrderNo;
    }

    public void setDepSiteNo(String DepSiteNo) {
        this.DepSiteNo = DepSiteNo;
    }

    public void setDestSiteNo(String DestSiteNo) {
        this.DestSiteNo = DestSiteNo;
    }

    public void setBoxNo(String BoxNo) {
        this.BoxNo = BoxNo;
    }

    public void setIsEmpty(int IsEmpty) {
        this.IsEmpty = IsEmpty;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof BoxArriveBean))
            return false;
        BoxArriveBean other = (BoxArriveBean)o;
        if (!other.canEqual(this))
            return false;
        Object this$OrderNo = getOrderNo(), other$OrderNo = other.getOrderNo();
        if ((this$OrderNo == null) ? (other$OrderNo != null) : !this$OrderNo.equals(other$OrderNo))
            return false;
        Object this$DepSiteNo = getDepSiteNo(), other$DepSiteNo = other.getDepSiteNo();
        if ((this$DepSiteNo == null) ? (other$DepSiteNo != null) : !this$DepSiteNo.equals(other$DepSiteNo))
            return false;
        Object this$DestSiteNo = getDestSiteNo(), other$DestSiteNo = other.getDestSiteNo();
        if ((this$DestSiteNo == null) ? (other$DestSiteNo != null) : !this$DestSiteNo.equals(other$DestSiteNo))
            return false;
        Object this$BoxNo = getBoxNo(), other$BoxNo = other.getBoxNo();
        return ((this$BoxNo == null) ? (other$BoxNo != null) : !this$BoxNo.equals(other$BoxNo)) ? false : (!(getIsEmpty() != other.getIsEmpty()));
    }

    protected boolean canEqual(Object other) {
        return other instanceof BoxArriveBean;
    }

    public int hashCode() {
        int PRIME = 59,
        result = 1;
        Object $OrderNo = getOrderNo();
        result = result * 59 + (($OrderNo == null) ? 43 : $OrderNo.hashCode());
        Object $DepSiteNo = getDepSiteNo();
        result = result * 59 + (($DepSiteNo == null) ? 43 : $DepSiteNo.hashCode());
        Object $DestSiteNo = getDestSiteNo();
        result = result * 59 + (($DestSiteNo == null) ? 43 : $DestSiteNo.hashCode());
        Object $BoxNo = getBoxNo();
        result = result * 59 + (($BoxNo == null) ? 43 : $BoxNo.hashCode());
        return result * 59 + getIsEmpty();
    }

    public String toString() {
        return "BoxArriveBean(OrderNo=" + getOrderNo() + ", DepSiteNo=" + getDepSiteNo() + ", DestSiteNo=" + getDestSiteNo() + ", BoxNo=" + getBoxNo() + ", IsEmpty=" + getIsEmpty() + ")";
    }

    public Integer getOrderNo() {
        return this.OrderNo;
    }

    public String getDepSiteNo() {
        return this.DepSiteNo;
    }

    public String getDestSiteNo() {
        return this.DestSiteNo;
    }

    public String getBoxNo() {
        return this.BoxNo;
    }

    public int getIsEmpty() {
        return this.IsEmpty;
    }
}

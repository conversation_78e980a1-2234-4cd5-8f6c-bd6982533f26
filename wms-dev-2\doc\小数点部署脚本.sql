SET FOREIGN_KEY_CHECKS=0;

ALTER TABLE `wms`.`wms_auxiliary_detail` MODIFY COLUMN `quantity` decimal(10, 2) NULL DEFAULT NULL COMMENT '数量' AFTER `material_model`;

ALTER TABLE `wms`.`wms_auxiliary_detail` MODIFY COLUMN `base_quantity` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '基础单位数量' AFTER `prebox_id`;

ALTER TABLE `wms`.`wms_box_item` MODIFY COLUMN `material_quantity` decimal(10, 2) NULL DEFAULT NULL COMMENT '物料数量' AFTER `material_size`;

ALTER TABLE `wms`.`wms_machine_in_list` MODIFY COLUMN `in_quantity` decimal(10, 2) NOT NULL COMMENT '入库数量' AFTER `material_name`;

ALTER TABLE `wms`.`wms_machine_in_list` MODIFY COLUMN `rec_quantity` decimal(10, 2) NOT NULL COMMENT '来料数量' AFTER `delete_flag`;

ALTER TABLE `wms`.`wms_machine_inventory_details` MODIFY COLUMN `inventory_quantity` decimal(10, 2) NULL DEFAULT NULL COMMENT '盘点数量' AFTER `material_name`;

ALTER TABLE `wms`.`wms_machine_inventory_record` MODIFY COLUMN `inventory_quantity` decimal(10, 2) NULL DEFAULT NULL COMMENT '盘点数量' AFTER `material_name`;

ALTER TABLE `wms`.`wms_machine_out_detail_list` MODIFY COLUMN `out_quantity` decimal(10, 2) NOT NULL COMMENT '出库数量' AFTER `material_code`;

ALTER TABLE `wms`.`wms_machine_out_detail_record` MODIFY COLUMN `out_quantity` decimal(10, 2) NOT NULL COMMENT '出库数量' AFTER `material_name`;

ALTER TABLE `wms`.`wms_machinematerials_box_pre_packing` MODIFY COLUMN `original_quantity` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '原数量' AFTER `brand`;

ALTER TABLE `wms`.`wms_machinematerials_box_pre_packing` MODIFY COLUMN `pending_quantity` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '待入库数量' AFTER `original_quantity`;

ALTER TABLE `wms`.`wms_machinematerials_box_pre_packing` MODIFY COLUMN `actual_quantity` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '实际入库数量' AFTER `pending_quantity`;

SET FOREIGN_KEY_CHECKS=1;
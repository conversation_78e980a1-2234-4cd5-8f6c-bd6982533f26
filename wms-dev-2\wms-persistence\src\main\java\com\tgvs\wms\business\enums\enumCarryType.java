package com.tgvs.wms.business.enums;

import com.tgvs.wms.common.util.oConvertUtils;

public enum enumCarryType {
    dolly_to_cache(Integer.valueOf(1001), "dolly_to_cache", "松布架到缓存货位"),
    cache_to_dolly(Integer.valueOf(1002), "cache_to_dolly", "缓存货位到松布架"),
    site_to_dolly(Integer.valueOf(2001), "site_to_dolly", "工位到松布架"),
    dolly_to_site(Integer.valueOf(2002), "dolly_to_site", "松布架到工位"),
    site_to_cache(Integer.valueOf(3001), "site_to_cache", "工位到缓存货位"),
    cache_to_site(Integer.valueOf(3002), "cache_to_site", "缓存货位到工位"),
    cache_to_cache(Integer.valueOf(5000), "cache_to_cache", "缓存货位之间"),
    dolly_to_dolly(Integer.valueOf(6000), "dolly_to_dolly", "松布架之间");

    private Integer value;

    private String code;

    private String text;

    enumCarryType(Integer value, String code, String text) {
        this.value = value;
        this.code = code;
        this.text = text;
    }

    public static enumCarryType getByValue(int value) {
        if (oConvertUtils.isEmpty(Integer.valueOf(value)))
            return null;
        for (enumCarryType val : values()) {
            if (val.getValue().equals(Integer.valueOf(value)))
                return val;
        }
        return null;
    }

    public Integer getValue() {
        return this.value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getText() {
        return this.text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public static enumCarryType toEnum(Integer Value) {
        for (enumCarryType e : values()) {
            if (e.getValue().equals(Value))
                return e;
        }
        return null;
    }
}

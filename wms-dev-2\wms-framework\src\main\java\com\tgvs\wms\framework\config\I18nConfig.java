package com.tgvs.wms.framework.config;

import java.util.Locale;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.i18n.LocaleChangeInterceptor;
import org.springframework.web.servlet.i18n.SessionLocaleResolver;

/**
 * 资源文件配置加载
 *
 * <AUTHOR>
 */
@Configuration
public class I18nConfig implements WebMvcConfigurer
{
    @Bean
    public LocaleResolver localeResolver()
    {
        SessionLocaleResolver slr = new SessionLocaleResolver();
        // 默认语言
        slr.setDefaultLocale(Locale.SIMPLIFIED_CHINESE);
        return slr;
    }

    @Bean
    public LocaleChangeInterceptor localeChangeInterceptor()
    {
        LocaleChangeInterceptor lci = new LocaleChangeInterceptor();
        // 参数名
        lci.setParamName("lang");
        return lci;
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry)
    {
        registry.addInterceptor(localeChangeInterceptor());
    }

    /**
     * 静态资源处理器配置
     * 配置webapp目录下的静态资源映射
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 配置静态资源映射 - 从webapp目录
        registry.addResourceHandler("/css/**")
                .addResourceLocations("classpath:/static/css/", "/css/");

        registry.addResourceHandler("/js/**")
                .addResourceLocations("classpath:/static/js/", "/js/");

        registry.addResourceHandler("/lib/**")
                .addResourceLocations("classpath:/static/lib/", "/lib/");

        registry.addResourceHandler("/images/**")
                .addResourceLocations("classpath:/static/images/", "/images/");

        registry.addResourceHandler("/fonts/**")
                .addResourceLocations("classpath:/static/fonts/", "/fonts/");

        registry.addResourceHandler("/api/**")
                .addResourceLocations("classpath:/static/api/", "/api/");

        // 配置页面资源
        registry.addResourceHandler("/pages/**")
                .addResourceLocations("classpath:/static/pages/", "/pages/");

        // 配置根目录下的HTML文件
        registry.addResourceHandler("/*.html")
                .addResourceLocations("classpath:/static/", "/");

        // 添加默认的静态资源处理
        registry.addResourceHandler("/**")
                .addResourceLocations("classpath:/META-INF/resources/",
                                    "classpath:/resources/",
                                    "classpath:/static/",
                                    "classpath:/public/",
                                    "/");
    }
}
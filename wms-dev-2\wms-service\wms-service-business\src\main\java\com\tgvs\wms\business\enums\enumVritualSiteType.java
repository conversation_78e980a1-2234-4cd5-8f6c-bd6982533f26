package com.tgvs.wms.business.enums;


import com.tgvs.wms.common.util.oConvertUtils;

public enum enumVritualSiteType {
    songbu(Integer.valueOf(15), "songbu", "松布"),
    cutting(Integer.valueOf(20), "cutting", "裁剪"),
    cache(Integer.valueOf(30), "cache", "缓存架"),
    surplus(Integer.valueOf(50), "surplus", "余布处理节点");

    private Integer value;

    private String code;

    private String text;

    enumVritualSiteType(Integer value, String code, String text) {
        this.value = value;
        this.code = code;
        this.text = text;
    }

    public static enumVritualSiteType getByValue(int value) {
        if (oConvertUtils.isEmpty(Integer.valueOf(value)))
            return null;
        for (enumVritualSiteType val : values()) {
            if (val.getValue().equals(Integer.valueOf(value)))
                return val;
        }
        return null;
    }

    public Integer getValue() {
        return this.value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getText() {
        return this.text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public static enumVritualSiteType toEnum(Integer Value) {
        for (enumVritualSiteType e : values()) {
            if (e.getValue().equals(Value))
                return e;
        }
        return null;
    }
}

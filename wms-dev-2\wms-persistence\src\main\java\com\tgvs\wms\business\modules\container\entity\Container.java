package com.tgvs.wms.business.modules.container.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tgvs.wms.base.entity.BaseEntity;
import io.swagger.annotations.ApiModel;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;



@EqualsAndHashCode(callSuper = true)
@TableName ( "wms_box" )
@ApiModel(value = "wms_box对象", description = "容器管理")
@Data
public class Container extends BaseEntity {

	private static final long serialVersionUID =  7645464523088940684L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private String id;

	/**
	 * 物料料类型：1、辅料/2、机物料;0：空料箱或者空托盘
	 */
	@TableField(value = "material_type")
	private Integer materialType;

	/**
	 * 容器号
	 */
	@TableField(value = "box_no")
	private String boxNo;

	/**
	 * 合约号
	 */
	@TableField(value = "contract_no")
	private String contractNo;

	/**
	 * 容器类型：1-料箱 2-托盘
	 */
	@TableField(value = "box_type")
	private Integer boxType;

	/**
	 * 容器类型：1-一格箱 2-两格箱 3-三格箱 4-四格箱 6-六格箱 8-八格箱
	 */
	@TableField(value = "box_container_type")
	private Integer boxContainerType;

	/**
	 * 已使用多少个格子
	 */
	@TableField(value = "box_empty_status")
	private Integer boxEmptyStatus;

	/**
	 * 容器状态：0-空容器 1-未满容器 2-满容器
	 */
	@TableField(value = "box_empty")
	private Integer boxEmpty;

	/**
	 * 创建人
	 */
	@TableField(value = "create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time")
	private Date createTime;

	/**
	 * 更新人
	 */
	@TableField(value = "update_by")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time")
	private Date updateTime;

	/**
	 * 删除标志
	 */
	@TableField(value = "delete_flag")
	private Long deleteFlag;

	/**
	 * 版本
	 */
    private Integer version;
	
	

}

package com.tgvs.wms.business.modules.storage.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tgvs.wms.base.entity.BaseEntity;
import com.tgvs.wms.common.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;

@TableName("wms_box_shelf")
@ApiModel(value = "wms_box_shelf对象", description = "货位管理")
@Data
public class Shelf extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @Excel(name = "货位编码", width = 15.0D)
    @ApiModelProperty("货位编码")
    private String code;

    @Excel(name = "机器人站点编码", width = 15.0D)
    @ApiModelProperty("机器人站点编码")
    private String robotCode;

    @Excel(name = "库位类型", width = 15.0D, dicCode = "location_type")
    @Dict(dicCode = "location_type")
    @ApiModelProperty("库位类型:1-料箱 2-托盘 3-虚拟库位")
    private Integer boxType;

    @Excel(name = "层位", width = 15.0D)
    @ApiModelProperty("层位")
    private Integer level;

    @Excel(name = "行位", width = 15.0D)
    @ApiModelProperty("行位")
    @TableField("`row`")
    private Integer row;

    @Excel(name = "列位", width = 15.0D)
    @ApiModelProperty("列位")
    @TableField("`column`")
    private Integer column;

    @Excel(name = "区域", width = 15.0D)
    @ApiModelProperty("区域")
    private String area;

    @Excel(name = "货位锁定", width = 15.0D, dicCode = "locked_status")
    @Dict(dicCode = "locked")
    @ApiModelProperty("货位锁定 0-未锁定 1-锁定")
    private Integer locked;

    @Excel(name = "货位状态", width = 15.0D, dicCode = "location_status")
    @Dict(dicCode = "status")
    @ApiModelProperty("货位状态 0-空闲 1-占用")
    private Integer state;

    @Excel(name = "容器号", width = 15.0D)
    @ApiModelProperty("容器号")
    private String boxNo;

    @Excel(name = "是否删除", width = 15.0D)
    @ApiModelProperty("容器号")
    private Integer deleteFlag;
}

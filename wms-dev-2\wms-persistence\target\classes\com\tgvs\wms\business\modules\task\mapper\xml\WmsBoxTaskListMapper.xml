<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tgvs.wms.business.modules.task.mapper.WmsBoxTaskListMapper">



    <select id="selectOutTaskList" resultType="com.tgvs.wms.business.modules.task.entity.WmsBoxTaskList">
        select *
        from wms_box_task_list
        <where>
            grid_status lt;='2'

        </where>
        order by create_time desc
    </select>
    <select id="getNextTaskOrderSequenceValue" resultType="java.lang.Long">
        SELECT getNextVal(#{sequenceName}) AS next_val
    </select>
</mapper>

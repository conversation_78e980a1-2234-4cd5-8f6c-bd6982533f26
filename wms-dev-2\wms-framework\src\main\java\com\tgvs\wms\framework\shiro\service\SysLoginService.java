package com.tgvs.wms.framework.shiro.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.tgvs.wms.common.constant.Constants;
import com.tgvs.wms.common.constant.ShiroConstants;
import com.tgvs.wms.common.constant.UserConstants;
import com.tgvs.wms.common.enums.UserStatus;
import com.tgvs.wms.common.exception.user.CaptchaException;
import com.tgvs.wms.common.exception.user.UserBlockedException;
import com.tgvs.wms.common.exception.user.UserDeleteException;
import com.tgvs.wms.common.exception.user.UserNotExistsException;
import com.tgvs.wms.common.exception.user.UserPasswordNotMatchException;
import com.tgvs.wms.common.util.DateUtils;
import com.tgvs.wms.common.util.MessageUtils;
import com.tgvs.wms.common.util.ServletUtils;
import com.tgvs.wms.common.util.ShiroUtils;
import com.tgvs.wms.common.util.StringUtils;
import com.tgvs.wms.framework.manager.AsyncManager;
import com.tgvs.wms.framework.manager.factory.AsyncFactory;
import com.tgvs.wms.system.entity.SysUser;
import com.tgvs.wms.system.service.system.SysUserService;

/**
 * 登录校验方法
 * 
 * <AUTHOR>
 */
@Component
public class SysLoginService
{
    @Autowired
    private SysPasswordService passwordService;
//
    @Autowired
    private SysUserService userService;

    /**
     * 登录
     */
    public SysUser login(String username, String password)
    {
        // 验证码校验
        if (ShiroConstants.CAPTCHA_ERROR.equals(ServletUtils.getRequest().getAttribute(ShiroConstants.CURRENT_CAPTCHA))){
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.error")));
            throw new CaptchaException();
        }
        // 用户名或密码为空 错误
        if (StringUtils.isEmpty(username) || StringUtils.isEmpty(password)){
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("not.null")));
            throw new UserNotExistsException();
        }
        // 密码如果不在指定范围内 错误
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH){
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
            throw new UserPasswordNotMatchException();
        }

        // 用户名不在指定范围内 错误
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH){
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
            throw new UserPasswordNotMatchException();
        }

        // 查询用户信息
        SysUser user = userService.getByUserName(username);

        //用户不存在
        if (user == null){
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.not.exists")));
            throw new UserNotExistsException();
        }

        //用户已被删除
        if (UserStatus.DELETED.getCode().equals(user.getDelFlag())){
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.delete")));
            throw new UserDeleteException();
        }

        //用户已冻结
        if (UserStatus.DISABLE.getCode().equals(user.getStatus())){
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.blocked")));
            throw new UserBlockedException();
        }
        //校验密码
        passwordService.validate(user, password);
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));

        //更新用户登录信息
        userService.updateUserLogin(user.getId(), ShiroUtils.getIp(), DateUtils.getNowDate());

        return user;
    }
}

package com.tgvs.wms.business.modules.machineMaterials.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tgvs.wms.business.modules.machineMaterials.entity.WmsMachineInfo;
import com.tgvs.wms.business.modules.machineMaterials.vo.WmsMachineInfoConvergeVo;
import com.tgvs.wms.business.modules.machineMaterials.vo.WmsMachineInfoVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 机物料基础信息Mapper接口
 * 对于基本的CRUD操作，直接使用MyBatis-Plus的BaseMapper提供的方法：
 * - 插入: insert
 * - 更新: updateById
 * - 删除: deleteById
 * - ID查询: selectById
 * - 列表查询: selectList
 * - 批量ID查询: selectBatchIds
 */
@Mapper
public interface MaterialInfoMapper extends BaseMapper<WmsMachineInfo> {
    
    /**
     * 自定义条件查询机物料基础信息
     * 
     * @param wmsMachineInfo 查询条件
     * @return 机物料基础信息列表
     */
    List<WmsMachineInfo> selectWmsMaterialInfoCustom(WmsMachineInfo wmsMachineInfo);
    
    /**
     * 批量逻辑删除机物料基础信息
     * 
     * @param ids 主键ID数组
     * @return 影响行数
     */
    int deleteWmsMaterialInfoByIds(String[] ids);

    /**
     * 根据物料编码查询库存数据条数
     * @param materialCode
     * @return
     */
    WmsMachineInfoVo selectItemNumber(String materialCode);
    /**
     * 根据物料编码查询可以合箱处理的库存数据
     * @param materialCode
     * @return
     */
    List<WmsMachineInfoConvergeVo>selectConvergeBoxList(String materialCode);
} 
package com.tgvs.wms.business.modules.machineMaterials.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tgvs.wms.base.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Data;


@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName ( "wms_machine_out_detail_list" )
@Data
public class WmsMachineOutDetailList  extends BaseEntity {

//	private static final long serialVersionUID = 1L;
	private static final long serialVersionUID =  1125167523697984290L;

	/**
	 * 出库单号
	 */
   	@TableField(value = "out_store_number")
	private String outStoreNumber;

	/**
	 * 物料编码
	 */
   	@TableField(value = "material_code")
	private String materialCode;

	/**
	 * 出库数量
	 */
   	@TableField(value = "out_quantity")
	private BigDecimal outQuantity;

	/**
	 * 容器号
	 */
	@TableField(value = "box_no")
	private String boxNo;

	/**
	 * 状态：1.待出库；2.已出库
	 */
	@TableField(value = "status")
	private Integer status;

	/**
	 * 格号
	 */
	@TableField(value = "grid_no")
	private Integer gridNo;

	/**
	 * 唯一标识符
	 */
	@TableField(value = "object_id")
	@ApiModelProperty("唯一标识符")
	private String objectId;

	/**
	 * 是否删除：0.正常；1.删除
	 */
	@TableField(value = "delete_flag")
	@ApiModelProperty("是否删除")
	private Integer deleteFlag;
}

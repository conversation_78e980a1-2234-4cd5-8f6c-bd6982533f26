package com.tgvs.wms.business.modules.container.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tgvs.wms.business.modules.container.entity.Container;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ContainerMapper extends BaseMapper<Container> {
    /**
     *
     * @param boxNo 查询条件
     *              根据箱号获取容器信息
     */
    Container selectBoxNo(String boxNo);

    /**
     *
     * @param materialType 查询条件
     *                     获取机物料未满箱库存
     */
    List<Container> selectContainerType(int materialType, int boxEmpty, int boxContainerType);

    /**
     * 根据多种条件查询容器类型列表
     * 
     * @param boxType          容器类型：1-料箱 2-托盘
     * @param materialType     物料类型：1-辅料 2-机物料 0-空容器
     * @param boxEmpty         容器状态：0-空容器 1-未满容器 2-满容器
     * @param boxContainerType 容器格数：1-一格箱 2-两格箱 等
     * @param contractNo       合约号
     * @param boxNo            容器编号（支持模糊查询）
     * @param hasShelf         是否有库位：true-有有效库位 false-无库位 null-不限制
     * @param area             区域代码
     * @param limit            限制返回数量
     * @return 符合条件的容器列表
     */
    List<Container> selectContainerTypeList(
            @Param("boxType") Integer boxType,
            @Param("materialType") Integer materialType,
            @Param("boxEmpty") Integer boxEmpty,
            @Param("boxContainerType") Integer boxContainerType,
            @Param("contractNo") String contractNo,
            @Param("boxNo") String boxNo,
            @Param("hasShelf") Boolean hasShelf,
            @Param("area") String area,
            @Param("limit") Integer limit);

    /**
     *找对应类型空箱
     * @param boxContainerType 查询条件
     *                         找对应空箱
     */
    List<Container> selectEmptyBoxList(int boxContainerType);

    /**
     * 找对应类型未满箱
     * @param boxContainerType
     * @return
     */
    List<Container> selectNotFullBoxList(int boxContainerType);

    /**
     * 查询空料架
     * @return
     */
    List<Container> selectEmptyTray();

    /**
     * 根据区域查询空容器列表 (通过 JOIN shelf 表实现)
     *
     * @param zone 区域代码
     * @return 指定区域内的空容器列表
     */
    List<Container> selectEmptyContainersByZone(@Param("zone") String zone);

    List<Container> selectContainerShelfByBoxNo(@Param("boxNo") String boxNo);

    /**
     * 检查是否有可用的空容器（辅料用）
     * 直接通过SQL关联查询库位和容器表
     * 
     * @param containerTypeValue 容器类型：1-料箱 2-托盘
     * @return 可用空容器数量
     */
    Integer checkAvailableEmptyContainersCount(@Param("containerTypeValue") Integer containerTypeValue);

    /**
     * 查询可用的空容器列表（辅料用）
     * 直接通过SQL关联查询库位和容器表
     * 
     * @param containerTypeValue 容器类型：1-料箱 2-托盘
     * @param limit              限制返回数量
     * @return 可用空容器列表
     */
    List<Container> selectAvailableEmptyContainers(
            @Param("containerTypeValue") Integer containerTypeValue,
            @Param("limit") Integer limit);
}

package com.tgvs.wms.business.modules.machineMaterials.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tgvs.wms.base.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 机物料出库实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wms_machine_out_list")
public class WmsMachineOutbound extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    @ApiModelProperty("主键ID")
    private String id;

    /**
     * 出库单号
     */
    @ApiModelProperty("出库单号")
    private String outStoreNumber;

    /**
     * 出库优先级，优先级的值越大，出库级别越高
     */
    @ApiModelProperty("出库优先级，优先级的值越大，出库级别越高")
    private Integer priority;
    
    /**
     * 删除标志(0:未删除，1:已删除)
     */
    @ApiModelProperty("删除标志(0:未删除，1:已删除)")
    private Integer deleteFlag;

    /**
     * 唯一标识符
     */
    @ApiModelProperty("唯一标识符")
    private String objectId;

    /**
     * 出库类型:1.领料出库；2.补单出库
     */
    @ApiModelProperty("出库类型:1.领料出库；2.补单出库")
    private Integer type;

    /**
     * 状态：0.创建；1.执行中；2.完成
     */
    @ApiModelProperty("出库状态：0.创建；1.执行中；2.完成")
    private Integer status;
} 
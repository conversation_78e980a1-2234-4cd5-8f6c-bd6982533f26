package com.tgvs.wms.business.modules.warehouse.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.tgvs.wms.base.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * 仓库信息表实体
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("wms_warehouse_info") // Define table name explicitly
public class WarehouseInfo extends BaseEntity {



    /**
     * 仓库编码 (唯一, 非空)
     */
    @TableField("code")
    private String code;

    /**
     * 仓库名称 (非空)
     */
    @TableField("name")
    private String name;

    /**
     * 仓库地址
     */
    @TableField("address")
    private String address;

    /**
     * 仓库状态 (1:启用, 0:禁用)
     * Added default value via columnDefinition
     */
    @TableField("status")
    private Integer status;

    /**
     * 联系人
     */
    @TableField("contact_person")
    private String contactPerson;

    /**
     * 联系电话
     */
    @TableField("contact_phone")
    private String contactPhone;

    /**
     * 是否为当前系统管理的仓库 (1:是, 0:否)
     */
    @TableField("is_store")
    private Integer isStore;



}
package com.tgvs.wms.business.httpservice.baseBean.warehouse;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

public class AdjustResult {
    private int id;

    public void setId(int id) {
        this.id = id;
    }

    public void setMessageName(String messageName) {
        this.messageName = messageName;
    }

    public void setWmsId(String wmsId) {
        this.wmsId = wmsId;
    }

    public void setResult(AdjustLocation result) {
        this.result = result;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof AdjustResult))
            return false;
        AdjustResult other = (AdjustResult)o;
        if (!other.canEqual(this))
            return false;
        if (getId() != other.getId())
            return false;
        Object this$messageName = getMessageName(), other$messageName = other.getMessageName();
        if ((this$messageName == null) ? (other$messageName != null) : !this$messageName.equals(other$messageName))
            return false;
        Object this$wmsId = getWmsId(), other$wmsId = other.getWmsId();
        if ((this$wmsId == null) ? (other$wmsId != null) : !this$wmsId.equals(other$wmsId))
            return false;
        Object this$result = getResult(), other$result = other.getResult();
        return !((this$result == null) ? (other$result != null) : !this$result.equals(other$result));
    }

    protected boolean canEqual(Object other) {
        return other instanceof AdjustResult;
    }

    public int hashCode() {
        int PRIME = 59,
        result = 1;
        result = result * 59 + getId();
        Object $messageName = getMessageName();
        result = result * 59 + (($messageName == null) ? 43 : $messageName.hashCode());
        Object $wmsId = getWmsId();
        result = result * 59 + (($wmsId == null) ? 43 : $wmsId.hashCode());
        Object $result = getResult();
        return result * 59 + (($result == null) ? 43 : $result.hashCode());
    }

    public String toString() {
        return "AdjustResult(id=" + getId() + ", messageName=" + getMessageName() + ", wmsId=" + getWmsId() + ", result=" + getResult() + ")";
    }

    public int getId() {
        return this.id;
    }

    private String messageName = "taskAdjust";

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String wmsId;

    @JsonProperty("result")
    private AdjustLocation result;

    public String getMessageName() {
        return this.messageName;
    }

    public String getWmsId() {
        return this.wmsId;
    }

    public AdjustLocation getResult() {
        return this.result;
    }
}

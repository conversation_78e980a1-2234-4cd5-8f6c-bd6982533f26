package com.tgvs.wms.business.modules.config.entity;

import java.io.Serializable;
import java.util.Date;

import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.tgvs.wms.common.annotation.Dict;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TableName("wms_shelf_config")
@ApiModel(value = "wms_shelf_config对象", description = "设备运行维护")
@Data
public class ManageConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键")
    private String id;

    @Excel(name = "设备ID号", width = 15.0D)
    @ApiModelProperty("设备ID号")
    private Integer value;

    @Excel(name = "名称", width = 15.0D)
    @ApiModelProperty("名称")
    private String text;

    @Excel(name = "关联设备", width = 15.0D, dictTable = "wms_point", dicText = "point_name", dicCode = "point_no")
    @Dict(dictTable = "wms_point", dicText = "point_name", dicCode = "point_no")
    @ApiModelProperty("关联设备")
    private String code;

    @Excel(name = "出入类型", width = 15.0D, dicCode = "bound_site")
    @Dict(dicCode = "bound_site")
    @ApiModelProperty("出入类型")
    private Integer type;

    @Excel(name = "启用状态", width = 15.0D, dicCode = "depart_status")
    @Dict(dicCode = "depart_status")
    @ApiModelProperty("启用状态")
    private Integer status;

    @Excel(name = "所属系统", width = 15.0D, dictTable = "wms_commncation_config", dicText = "sysname", dicCode = "sysid")
    @Dict(dictTable = "wms_commncation_config", dicText = "sysname", dicCode = "sysid")
    @ApiModelProperty("所属系统")
    private String sysid;

    @ApiModelProperty("创建人")
    private String createBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("更新时间")
    private Date updateTime;

    @Excel(name = "更新时间", width = 15.0D, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("更新时间")
    private Date endTime;

    @Excel(name = "删除状态", width = 15.0D)
    @ApiModelProperty("删除状态")
    private Integer delFlag;
}

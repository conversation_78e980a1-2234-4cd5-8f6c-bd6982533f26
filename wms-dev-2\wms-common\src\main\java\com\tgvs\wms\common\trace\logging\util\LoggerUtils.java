package com.tgvs.wms.common.trace.logging.util;

import java.util.Map;

import org.slf4j.MDC;

import ch.qos.logback.classic.spi.ILoggingEvent;

public class LoggerUtils {

    public static final String MSG_KEY = "Message";

    private static final String KEY_PREFIX = "serviceContext.";
    private static final String KEY_REQUEST_ID = "requestId";

    public static String getRequestIdFromEvent(ILoggingEvent event){
        return doGet(event, KEY_PREFIX + KEY_REQUEST_ID);
    }

    private static String doGet(ILoggingEvent event, String key){
        Map<String, String> mdcMap = event.getMDCPropertyMap();
        if(mdcMap == null){
            return null;
        }
        return mdcMap.get(key);
    }

    public static String quote(String string){
        if(string == null || string.length() == 0){
            return "\"\"";
        }
        char c = 0;
        int i;
        int len = string.length();
        StringBuilder sb = new StringBuilder(len +4);
        String t;

        sb.append('"');
        for(i = 0 ; i < len; i += 1){
            c = string.charAt(i);
            switch (c){
                case '\\':
                case '"':
                case '/':
                    sb.append('\\');
                    sb.append(c);
                    break;
                case '\b':
                    sb.append("\\b");
                    break;
                case '\t':
                    sb.append("\\t");
                    break;
                case '\n':
                    sb.append("\\n");
                    break;
                case '\f':
                    sb.append("\\f");
                    break;
                case '\r':
                    sb.append("\\r");
                    break;
                default:
                    if(c < ' '){
                        t = "000" + Integer.toHexString(c);
                        sb.append("\\u").append(t.substring(t.length() - 4));
                    }else{
                        sb.append(c);
                    }
            }
        }
        sb.append('"');
        return sb.toString();
    }

    public static void putRequestId(String requestId){
        MDC.put(KEY_PREFIX + KEY_REQUEST_ID, requestId);
    }

    public static void removeRequestId(){
        MDC.remove(KEY_PREFIX + KEY_REQUEST_ID);
    }
}

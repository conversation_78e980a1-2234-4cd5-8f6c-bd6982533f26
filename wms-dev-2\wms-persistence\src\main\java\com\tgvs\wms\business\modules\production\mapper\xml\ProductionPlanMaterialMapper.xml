<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tgvs.wms.business.modules.production.mapper.ProductionPlanMaterialMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.tgvs.wms.business.modules.production.entity.ProductionPlanMaterial">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="plan_no" property="planNo" jdbcType="VARCHAR"/>
        <result column="plan_date" property="planDate" jdbcType="DATE"/>
        <result column="material_code" property="materialCode" jdbcType="VARCHAR"/>
        <result column="material_name" property="materialName" jdbcType="VARCHAR"/>
        <result column="required_quantity" property="requiredQuantity" jdbcType="DECIMAL"/>
        <result column="priority" property="priority" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="is_hot" property="isHot" jdbcType="INTEGER"/>
        <result column="relocation_batch_no" property="relocationBatchNo" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="delete_flag" property="deleteFlag" jdbcType="INTEGER"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 分页查询生产计划物料 -->
    <select id="selectPlanMaterialPage" resultMap="BaseResultMap">
        SELECT 
            ppm.*
        FROM wms_production_plan_material ppm
        WHERE ppm.delete_flag = 0
        <if test="planNo != null and planNo != ''">
            AND ppm.plan_no LIKE CONCAT('%', #{planNo}, '%')
        </if>
        <if test="materialCode != null and materialCode != ''">
            AND ppm.material_code LIKE CONCAT('%', #{materialCode}, '%')
        </if>
        <if test="materialName != null and materialName != ''">
            AND ppm.material_name LIKE CONCAT('%', #{materialName}, '%')
        </if>
        <if test="status != null">
            AND ppm.status = #{status}
        </if>
        ORDER BY ppm.plan_date DESC, ppm.priority DESC, ppm.create_time DESC
    </select>

    <!-- 根据计划编号查询物料列表 -->
    <select id="selectByPlanNo" resultMap="BaseResultMap">
        SELECT * FROM wms_production_plan_material 
        WHERE plan_no = #{planNo} 
        AND delete_flag = 0
        ORDER BY priority DESC, required_quantity DESC
    </select>

    <!-- 根据计划编号查询热门物料列表 -->
    <select id="selectHotMaterialsByPlanNo" resultMap="BaseResultMap">
        SELECT * FROM wms_production_plan_material 
        WHERE plan_no = #{planNo} 
        AND is_hot = 1
        AND delete_flag = 0
        ORDER BY priority DESC, required_quantity DESC
    </select>

    <!-- 批量更新物料状态 -->
    <update id="batchUpdateStatus">
        UPDATE wms_production_plan_material 
        SET status = #{status}, 
            update_by = #{updateBy}, 
            update_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 根据物料编码查询最近的生产计划 -->
    <select id="selectRecentPlansByMaterialCode" resultMap="BaseResultMap">
        SELECT * FROM wms_production_plan_material 
        WHERE material_code = #{materialCode}
        AND delete_flag = 0
        AND plan_date >= DATE_SUB(CURDATE(), INTERVAL #{days} DAY)
        ORDER BY plan_date DESC
    </select>

    <!-- 统计物料使用频率 -->
    <select id="selectMaterialFrequency" resultMap="BaseResultMap">
        SELECT 
            material_code,
            material_name,
            COUNT(*) as frequency,
            SUM(required_quantity) as total_quantity,
            MAX(plan_date) as last_plan_date
        FROM wms_production_plan_material 
        WHERE delete_flag = 0
        AND plan_date >= DATE_SUB(CURDATE(), INTERVAL #{days} DAY)
        GROUP BY material_code, material_name
        HAVING COUNT(*) >= 2
        ORDER BY COUNT(*) DESC, SUM(required_quantity) DESC
    </select>

    <!-- 更新移库批次号 -->
    <update id="updateRelocationBatchNo">
        UPDATE wms_production_plan_material 
        SET relocation_batch_no = #{batchNo},
            update_by = #{updateBy},
            update_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tgvs.wms.business.modules.machineAuxiliary.mapper.WmsAuxiliaryInListMapper">

    <!-- 为DTO添加明确的ResultMap -->
    <resultMap id="AuxiliaryInBoundDtoMap" type="com.tgvs.wms.business.modules.machineAuxiliary.dto.AuxiliaryInBoundDto">
        <result column="inStoreNumber" property="inStoreNumber"/>
        <result column="taskType" property="taskType"/>
        <result column="status" property="status"/>
        <result column="remarks" property="remarks"/>
        <result column="createBy" property="createBy"/>
        <result column="createTime" property="createTime"/>
        <result column="updateBy" property="updateBy"/>
        <result column="updateTime" property="updateTime"/>
        <result column="deleteFlag" property="deleteFlag"/>
        <result column="id" property="id"/>
        <result column="operationType" property="operationType"/>
        <result column="contractNo" property="contractNo"/>
        <result column="itemNo" property="itemNo"/>
        <result column="reqListId" property="reqListId"/>
        <result column="materialCode" property="materialCode"/>
        <result column="materialName" property="materialName"/>
        <result column="materialColor" property="materialColor"/>
        <result column="materialColorCode" property="materialColorCode"/>
        <result column="materialModel" property="materialModel"/>
        <result column="quantity" property="quantity"/>
        <result column="materialUnit" property="materialUnit"/>
        <result column="priority" property="priority"/>
        <result column="materialType" property="materialType"/>
        <result column="isStore" property="isStore"/>
    </resultMap>

    <!-- Custom select for combined DTO with pagination -->
    <select id="selectDtoPage" resultMap="AuxiliaryInBoundDtoMap">
        SELECT
            h.in_store_number       AS inStoreNumber,
            h.task_type             AS taskType,
            h.remarks               AS remarks,
            h.create_by             AS createBy,
            h.create_time           AS createTime,
            h.update_by             AS updateBy,
            h.update_time           AS updateTime,
            h.delete_flag           AS deleteFlag,
            d.id                    AS id,
            d.operation_type        AS operationType,
            d.contract_no           AS contractNo,
            d.item_no               AS itemNo,
            d.req_list_id           AS reqListId,
            d.material_code         AS materialCode,
            d.material_name         AS materialName,
            d.material_color        AS materialColor,
            d.material_color_code   AS materialColorCode,
            d.material_model        AS materialModel,
            d.quantity              AS quantity,
            d.material_unit         AS materialUnit,
            d.priority              AS priority,
            d.prebox_status                AS status,
            i.material_type         AS materialType,
            i.is_store            AS isStore
        FROM
            wms_auxiliary_inbound h
                INNER JOIN
            wms_auxiliary_detail d ON h.in_store_number = d.ref_number
                LEFT JOIN
            wms_auxiliary_info i ON d.material_code = i.material_code
        ${ew.customSqlSegment}
    </select>
    
    <!-- 自定义条件查询辅料入库列表 -->
    <select id="selectWmsAuxiliaryInListCustom" resultType="com.tgvs.wms.business.modules.machineAuxiliary.entity.WmsAuxiliaryInBound">
        select d.id, l.contract_no, l.item_no, l.material_code, l.material_name, l.material_color, l.material_model,
        l.quantity,  l.priority, d.create_by, d.create_time, d.update_by, d.update_time, d.delete_flag
        from wms_auxiliary_inbound d
        INNER JOIN wms_auxiliary_detail  l on d.in_store_number=l.ref_number
        <where>
            <if test="contractNo != null and contractNo != ''">
                AND l.contract_no = #{contractNo}
            </if>
            <if test="itemNo != null and itemNo != ''">
                AND l.item_no = #{itemNo}
            </if>
            <if test="materialCode != null and materialCode != ''">
                AND l.material_code = #{materialCode}
            </if>
            <if test="materialName != null and materialName != ''">
                AND l.material_name like concat('%', #{materialName}, '%')
            </if>
            <if test="materialColor != null and materialColor != ''">
                AND l.material_color = #{materialColor}
            </if>
            <if test="materialModel != null and materialModel != ''">
                AND l.material_model = #{materialModel}
            </if>
            <if test="inQuantity != null">
                AND l.quantity = #{inQuantity}
            </if>
            <if test="priority != null">
                AND l.priority = #{priority}
            </if>
            AND d.delete_flag = 0
        </where>
        order by d.create_time desc
    </select>
    
    <!-- 批量逻辑删除辅料入库信息 -->
    <update id="deleteWmsAuxiliaryInListByIds">
        update wms_auxiliary_inbound set delete_flag = 1
        where id in
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 历史查询DTO的ResultMap -->
    <resultMap id="AuxiliaryInBoundHistoryDtoMap" type="com.tgvs.wms.business.modules.machineAuxiliary.dto.AuxiliaryInBoundHistoryDto">
        <result column="inStoreNumber" property="inStoreNumber"/>
        <result column="taskType" property="taskType"/>
        <result column="status" property="status"/>
        <result column="remarks" property="remarks"/>
        <result column="createBy" property="createBy"/>
        <result column="createTime" property="createTime"/>
        <result column="updateBy" property="updateBy"/>
        <result column="updateTime" property="updateTime"/>
        <result column="deleteFlag" property="deleteFlag"/>
        <result column="id" property="id"/>
        <result column="operationType" property="operationType"/>
        <result column="contractNo" property="contractNo"/>
        <result column="itemNo" property="itemNo"/>
        <result column="reqListId" property="reqListId"/>
        <result column="materialCode" property="materialCode"/>
        <result column="materialName" property="materialName"/>
        <result column="materialColor" property="materialColor"/>
        <result column="materialColorCode" property="materialColorCode"/>
        <result column="materialModel" property="materialModel"/>
        <result column="quantity" property="quantity"/>
        <result column="materialUnit" property="materialUnit"/>
        <result column="priority" property="priority"/>
        <result column="materialType" property="materialType"/>
        <result column="isStore" property="isStore"/>
        <result column="actualInboundQty" property="actualInboundQty"/>
        <result column="boxNo" property="boxNo"/>
        <result column="taskStartTime" property="taskStartTime"/>
        <result column="taskEndTime" property="taskEndTime"/>
        <result column="taskOrder" property="taskOrder"/>
        <result column="containerType" property="containerType"/>
    </resultMap>

    <!-- 辅料入库历史查询（关联任务表） - 按容器拆分显示，修复SQL语法错误 -->
    <select id="selectHistoryDtoPage" resultMap="AuxiliaryInBoundHistoryDtoMap">
        SELECT
            inStoreNumber, taskType, remarks, createBy, createTime, updateBy, updateTime,
            deleteFlag, id, operationType, contractNo, itemNo, reqListId, materialCode,
            materialName, materialColor, materialColorCode, materialModel, quantity,
            materialUnit, priority, status, materialType, isStore, actualInboundQty,boxNo,
            taskStartTime, taskEndTime, taskOrder
        FROM (
            SELECT
                h.in_store_number       AS inStoreNumber,
                h.task_type             AS taskType,
                h.remarks               AS remarks,
                h.create_by             AS createBy,
                h.create_time           AS createTime,
                h.update_by             AS updateBy,
                h.update_time           AS updateTime,
                h.delete_flag           AS deleteFlag,
                d.id                    AS id,
                d.operation_type        AS operationType,
                d.contract_no           AS contractNo,
                d.item_no               AS itemNo,
                d.req_list_id           AS reqListId,
                d.material_code         AS materialCode,
                d.material_name         AS materialName,
                d.material_color        AS materialColor,
                d.material_color_code   AS materialColorCode,
                d.material_model        AS materialModel,
                d.quantity              AS quantity,
                d.material_unit         AS materialUnit,
                d.priority              AS priority,
                d.prebox_status         AS status,
                i.material_type         AS materialType,
                i.is_store              AS isStore,
                -- 直接显示该容器的实际入库数量，不进行汇总
                COALESCE(x.actual_inbound_qty, 0) AS actualInboundQty,
                -- 显示具体的容器信息
                x.container_no AS boxNo,
                x.task_order AS taskOrder,
                t.create_time AS taskStartTime,
                t.update_time AS taskEndTime
            FROM
                wms_auxiliary_inbound h
                INNER JOIN wms_auxiliary_detail d ON h.in_store_number = d.ref_number
                LEFT JOIN wms_auxiliary_info i ON d.material_code = i.material_code
                -- 直接关联预装箱记录，按容器拆分显示
                LEFT JOIN wms_auxiliary_prebox x ON h.id = x.stock_in_id
                    AND h.in_store_number = x.stock_in_no
                    AND d.material_code = x.material_code
                    AND d.contract_no = x.contract_no
                    AND (d.item_no = x.item_no OR (d.item_no IS NULL AND x.item_no IS NULL))
                    AND x.status = 1
                    AND (x.is_deleted = 0 OR x.is_deleted IS NULL)
                -- 关联任务表获取任务时间信息
                LEFT JOIN wms_box_task_list t ON x.task_order = t.task_order
                ${ew.customSqlSegment}
        ) final_data
        ORDER BY updateTime DESC, id, boxNo
    </select>

</mapper>

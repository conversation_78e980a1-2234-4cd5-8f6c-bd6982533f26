package com.tgvs.wms.business.modules.commncation.entity;

import java.io.Serializable;
import java.util.Date;

import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.tgvs.wms.common.annotation.Dict;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TableName("wms_dps_controllerinfo")
@ApiModel(value = "wms_dps_controllerinfo对象", description = "电子标签控制器配置")
@Data
public class DpsControllerinfo implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键")
    private String id;

    @Excel(name = "控制器ID", width = 15.0D)
    @ApiModelProperty("控制器ID")
    private Integer controllerid;

    @Excel(name = "IP地址", width = 15.0D)
    @ApiModelProperty("IP地址")
    private String ipaddress;

    @Excel(name = "端口", width = 15.0D)
    @ApiModelProperty("端口")
    private Integer port;

    @Excel(name = "序列号", width = 15.0D)
    @ApiModelProperty("序列号")
    private String license;

    @Excel(name = "是否监控", width = 15.0D)
    @ApiModelProperty("是否监控")
    private Integer monitorcontroller;

    @Excel(name = "监控间隔", width = 15.0D)
    @ApiModelProperty("监控间隔")
    private Integer monitorinterval;

    @Excel(name = "重连次数", width = 15.0D)
    @ApiModelProperty("重连次数")
    private Integer monitormaxretrycount;

    @Excel(name = "超时时间", width = 15.0D)
    @ApiModelProperty("超时时间")
    private Integer timeout;

    @Excel(name = "控制器类型", width = 15.0D, dicCode = "light_type")
    @Dict(dicCode = "light_type")
    @ApiModelProperty("控制器类型")
    private Integer lightsteptype;

    @Excel(name = "媒体文件路径", width = 15.0D)
    @ApiModelProperty("媒体文件路径")
    private String pcmediapath;

    @Excel(name = "描述", width = 15.0D)
    @ApiModelProperty("描述")
    private String description;

    @Excel(name = "加权值", width = 15.0D)
    @ApiModelProperty("加权值")
    private Integer msgaddrweightvalue;

    @Excel(name = "消息类别", width = 15.0D)
    @ApiModelProperty("消息类别")
    private Integer msgtype;

    @Excel(name = "返回值", width = 15.0D)
    @ApiModelProperty("返回值")
    private Integer returnkey;

    @Excel(name = "返回的消息", width = 15.0D)
    @ApiModelProperty("返回的消息")
    private String msg;

    @Excel(name = "所属系统", width = 15.0D)
    @ApiModelProperty("所属系统")
    private String sysid;

    @ApiModelProperty("创建人")
    private String createBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("更新时间")
    private Date updateTime;

    @Excel(name = "状态", width = 15.0D, dicCode = "connect_status")
    @Dict(dicCode = "connect_status")
    @ApiModelProperty("状态")
    private Integer state;
}

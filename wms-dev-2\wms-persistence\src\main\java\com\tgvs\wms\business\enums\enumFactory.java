package com.tgvs.wms.business.enums;

import com.tgvs.wms.common.util.oConvertUtils;

public enum enumFactory {
    two(Integer.valueOf(2), "B", "申洲二部"),
    four(Integer.valueOf(4), "D", "申洲四部");

    private Integer value;

    private String code;

    private String text;

    enumFactory(Integer value, String code, String text) {
        this.value = value;
        this.code = code;
        this.text = text;
    }

    public static enumFactory getByValue(int value) {
        if (oConvertUtils.isEmpty(Integer.valueOf(value)))
            return null;
        for (enumFactory val : values()) {
            if (val.getValue().equals(Integer.valueOf(value)))
                return val;
        }
        return null;
    }

    public Integer getValue() {
        return this.value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getText() {
        return this.text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public static enumFactory toEnum(Integer Value) {
        for (enumFactory e : values()) {
            if (e.getValue().equals(Value))
                return e;
        }
        return null;
    }
}

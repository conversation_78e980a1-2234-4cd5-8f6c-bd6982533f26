package com.tgvs.wms.business.httpservice.baseBean.warehouse;


import com.fasterxml.jackson.annotation.JsonProperty;

public class MoveOrder {
    private Integer id;

    public void setId(Integer id) {
        this.id = id;
    }

    public void setMessageName(String messageName) {
        this.messageName = messageName;
    }

    public void setBoxId(String boxId) {
        this.boxId = boxId;
    }

    public void setS_level(int s_level) {
        this.s_level = s_level;
    }

    public void setS_location(int s_location) {
        this.s_location = s_location;
    }

    public void setE_level(int e_level) {
        this.e_level = e_level;
    }

    public void setE_location(int e_location) {
        this.e_location = e_location;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public void setWmsId(String wmsId) {
        this.wmsId = wmsId;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof MoveOrder))
            return false;
        MoveOrder other = (MoveOrder)o;
        if (!other.canEqual(this))
            return false;
        Object this$id = getId(), other$id = other.getId();
        if ((this$id == null) ? (other$id != null) : !this$id.equals(other$id))
            return false;
        Object this$messageName = getMessageName(), other$messageName = other.getMessageName();
        if ((this$messageName == null) ? (other$messageName != null) : !this$messageName.equals(other$messageName))
            return false;
        Object this$boxId = getBoxId(), other$boxId = other.getBoxId();
        if ((this$boxId == null) ? (other$boxId != null) : !this$boxId.equals(other$boxId))
            return false;
        if (getS_level() != other.getS_level())
            return false;
        if (getS_location() != other.getS_location())
            return false;
        if (getE_level() != other.getE_level())
            return false;
        if (getE_location() != other.getE_location())
            return false;
        Object this$priority = getPriority(), other$priority = other.getPriority();
        if ((this$priority == null) ? (other$priority != null) : !this$priority.equals(other$priority))
            return false;
        Object this$wmsId = getWmsId(), other$wmsId = other.getWmsId();
        return !((this$wmsId == null) ? (other$wmsId != null) : !this$wmsId.equals(other$wmsId));
    }

    protected boolean canEqual(Object other) {
        return other instanceof MoveOrder;
    }

    public int hashCode() {
        int PRIME = 59,
        result = 1;
        Object $id = getId();
        result = result * 59 + (($id == null) ? 43 : $id.hashCode());
        Object $messageName = getMessageName();
        result = result * 59 + (($messageName == null) ? 43 : $messageName.hashCode());
        Object $boxId = getBoxId();
        result = result * 59 + (($boxId == null) ? 43 : $boxId.hashCode());
        result = result * 59 + getS_level();
        result = result * 59 + getS_location();
        result = result * 59 + getE_level();
        result = result * 59 + getE_location();
        Object $priority = getPriority();
        result = result * 59 + (($priority == null) ? 43 : $priority.hashCode());
        Object $wmsId = getWmsId();
        return result * 59 + (($wmsId == null) ? 43 : $wmsId.hashCode());
    }

    public String toString() {
        return "MoveOrder(id=" + getId() + ", messageName=" + getMessageName() + ", boxId=" + getBoxId() + ", s_level=" + getS_level() + ", s_location=" + getS_location() + ", e_level=" + getE_level() + ", e_location=" + getE_location() + ", priority=" + getPriority() + ", wmsId=" + getWmsId() + ")";
    }

    public Integer getId() {
        return this.id;
    }

    @JsonProperty("messageName")
    private String messageName = "appointBoxAnnounce";

    @JsonProperty("boxId")
    private String boxId;

    private int s_level;

    private int s_location;

    private int e_level;

    private int e_location;

    private Integer priority;

    @JsonProperty("wmsId")
    private String wmsId;

    public String getMessageName() {
        return this.messageName;
    }

    public String getBoxId() {
        return this.boxId;
    }

    public int getS_level() {
        return this.s_level;
    }

    public int getS_location() {
        return this.s_location;
    }

    public int getE_level() {
        return this.e_level;
    }

    public int getE_location() {
        return this.e_location;
    }

    public Integer getPriority() {
        return this.priority;
    }

    public String getWmsId() {
        return this.wmsId;
    }
}

package com.tgvs.wms.business.enums;


import com.tgvs.wms.common.util.oConvertUtils;

public enum enumYesOrNo {
    no(Integer.valueOf(0), "false", "否"),
    yes(Integer.valueOf(1), "true", "是");

    private Integer value;

    private String code;

    private String text;

    enumYesOrNo(Integer value, String code, String text) {
        this.value = value;
        this.code = code;
        this.text = text;
    }

    public static enumYesOrNo getByValue(int value) {
        if (oConvertUtils.isEmpty(Integer.valueOf(value)))
            return null;
        for (enumYesOrNo val : values()) {
            if (val.getValue().equals(Integer.valueOf(value)))
                return val;
        }
        return null;
    }

    public Integer getValue() {
        return this.value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getText() {
        return this.text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public static enumYesOrNo toEnum(Integer Value) {
        for (enumYesOrNo e : values()) {
            if (e.getValue().equals(Value))
                return e;
        }
        return null;
    }
}

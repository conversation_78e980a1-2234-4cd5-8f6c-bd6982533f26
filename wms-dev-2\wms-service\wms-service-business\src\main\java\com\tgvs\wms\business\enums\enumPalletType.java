package com.tgvs.wms.business.enums;

import com.tgvs.wms.common.util.oConvertUtils;

public enum enumPalletType {
    unknow(Integer.valueOf(0), "unknow", "无"),
    no_pallet(Integer.valueOf(1), "no_pallet", "无板"),
    empty_pallet(Integer.valueOf(2), "empty", "空板"),
    surplus(Integer.valueOf(3), "surplus", "余布"),
    full(Integer.valueOf(4), "full", "布料");

    private Integer value;

    private String code;

    private String text;

    enumPalletType(Integer value, String code, String text) {
        this.value = value;
        this.code = code;
        this.text = text;
    }

    public static enumPalletType getByValue(int value) {
        if (oConvertUtils.isEmpty(Integer.valueOf(value)))
            return null;
        for (enumPalletType val : values()) {
            if (val.getValue().equals(Integer.valueOf(value)))
                return val;
        }
        return null;
    }

    public Integer getValue() {
        return this.value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getText() {
        return this.text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public static enumPalletType toEnum(Integer Value) {
        for (enumPalletType e : values()) {
            if (e.getValue().equals(Value))
                return e;
        }
        return null;
    }

    public static enumPalletType toEnum(String Value) {
        for (enumPalletType e : values()) {
            if (e.getValue().toString().equals(Value))
                return e;
        }
        return null;
    }
}

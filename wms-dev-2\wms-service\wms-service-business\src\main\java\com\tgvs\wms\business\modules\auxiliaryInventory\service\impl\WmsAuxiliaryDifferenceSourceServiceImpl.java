package com.tgvs.wms.business.modules.auxiliaryInventory.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tgvs.wms.business.modules.auxiliaryInventory.entity.WmsAuxiliaryDifferenceSource;
import com.tgvs.wms.business.modules.auxiliaryInventory.mapper.WmsAuxiliaryDifferenceSourceMapper;
import com.tgvs.wms.business.modules.auxiliaryInventory.service.IWmsAuxiliaryDifferenceSourceService;
import com.tgvs.wms.common.core.domain.QueryModel;
import com.tgvs.wms.common.query.QueryGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 辅料差异数据源 Service实现类
 * 
 * <AUTHOR> Generated
 * @date 2025-01-24
 */
@Slf4j
@Service
public class WmsAuxiliaryDifferenceSourceServiceImpl extends ServiceImpl<WmsAuxiliaryDifferenceSourceMapper, WmsAuxiliaryDifferenceSource> implements IWmsAuxiliaryDifferenceSourceService {

    @Autowired
    private WmsAuxiliaryDifferenceSourceMapper differenceSourceMapper;

    @Override
    public IPage<WmsAuxiliaryDifferenceSource> pageDifferenceSourceList(QueryModel queryModel) {
        log.info("分页查询差异数据源列表: {}", queryModel);
        QueryWrapper<WmsAuxiliaryDifferenceSource> queryWrapper = QueryGenerator.initQueryWrapper(queryModel.getSearchParams());
        Page<WmsAuxiliaryDifferenceSource> page = new Page<>(queryModel.getPage(), queryModel.getLimit());
        return  this.baseMapper.selectPage(page, queryWrapper);
    }

    @Override
    public List<WmsAuxiliaryDifferenceSource> listPendingDifference() {
        log.info("查询待处理的差异数据列表");
        return differenceSourceMapper.selectPendingDifferenceList();
    }

    @Override
    public Map<String, Object> getDifferenceStatistics() {
        log.info("获取差异数据统计");
        
        Map<String, Object> statistics = new HashMap<>();
        
        // 统计待处理数据
        int pendingCount = differenceSourceMapper.countByDiffTypeAndStatus(null, "PENDING");
        int surplusCount = differenceSourceMapper.countByDiffTypeAndStatus("SURPLUS", "PENDING");
        int shortageCount = differenceSourceMapper.countByDiffTypeAndStatus("SHORTAGE", "PENDING");
        
        // 统计已选择盘点数据
        int selectedCount = differenceSourceMapper.countByDiffTypeAndStatus(null, "SELECTED");
        
        // 统计已完成数据
        int completedCount = differenceSourceMapper.countByDiffTypeAndStatus(null, "COMPLETED");
        
        statistics.put("pendingCount", pendingCount);
        statistics.put("surplusCount", surplusCount);
        statistics.put("shortageCount", shortageCount);
        statistics.put("selectedCount", selectedCount);
        statistics.put("completedCount", completedCount);
        statistics.put("totalCount", pendingCount + selectedCount + completedCount);
        
        log.info("差异数据统计结果: {}", statistics);
        return statistics;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchImportDifferenceData(List<WmsAuxiliaryDifferenceSource> differenceList, String sourceBatch, String sourceSystem) {
        log.info("批量导入差异数据, 批次号: {}, 来源系统: {}, 数据量: {}", sourceBatch, sourceSystem, differenceList.size());
        
        try {
            Date now = new Date();
            for (WmsAuxiliaryDifferenceSource difference : differenceList) {
                // 设置导入信息
                difference.setSourceBatch(sourceBatch);
                difference.setSourceSystem(sourceSystem);
                difference.setImportTime(now);
                difference.setStatus("PENDING");
                difference.setDelFlag(0);
                difference.setCreateTime(now);
                difference.setUpdateTime(now);
                
                // 计算差异率 (基于WMS数量)
                if (difference.getWmsQty() != null && difference.getWmsQty().compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal diffRate = difference.getDiffQty()
                            .divide(difference.getWmsQty(), 4, BigDecimal.ROUND_HALF_UP)
                            .multiply(new BigDecimal("100"));
                    difference.setDiffRate(diffRate);
                }
            }
            
            // 批量插入
            boolean result = this.saveBatch(differenceList);
            log.info("批量导入差异数据结果: {}", result);
            return result;
            
        } catch (Exception e) {
            log.error("批量导入差异数据异常", e);
            throw new RuntimeException("批量导入差异数据失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateStatus(List<String> ids, String status, String updateBy) {
        log.info("批量更新状态: ids={}, status={}, updateBy={}", ids, status, updateBy);
        
        try {
            int updateCount = differenceSourceMapper.batchUpdateStatus(ids, status, updateBy);
            boolean result = updateCount > 0;
            log.info("批量更新状态结果: 更新数量={}, 结果={}", updateCount, result);
            return result;
            
        } catch (Exception e) {
            log.error("批量更新状态异常", e);
            throw new RuntimeException("批量更新状态失败: " + e.getMessage());
        }
    }

    @Override
    public List<WmsAuxiliaryDifferenceSource> listByIds(List<String> ids) {
        log.info("根据ID列表获取差异数据: {}", ids);
        
        LambdaQueryWrapper<WmsAuxiliaryDifferenceSource> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(WmsAuxiliaryDifferenceSource::getId, ids)
                .eq(WmsAuxiliaryDifferenceSource::getDelFlag, 0)
                .orderByDesc(WmsAuxiliaryDifferenceSource::getDiffRate);
        
        List<WmsAuxiliaryDifferenceSource> result = this.list(wrapper);
        log.info("获取差异数据结果数量: {}", result.size());
        return result;
    }
}
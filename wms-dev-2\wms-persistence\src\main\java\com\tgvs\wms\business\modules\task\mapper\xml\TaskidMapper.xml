<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tgvs.wms.business.modules.task.mapper.TaskidMapper">

	<update id="uodateid" parameterType="java.lang.Integer">
		update wms_taskid set taskid = #{taskid}
	</update>

	<select id="getTaskid" resultType="com.tgvs.wms.business.modules.task.entity.Taskid">
		SELECT * FROM  wms_taskid LIMIT 1
	</select>

</mapper>
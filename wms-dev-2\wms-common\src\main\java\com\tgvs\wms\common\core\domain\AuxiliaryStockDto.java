package com.tgvs.wms.common.core.domain;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 辅料库存查询DTO（共享类）
 * 
 * <AUTHOR>
 */
@Data
public class AuxiliaryStockDto {
    /**
     * 主键
     */
    private String id;

    /**
     * 容器类型：1-料箱 2-托盘号
     */
    private Integer boxType;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 箱号/托盘号
     */
    private String boxNo;

    /**
     * 格子号
     */
    private Integer gridId;

    /**
     * 格子状态：0-空 1-25% 2-50% 3-75% 4-满格
     */
    private Integer gridStatus;

    /**
     * 托盘容量
     */
    private Integer gridVolume;

    /**
     * 合约号
     */
    private String contractNo;

    /**
     * 合约号-Po号（款式属性才有)
     */
    private String contractNoPo;

    /**
     * 款号
     */
    private String itemNo;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 物料颜色
     */
    private String materialColor;

    /**
     * 物料规格
     */
    private String materialModel;

    /**
     * 尺码
     */
    private String materialSize;

    /**
     * 物料数量(每格)
     */
    private BigDecimal materialQuantity;

    /**
     * 物料属性(0-自身属性 1-款式属性)
     */
    private Integer materialProperty;

    /**
     * 预装数量(每格)
     */
    private BigDecimal preInstalledQantity;

    /**
     * 预装状态：0.未使用，1.预装，2.已装，3拣货出库完成.4入库完成
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 列
     */
    private Integer column;

    /**
     * 行
     */
    private Integer row;

    /**
     * 层
     */
    private Integer level;

    /**
     * 货位编码
     */
    private String code;

    /**
     * 辅料类型（来自wms_auxiliary_info表）
     */
    private String materialType;
} 
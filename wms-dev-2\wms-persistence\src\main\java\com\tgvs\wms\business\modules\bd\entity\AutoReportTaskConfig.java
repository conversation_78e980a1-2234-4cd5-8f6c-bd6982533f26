package com.tgvs.wms.business.modules.bd.entity;

import java.io.Serializable;
import java.util.Date;

import com.tgvs.wms.base.entity.BaseEntity;
import com.tgvs.wms.common.annotation.Dict;
import lombok.EqualsAndHashCode;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;


import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@EqualsAndHashCode(callSuper = true)
@TableName("wms_auto_report_task_config")
@ApiModel(value = "wms_auto_report_task_config对象", description = "自动上报定时任务配置")
@Data
public class AutoReportTaskConfig extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;



    @Excel(name = "任务类型", width = 15.0D, dicCode = "report_task_type")
    @Dict(dicCode = "report_task_type")
    @ApiModelProperty("任务类型")
    private String taskType;

    @Excel(name = "执行周期(分钟)", width = 15.0D)
    @ApiModelProperty("执行周期(分钟)")
    private Integer interval;

    @Excel(name = "任务状态", width = 15.0D, dicCode = "report_task_status")
    @Dict(dicCode = "report_task_status")
    @ApiModelProperty("任务状态（1-启用，0-禁用）")
    private String status;

    @ApiModelProperty("任务描述")
    private String description;

    @ApiModelProperty("创建人")
    private String createBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("更新时间")
    private Date updateTime;

    @ApiModelProperty("删除状态")
    private Integer delFlag;
} 
package com.tgvs.wms.common.core.domain;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import lombok.Data;

@Data
public class QueryModel {
    private Integer limit;
    private Integer page;
    private Long total;
    private HashMap<String, String> searchParams;

    private List<SortModel> sortList;

    public void putParam(String key, String value){
        if(this.searchParams == null){
            searchParams = new HashMap<>();
        }
        searchParams.put(key, value);
    }

    public void ascSort(String column){

        if(this.sortList == null){
            this.sortList = new ArrayList<>();
        }

        SortModel sortModel = new SortModel();
        sortModel.setColumn(column);
        sortModel.setSortType(1);

        this.sortList.add(sortModel);
    }

    public void descSort(String column){

        if(this.sortList == null){
            this.sortList = new ArrayList<>();
        }

        SortModel sortModel = new SortModel();
        sortModel.setColumn(column);
        sortModel.setSortType(2);

        this.sortList.add(sortModel);
    }

    @Data
    public static class SortModel{
        private String column;
        private int sortType;
    }

}

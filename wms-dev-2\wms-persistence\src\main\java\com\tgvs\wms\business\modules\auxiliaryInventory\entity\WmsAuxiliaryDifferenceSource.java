package com.tgvs.wms.business.modules.auxiliaryInventory.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.tgvs.wms.base.entity.BaseEntity;
import com.tgvs.wms.common.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 辅料差异数据源表(第三方导入)
 * 
 * <AUTHOR> Generated
 * @date 2025-01-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("wms_auxiliary_difference_source")
@ApiModel(value = "WmsAuxiliaryDifferenceSource对象", description = "辅料差异数据源表")
public class WmsAuxiliaryDifferenceSource extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键ID")
    private String id;

    // ========== 物料信息 ==========
    @Excel(name = "物料编码", width = 15)
    @ApiModelProperty("物料编码")
    private String materialCode;

    @Excel(name = "物料名称", width = 20)
    @ApiModelProperty("物料名称")
    private String materialName;

    @Excel(name = "规格型号", width = 15)
    @ApiModelProperty("规格型号")
    private String materialSpec;

    @Excel(name = "物料颜色", width = 10)
    @ApiModelProperty("物料颜色")
    private String materialColor;

    @Excel(name = "尺码", width = 8)
    @ApiModelProperty("尺码")
    private String materialSize;

    @Excel(name = "单位", width = 8)
    @ApiModelProperty("单位")
    private String unit;

    // ========== 库存位置信息 ==========
    @Excel(name = "库位编码", width = 12)
    @ApiModelProperty("库位编码")
    private String locationCode;

    @Excel(name = "库位名称", width = 15)
    @ApiModelProperty("库位名称")
    private String locationName;

    @Excel(name = "容器号", width = 12)
    @ApiModelProperty("容器号")
    private String boxNo;

    @Excel(name = "容器类型", width = 10, dicCode = "box_type")
    @Dict(dicCode = "box_type")
    @ApiModelProperty("容器类型")
    private Integer boxType;

    // ========== 合约信息 ==========
    @Excel(name = "合约号", width = 15)
    @ApiModelProperty("合约号")
    private String contractNo;

    @Excel(name = "PO号", width = 15)
    @ApiModelProperty("PO号")
    private String contractNoPo;

    @Excel(name = "款号", width = 12)
    @ApiModelProperty("款号")
    private String itemNo;

    // ========== 库存数量对比数据 ==========
    @Excel(name = "WMS库存数量", width = 12)
    @ApiModelProperty("WMS库存数量")
    private BigDecimal wmsQty;

    @Excel(name = "ERP(i9)库存数量", width = 15)
    @ApiModelProperty("ERP(i9)库存数量")
    private BigDecimal erpQty;

    @Excel(name = "差异数量", width = 12)
    @ApiModelProperty("差异数量(ERP-WMS)")
    private BigDecimal diffQty;

    @Excel(name = "差异类型", width = 10, dicCode = "difference_type")
    @Dict(dicCode = "difference_type")
    @ApiModelProperty("差异类型")
    private String diffType;

    @Excel(name = "差异率(%)", width = 10)
    @ApiModelProperty("差异率(%)")
    private BigDecimal diffRate;

    // ========== 数据来源信息 ==========
    @Excel(name = "来源系统", width = 12)
    @ApiModelProperty("来源系统")
    private String sourceSystem;

    @Excel(name = "导入批次号", width = 20)
    @ApiModelProperty("导入批次号")
    private String sourceBatch;

    @Excel(name = "导入时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("导入时间")
    private Date importTime;

    // ========== 处理状态 ==========
    @Excel(name = "状态", width = 10, dicCode = "difference_source_status")
    @Dict(dicCode = "difference_source_status")
    @ApiModelProperty("状态")
    private String status;

    @Excel(name = "选择盘点时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("选择盘点时间")
    private Date selectedTime;

    @Excel(name = "完成时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("完成时间")
    private Date completedTime;

    @Excel(name = "备注", width = 30)
    @ApiModelProperty("备注")
    private String remark;
@ApiModelProperty("删除")
    private int delFlag;
}
package com.tgvs.wms.business.enums;


import com.tgvs.wms.common.util.oConvertUtils;

public enum enumTargetType {
    site(Integer.valueOf(1), "S", "工位"),
    dolly(Integer.valueOf(2), "D", "松布架"),
    cache(Integer.valueOf(3), "R", "缓存架");

    private Integer value;

    private String code;

    private String text;

    enumTargetType(Integer value, String code, String text) {
        this.value = value;
        this.code = code;
        this.text = text;
    }

    public static enumTargetType getByValue(int value) {
        if (oConvertUtils.isEmpty(Integer.valueOf(value)))
            return null;
        for (enumTargetType val : values()) {
            if (val.getValue().equals(Integer.valueOf(value)))
                return val;
        }
        return null;
    }

    public Integer getValue() {
        return this.value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getText() {
        return this.text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public static enumTargetType toEnum(Integer Value) {
        for (enumTargetType e : values()) {
            if (e.getValue().equals(Value))
                return e;
        }
        return null;
    }
}

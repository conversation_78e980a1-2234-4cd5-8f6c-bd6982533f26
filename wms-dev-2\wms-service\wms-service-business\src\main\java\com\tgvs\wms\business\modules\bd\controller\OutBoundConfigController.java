package com.tgvs.wms.business.modules.bd.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tgvs.wms.base.controller.BaseController;
import com.tgvs.wms.business.modules.bd.entity.OutboundConfig;
import com.tgvs.wms.business.modules.bd.service.IOutboundConfigService;
import com.tgvs.wms.business.wmsservice.MainService;
import com.tgvs.wms.common.annotation.AutoLog;
import com.tgvs.wms.common.constant.Result;
import com.tgvs.wms.common.core.domain.QueryModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

@Api(tags = {"出库策略管理"})
@RestController
@RequestMapping({"/bd/outbound"})
@Slf4j
public class OutBoundConfigController extends BaseController<OutboundConfig, IOutboundConfigService> {


    @AutoLog("出库策略管理-分页列表查询")
    @ApiOperation(value = "出库策略管理-分页列表查询", notes = "出库策略管理-分页列表查询")
    @PostMapping({"/list"})
    public Result<?> queryPageList(@RequestBody(required = false) QueryModel queryModel) {
        IPage<OutboundConfig> list = service.pageList(queryModel);
        Result result = Result.ok(list.getRecords());
        result.setTotal(list.getTotal());
        return result;
    }

    @AutoLog("出库策略管理-添加")
    @ApiOperation(value = "出库策略管理-添加", notes = "出库策略管理-添加")
    //@RequiresPermissions({"baseLineConfig:add"})
    @PostMapping({"/add"})
    public Result<?> add(@RequestBody OutboundConfig outboundConfig) {
        this.service.save(outboundConfig);
        return Result.OK("添加成功！");
    }

    @AutoLog("出库策略管理-编辑")
    @ApiOperation(value = "出库策略管理-编辑", notes = "出库策略管理-编辑")
    //@RequiresPermissions({"baseLineConfig:edit"})
    @PostMapping({"/edit"})
    public Result<?> edit(@RequestBody OutboundConfig outboundConfig) {
        this.service.updateById(outboundConfig);
        try {
            MainService.inboundColumns = outboundConfig.getValue();
        } catch (Exception exception) {
        }
        return Result.OK("编辑成功!");
    }

    @AutoLog("出库策略管理-通过id删除")
    @ApiOperation(value = "出库策略管理-通过id删除", notes = "出库策略管理-通过id删除")
    //@RequiresPermissions({"baseLineConfig:delete"})
    @PostMapping({"/delete"})
    public Result<?> delete(@RequestBody String id) {
        this.service.removeById(id);
        return Result.OK("删除成功!");
    }

    @AutoLog("出库策略管理-批量删除")
    @ApiOperation(value = "出库策略管理-批量删除", notes = "出库策略管理-批量删除")
    //@RequiresPermissions({"baseLineConfig:deleteBatch"})
    @PostMapping({"/deleteBatch"})
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.service.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    @AutoLog("出库策略管理-通过id查询")
    @ApiOperation(value = "出库策略管理-通过id查询", notes = "出库策略管理-通过id查询")
    @PostMapping({"/queryById"})
    public Result<?> queryById(@RequestBody String id) {
        OutboundConfig outboundConfig = (OutboundConfig) this.service.getById(id);
        if (outboundConfig == null)
            return Result.error("未找到对应数据");
        return Result.OK(outboundConfig);
    }

    @AutoLog("出库策略管理-修改默认策略")
    @ApiOperation(value = "出库策略管理-修改默认策略", notes = "出库策略管理-修改默认策略")
    @PostMapping({"/updateChecked"})
    public Result<?> updateCheckedById(@RequestBody String id) {
        this.service.updateChecked(id);
        return Result.OK("修改默认出库策略成功!");
    }

    @RequestMapping({"/exportXls"})
    public ModelAndView exportXls(HttpServletRequest request, OutboundConfig outboundConfig) {
        return exportXls(request, outboundConfig, OutboundConfig.class, "出库策略管理");
    }

    @RequiresPermissions({"area:importExcel"})
    @RequestMapping(value = {"/importExcel"}, method = {RequestMethod.POST})
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return importExcel(request, response, OutboundConfig.class);
    }
}

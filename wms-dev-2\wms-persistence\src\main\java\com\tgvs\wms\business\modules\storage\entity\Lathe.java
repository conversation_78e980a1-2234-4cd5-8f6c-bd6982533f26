package com.tgvs.wms.business.modules.storage.entity;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "铺床", description = "最少铺床")
@Data
public class Lathe implements Serializable {
    @ApiModelProperty("数量")
    private Integer subcount;

    @ApiModelProperty("铺床编码")
    private String latheNo;
}

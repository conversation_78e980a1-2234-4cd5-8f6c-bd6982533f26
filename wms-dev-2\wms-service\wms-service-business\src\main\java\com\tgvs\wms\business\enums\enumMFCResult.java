package com.tgvs.wms.business.enums;

import com.tgvs.wms.common.util.oConvertUtils;

public enum enumMFCResult {
    empty_wmsid(Integer.valueOf(-4), "empty_wmsid", "wmsid空"),
    success(Integer.valueOf(0), "success", "成功"),
    execute(Integer.valueOf(2), "execute", "执行中"),
    cancel_fault(Integer.valueOf(3), "cancel_fault", "执行中,无法取消"),
    cancel_empty(Integer.valueOf(4), "cancel_empty", "无任务，无法取消"),
    intask_repeat(Integer.valueOf(6), "intask_repeat", "入库任务重复，货位不同"),
    error(Integer.valueOf(10), "error", "其它异常"),
    non_outbound(Integer.valueOf(11), "non_outbound", "出库口不存在"),
    outbound_repeat(Integer.valueOf(12), "outbound_repeat", "出库口重复");

    private Integer value;

    private String code;

    private String text;

    enumMFCResult(Integer value, String code, String text) {
        this.value = value;
        this.code = code;
        this.text = text;
    }

    public static enumMFCResult getByValue(int value) {
        if (oConvertUtils.isEmpty(Integer.valueOf(value)))
            return null;
        for (enumMFCResult val : values()) {
            if (val.getValue().equals(Integer.valueOf(value)))
                return val;
        }
        return null;
    }

    public Integer getValue() {
        return this.value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getText() {
        return this.text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public static enumMFCResult toEnum(Integer Value) {
        for (enumMFCResult e : values()) {
            if (e.getValue().equals(Value))
                return e;
        }
        return null;
    }
}

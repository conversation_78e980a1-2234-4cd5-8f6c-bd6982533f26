<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tgvs.wms.business.modules.machineMaterials.mapper.WmsMachineInventoryRecordMapper">

    <select id="SelectWmsMachineInventoryRecord" resultType="com.tgvs.wms.business.modules.machineMaterials.vo.WmsMachineInventoryRecordVo">
        SELECT material_code,object_id,SUM(inventory_quantity) AS inQuantity
        FROM wms_machine_inventory_record
        <where>
            <if test="checkNumber != null and checkNumber != ''">
                AND check_number = #{checkNumber}
            </if>
            <if test="inventoryType != null and inventoryType!= ''">
                AND inventory_type = #{inventoryType}
            </if>
            AND status = 2
            AND delete_flag = 0
        </where>
        GROUP BY material_code,object_id
    </select>
</mapper>
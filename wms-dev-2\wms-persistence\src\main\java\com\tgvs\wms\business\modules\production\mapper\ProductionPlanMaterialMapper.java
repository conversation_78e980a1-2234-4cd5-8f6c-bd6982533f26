package com.tgvs.wms.business.modules.production.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tgvs.wms.business.modules.production.entity.ProductionPlanMaterial;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 生产计划物料Mapper接口
 */
@Mapper
public interface ProductionPlanMaterialMapper extends BaseMapper<ProductionPlanMaterial> {

    /**
     * 分页查询生产计划物料
     */
    IPage<ProductionPlanMaterial> selectPlanMaterialPage(Page<ProductionPlanMaterial> page, 
                                                        @Param("planNo") String planNo,
                                                        @Param("materialCode") String materialCode,
                                                        @Param("materialName") String materialName,
                                                        @Param("status") Integer status);

    /**
     * 根据计划编号查询物料列表
     */
    List<ProductionPlanMaterial> selectByPlanNo(@Param("planNo") String planNo);

    /**
     * 根据计划编号查询热门物料列表
     */
    List<ProductionPlanMaterial> selectHotMaterialsByPlanNo(@Param("planNo") String planNo);

    /**
     * 批量更新物料状态
     */
    int batchUpdateStatus(@Param("ids") List<String> ids, @Param("status") Integer status, @Param("updateBy") String updateBy);

    /**
     * 根据物料编码查询最近的生产计划
     */
    List<ProductionPlanMaterial> selectRecentPlansByMaterialCode(@Param("materialCode") String materialCode, @Param("days") Integer days);

    /**
     * 统计物料使用频率
     */
    List<ProductionPlanMaterial> selectMaterialFrequency(@Param("days") Integer days);

    /**
     * 更新移库批次号
     */
    int updateRelocationBatchNo(@Param("ids") List<String> ids, @Param("batchNo") String batchNo, @Param("updateBy") String updateBy);
}
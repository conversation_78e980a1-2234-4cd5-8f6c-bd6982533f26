package com.tgvs.wms.business.enums;

import com.tgvs.wms.common.util.oConvertUtils;

public enum enumPrepareStatus {
    create(Integer.valueOf(0), "create", "创建"),
    execute(Integer.valueOf(25), "execute", "执行"),
    cancel(Integer.valueOf(30), "cancel", "取消"),
    complete(Integer.valueOf(35), "complete", "完成"),
    error(Integer.valueOf(40), "error", "异常"),
    finish(Integer.valueOf(255), "finish", "结束");

    private Integer value;

    private String code;

    private String text;

    enumPrepareStatus(Integer value, String code, String text) {
        this.value = value;
        this.code = code;
        this.text = text;
    }

    public static enumPrepareStatus getByValue(int value) {
        if (oConvertUtils.isEmpty(Integer.valueOf(value)))
            return null;
        for (enumPrepareStatus val : values()) {
            if (val.getValue().equals(Integer.valueOf(value)))
                return val;
        }
        return null;
    }

    public Integer getValue() {
        return this.value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getText() {
        return this.text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public static enumPrepareStatus toEnum(Integer Value) {
        for (enumPrepareStatus e : values()) {
            if (e.getValue().equals(Value))
                return e;
        }
        return null;
    }
}

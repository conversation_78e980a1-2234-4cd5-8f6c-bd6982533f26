package com.tgvs.wms.business.modules.machineMaterials.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tgvs.wms.business.modules.machineMaterials.entity.WmsMachineInventoryList;
import com.tgvs.wms.business.modules.machineMaterials.vo.MachineInventoryQueryVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 机物料盘点单Mapper接口
 */
public interface WmsMachineInventoryListMapper extends BaseMapper<WmsMachineInventoryList> {
    
    /**
     * 自定义条件查询机物料盘点单列表
     * 
     * @param Vo 查询条件
     * @return 机物料盘点单列表
     */
    List<WmsMachineInventoryList> selectWmsMachineInventoryListCustom(@Param("query") MachineInventoryQueryVo Vo);
} 
package com.tgvs.wms.business.enums;


import com.tgvs.wms.common.util.oConvertUtils;

public enum enumVritualSiteStatus {
    free(Integer.valueOf(0), "free", "空闲"),
    occupy(Integer.valueOf(1), "occupy", "占用"),
    allot(Integer.valueOf(2), "allot", "分配"),
    error(Integer.valueOf(255), "error", "异常");

    private Integer value;

    private String code;

    private String text;

    enumVritualSiteStatus(Integer value, String code, String text) {
        this.value = value;
        this.code = code;
        this.text = text;
    }

    public static enumVritualSiteStatus getByValue(int value) {
        if (oConvertUtils.isEmpty(Integer.valueOf(value)))
            return null;
        for (enumVritualSiteStatus val : values()) {
            if (val.getValue().equals(Integer.valueOf(value)))
                return val;
        }
        return null;
    }

    public Integer getValue() {
        return this.value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getText() {
        return this.text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public static enumVritualSiteStatus toEnum(Integer Value) {
        for (enumVritualSiteStatus e : values()) {
            if (e.getValue().equals(Value))
                return e;
        }
        return null;
    }
}

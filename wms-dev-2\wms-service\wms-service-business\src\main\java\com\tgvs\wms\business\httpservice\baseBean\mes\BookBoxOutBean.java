package com.tgvs.wms.business.httpservice.baseBean.mes;


import com.alibaba.fastjson.annotation.JSONField;

public class BookBoxOutBean {
    @J<PERSON>NField(name = "OrderNo")
    private Integer OrderNo;

    @J<PERSON>NField(name = "SiteNo")
    private String SiteNo;

    @J<PERSON>NField(name = "BoxNo")
    private String BoxNo;

    @JSONField(name = "Quantity")
    private Integer Quantity;

    @JSONField(name = "Priority")
    private Integer Priority;

    @JSONField(name = "UserNo")
    private String UserNo;

    public void setOrderNo(Integer OrderNo) {
        this.OrderNo = OrderNo;
    }

    public void setSiteNo(String SiteNo) {
        this.SiteNo = SiteNo;
    }

    public void setBoxNo(String BoxNo) {
        this.BoxNo = BoxNo;
    }

    public void setQuantity(Integer Quantity) {
        this.Quantity = Quantity;
    }

    public void setPriority(Integer Priority) {
        this.Priority = Priority;
    }

    public void setUserNo(String UserNo) {
        this.UserNo = UserNo;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof BookBoxOutBean))
            return false;
        BookBoxOutBean other = (BookBoxOutBean)o;
        if (!other.canEqual(this))
            return false;
        Object this$OrderNo = getOrderNo(), other$OrderNo = other.getOrderNo();
        if ((this$OrderNo == null) ? (other$OrderNo != null) : !this$OrderNo.equals(other$OrderNo))
            return false;
        Object this$SiteNo = getSiteNo(), other$SiteNo = other.getSiteNo();
        if ((this$SiteNo == null) ? (other$SiteNo != null) : !this$SiteNo.equals(other$SiteNo))
            return false;
        Object this$BoxNo = getBoxNo(), other$BoxNo = other.getBoxNo();
        if ((this$BoxNo == null) ? (other$BoxNo != null) : !this$BoxNo.equals(other$BoxNo))
            return false;
        Object this$Quantity = getQuantity(), other$Quantity = other.getQuantity();
        if ((this$Quantity == null) ? (other$Quantity != null) : !this$Quantity.equals(other$Quantity))
            return false;
        Object this$Priority = getPriority(), other$Priority = other.getPriority();
        if ((this$Priority == null) ? (other$Priority != null) : !this$Priority.equals(other$Priority))
            return false;
        Object this$UserNo = getUserNo(), other$UserNo = other.getUserNo();
        return !((this$UserNo == null) ? (other$UserNo != null) : !this$UserNo.equals(other$UserNo));
    }

    protected boolean canEqual(Object other) {
        return other instanceof BookBoxOutBean;
    }

    public int hashCode() {
        int PRIME = 59,
        result = 1;
        Object $OrderNo = getOrderNo();
        result = result * 59 + (($OrderNo == null) ? 43 : $OrderNo.hashCode());
        Object $SiteNo = getSiteNo();
        result = result * 59 + (($SiteNo == null) ? 43 : $SiteNo.hashCode());
        Object $BoxNo = getBoxNo();
        result = result * 59 + (($BoxNo == null) ? 43 : $BoxNo.hashCode());
        Object $Quantity = getQuantity();
        result = result * 59 + (($Quantity == null) ? 43 : $Quantity.hashCode());
        Object $Priority = getPriority();
        result = result * 59 + (($Priority == null) ? 43 : $Priority.hashCode());
        Object $UserNo = getUserNo();
        return result * 59 + (($UserNo == null) ? 43 : $UserNo.hashCode());
    }

    public String toString() {
        return "BookBoxOutBean(OrderNo=" + getOrderNo() + ", SiteNo=" + getSiteNo() + ", BoxNo=" + getBoxNo() + ", Quantity=" + getQuantity() + ", Priority=" + getPriority() + ", UserNo=" + getUserNo() + ")";
    }

    public Integer getOrderNo() {
        return this.OrderNo;
    }

    public String getSiteNo() {
        return this.SiteNo;
    }

    public String getBoxNo() {
        return this.BoxNo;
    }

    public Integer getQuantity() {
        return this.Quantity;
    }

    public Integer getPriority() {
        return this.Priority;
    }

    public String getUserNo() {
        return this.UserNo;
    }
}

package com.tgvs.wms.business.httpservice.baseBean.warehouse;


public class MFCSystemSub {
    private Integer id;

    private Integer in_long_error;

    private Integer out_long_error;

    private Integer level;

    private Integer pos;

    public void setId(Integer id) {
        this.id = id;
    }

    public void setIn_long_error(Integer in_long_error) {
        this.in_long_error = in_long_error;
    }

    public void setOut_long_error(Integer out_long_error) {
        this.out_long_error = out_long_error;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public void setPos(Integer pos) {
        this.pos = pos;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof MFCSystemSub))
            return false;
        MFCSystemSub other = (MFCSystemSub)o;
        if (!other.canEqual(this))
            return false;
        Object this$id = getId(), other$id = other.getId();
        if ((this$id == null) ? (other$id != null) : !this$id.equals(other$id))
            return false;
        Object this$in_long_error = getIn_long_error(), other$in_long_error = other.getIn_long_error();
        if ((this$in_long_error == null) ? (other$in_long_error != null) : !this$in_long_error.equals(other$in_long_error))
            return false;
        Object this$out_long_error = getOut_long_error(), other$out_long_error = other.getOut_long_error();
        if ((this$out_long_error == null) ? (other$out_long_error != null) : !this$out_long_error.equals(other$out_long_error))
            return false;
        Object this$level = getLevel(), other$level = other.getLevel();
        if ((this$level == null) ? (other$level != null) : !this$level.equals(other$level))
            return false;
        Object this$pos = getPos(), other$pos = other.getPos();
        return !((this$pos == null) ? (other$pos != null) : !this$pos.equals(other$pos));
    }

    protected boolean canEqual(Object other) {
        return other instanceof MFCSystemSub;
    }

    public int hashCode() {
        int PRIME = 59,
        result = 1;
        Object $id = getId();
        result = result * 59 + (($id == null) ? 43 : $id.hashCode());
        Object $in_long_error = getIn_long_error();
        result = result * 59 + (($in_long_error == null) ? 43 : $in_long_error.hashCode());
        Object $out_long_error = getOut_long_error();
        result = result * 59 + (($out_long_error == null) ? 43 : $out_long_error.hashCode());
        Object $level = getLevel();
        result = result * 59 + (($level == null) ? 43 : $level.hashCode());
        Object $pos = getPos();
        return result * 59 + (($pos == null) ? 43 : $pos.hashCode());
    }

    public String toString() {
        return "MFCSystemSub(id=" + getId() + ", in_long_error=" + getIn_long_error() + ", out_long_error=" + getOut_long_error() + ", level=" + getLevel() + ", pos=" + getPos() + ")";
    }

    public Integer getId() {
        return this.id;
    }

    public Integer getIn_long_error() {
        return this.in_long_error;
    }

    public Integer getOut_long_error() {
        return this.out_long_error;
    }

    public Integer getLevel() {
        return this.level;
    }

    public Integer getPos() {
        return this.pos;
    }
}
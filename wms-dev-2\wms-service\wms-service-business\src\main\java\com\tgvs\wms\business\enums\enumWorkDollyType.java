package com.tgvs.wms.business.enums;


import com.tgvs.wms.common.util.oConvertUtils;

public enum enumWorkDollyType {
    unknow(Integer.valueOf(0), "unknow", "无松布架"),
    collect_full_pallet(Integer.valueOf(1), "collect_full_pallet", "收集满布松布架"),
    collect_empty_pallet(Integer.valueOf(2), "collect_empty_pallet", "收集空板松布架"),
    collect_surplus_pallet(Integer.valueOf(3), "collect_surplus_pallet", "收集余布松布架"),
    supply_full_pallet(Integer.valueOf(4), "collect_surplus_pallet", "提供满布松布架"),
    supply_empty_pallet(Integer.valueOf(5), "collect_surplus_pallet", "提供空板松布架"),
    supply_surplus_pallet(Integer.valueOf(6), "collect_surplus_pallet", "提供余布松布架");

    private Integer value;

    private String code;

    private String text;

    enumWorkDollyType(Integer value, String code, String text) {
        this.value = value;
        this.code = code;
        this.text = text;
    }

    public static enumWorkDollyType getByValue(int value) {
        if (oConvertUtils.isEmpty(Integer.valueOf(value)))
            return null;
        for (enumWorkDollyType val : values()) {
            if (val.getValue().equals(Integer.valueOf(value)))
                return val;
        }
        return null;
    }

    public Integer getValue() {
        return this.value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getText() {
        return this.text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public static enumWorkDollyType toEnum(Integer Value) {
        for (enumWorkDollyType e : values()) {
            if (e.getValue().equals(Value))
                return e;
        }
        return null;
    }
}

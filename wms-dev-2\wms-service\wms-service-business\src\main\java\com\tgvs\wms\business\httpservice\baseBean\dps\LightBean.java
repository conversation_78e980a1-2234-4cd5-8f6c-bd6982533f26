package com.tgvs.wms.business.httpservice.baseBean.dps;


import java.util.List;

import com.alibaba.fastjson.annotation.JSONField;

public class LightBean {
    @J<PERSON>NField(name = "AddrAndMsgList")
    private List<AddrAndMsg> AddrAndMsgList;

    @JSONField(name = "LMDigitNumber")
    private int LMDigitNumber;

    @JSONField(name = "M1ModeInfo")
    private ModeInfo M1ModeInfo;

    @JSONField(name = "M2ModeInfo")
    private ModeInfo M2ModeInfo;

    @JSONField(name = "M3ModeInfo")
    private ModeInfo M3ModeInfo;

    @JSONField(name = "M4ModeInfo")
    private ModeInfo M4ModeInfo;

    public void setAddrAndMsgList(List<AddrAndMsg> AddrAndMsgList) {
        this.AddrAndMsgList = AddrAndMsgList;
    }

    public void setLMDigitNumber(int LMDigitNumber) {
        this.LMDigitNumber = LMDigitNumber;
    }

    public void setM1ModeInfo(ModeInfo M1ModeInfo) {
        this.M1ModeInfo = M1ModeInfo;
    }

    public void setM2ModeInfo(ModeInfo M2ModeInfo) {
        this.M2ModeInfo = M2ModeInfo;
    }

    public void setM3ModeInfo(ModeInfo M3ModeInfo) {
        this.M3ModeInfo = M3ModeInfo;
    }

    public void setM4ModeInfo(ModeInfo M4ModeInfo) {
        this.M4ModeInfo = M4ModeInfo;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof LightBean))
            return false;
        LightBean other = (LightBean)o;
        if (!other.canEqual(this))
            return false;
        List<AddrAndMsg> this$AddrAndMsgList = (List<AddrAndMsg>)getAddrAndMsgList(), other$AddrAndMsgList = (List<AddrAndMsg>)other.getAddrAndMsgList();
        if ((this$AddrAndMsgList == null) ? (other$AddrAndMsgList != null) : !this$AddrAndMsgList.equals(other$AddrAndMsgList))
            return false;
        if (getLMDigitNumber() != other.getLMDigitNumber())
            return false;
        Object this$M1ModeInfo = getM1ModeInfo(), other$M1ModeInfo = other.getM1ModeInfo();
        if ((this$M1ModeInfo == null) ? (other$M1ModeInfo != null) : !this$M1ModeInfo.equals(other$M1ModeInfo))
            return false;
        Object this$M2ModeInfo = getM2ModeInfo(), other$M2ModeInfo = other.getM2ModeInfo();
        if ((this$M2ModeInfo == null) ? (other$M2ModeInfo != null) : !this$M2ModeInfo.equals(other$M2ModeInfo))
            return false;
        Object this$M3ModeInfo = getM3ModeInfo(), other$M3ModeInfo = other.getM3ModeInfo();
        if ((this$M3ModeInfo == null) ? (other$M3ModeInfo != null) : !this$M3ModeInfo.equals(other$M3ModeInfo))
            return false;
        Object this$M4ModeInfo = getM4ModeInfo(), other$M4ModeInfo = other.getM4ModeInfo();
        return !((this$M4ModeInfo == null) ? (other$M4ModeInfo != null) : !this$M4ModeInfo.equals(other$M4ModeInfo));
    }

    protected boolean canEqual(Object other) {
        return other instanceof LightBean;
    }

    public int hashCode() {
        int PRIME = 59,
        result = 1;
        List<AddrAndMsg> $AddrAndMsgList = (List<AddrAndMsg>)getAddrAndMsgList();
        result = result * 59 + (($AddrAndMsgList == null) ? 43 : $AddrAndMsgList.hashCode());
        result = result * 59 + getLMDigitNumber();
        Object $M1ModeInfo = getM1ModeInfo();
        result = result * 59 + (($M1ModeInfo == null) ? 43 : $M1ModeInfo.hashCode());
        Object $M2ModeInfo = getM2ModeInfo();
        result = result * 59 + (($M2ModeInfo == null) ? 43 : $M2ModeInfo.hashCode());
        Object $M3ModeInfo = getM3ModeInfo();
        result = result * 59 + (($M3ModeInfo == null) ? 43 : $M3ModeInfo.hashCode());
        Object $M4ModeInfo = getM4ModeInfo();
        return result * 59 + (($M4ModeInfo == null) ? 43 : $M4ModeInfo.hashCode());
    }

    public String toString() {
        return "LightBean(AddrAndMsgList=" + getAddrAndMsgList() + ", LMDigitNumber=" + getLMDigitNumber() + ", M1ModeInfo=" + getM1ModeInfo() + ", M2ModeInfo=" + getM2ModeInfo() + ", M3ModeInfo=" + getM3ModeInfo() + ", M4ModeInfo=" + getM4ModeInfo() + ")";
    }

    public List<AddrAndMsg> getAddrAndMsgList() {
        return this.AddrAndMsgList;
    }

    public int getLMDigitNumber() {
        return this.LMDigitNumber;
    }

    public ModeInfo getM1ModeInfo() {
        return this.M1ModeInfo;
    }

    public ModeInfo getM2ModeInfo() {
        return this.M2ModeInfo;
    }

    public ModeInfo getM3ModeInfo() {
        return this.M3ModeInfo;
    }

    public ModeInfo getM4ModeInfo() {
        return this.M4ModeInfo;
    }
}

package com.tgvs.wms.business.modules.storage.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tgvs.wms.base.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.io.Serializable;
import lombok.Data;


@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName ( "wms_station" )
@ApiModel(value = "wms_station对象", description = "站点管理")
@Data
public class WmsStation  extends BaseEntity {

	private static final long serialVersionUID =  1125167523697984290L;



	/**
	 * 站点类型：1.料箱入库口；2.料箱出库口；3.料架出入库口
	 */
   	@TableField(value = "station_type")
	private Integer stationType;

	/**
	 * 站点编码
	 */
   	@TableField(value = "station_code")
	private String stationCode;

	/**
	 * 站点名称
	 */
   	@TableField(value = "station_name")
	private String stationName;

	/**
	 * 站点状态：0.启用；1.禁用
	 */
   	@TableField(value = "station_status")
	private Integer stationStatus;

}

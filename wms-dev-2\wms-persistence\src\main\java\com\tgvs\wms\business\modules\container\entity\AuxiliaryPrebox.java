package com.tgvs.wms.business.modules.container.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tgvs.wms.base.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 辅料预装箱实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wms_auxiliary_prebox")
public class AuxiliaryPrebox extends BaseEntity {

    /**
     * 预装箱批次号
     */
    @TableField("stock_batch_id")
    private String stockBatchId;

    /**
     * 任务单号
     */
    @TableField("task_order")
    private String taskOrder;

    /**
     * 入库单Id
     */
    @TableField("stock_in_id")
    private String stockInId;
    /**
     * 入库单单号
     */
    @TableField("stock_in_no")
    private String stockInNo;

    /**
     * 入库单明细表(wms_auxiliary_detail)ID，包含的明细ID列表(逗号分隔)
     */
    @TableField("detail_ids")
    private String detailIds;

    /**
     * 合约号
     */
    @TableField("contract_no")
    private String contractNo;

    /**
     * 款号
     */
    @TableField("item_no")
    private String itemNo;

    /**
     * 物料编码
     */
    @TableField("material_code")
    private String materialCode;

    /**
     * 物料名称
     */
    @TableField("material_name")
    private String materialName;

    /**
     * 待入库数量
     */
    @TableField("material_quantity")
    private BigDecimal materialQuantity;

    /**
     * 实际入库数量
     */
    @TableField("actual_inbound_qty")
    private BigDecimal actualInboundQty;

    /**
     * 物料规格
     */
    @TableField("material_model")
    private String materialModel;

    /**
     * 尺码
     */
    @TableField("material_size")
    private String materialSize;

    /**
     * 物料颜色
     */
    @TableField("material_color")
    private String materialColor;

    /**
     * 容器号
     */
    @TableField("container_no")
    private String containerNo;

    /**
     * 格号
     */
    @TableField("grid_id")
    private String gridId;

    /**
     * 格号容量
     */
    @TableField("box_volume")
    private Integer boxVolume;

    /**
     * 容器类型(1-料箱 2-托盘号)
     */
    @TableField("container_type")
    private Integer containerType;

    /**
     * 物料属性(0自身属性，1款式属性）
     */
    @TableField("material_property")
    private Integer materialProperty;

    /**
     * 预装箱顺序
     */
    @TableField("prebox_order")
    private Integer preboxOrder;

    /**
     * PO号
     */
    @TableField("po_no")
    private String poNo;

    @TableField("status")
    private Integer status;

    /**
     * 任务状态（关联查询字段，非数据库字段）
     * 0-创建，1-任务已下发，2-执行搬运，3-输送线运输，4-完成
     */
    @TableField(exist = false)
    private Integer taskStatus;
    
    @TableField("is_deleted")
    private Integer isDeleted;

    /**
     * 是否入智能仓标识
     * 1-入智能仓，0-入虚拟仓，null-默认入虚拟仓
     */
    @TableField("is_smart_warehouse")
    private Integer isSmartWarehouse;
}

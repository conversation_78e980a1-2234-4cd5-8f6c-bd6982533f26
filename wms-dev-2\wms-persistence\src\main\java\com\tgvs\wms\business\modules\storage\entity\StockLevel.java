package com.tgvs.wms.business.modules.storage.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TableName("wms_stock")
@ApiModel(value = "wms_stock对象", description = "库存管理")
@Data
public class StockLevel implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("层位")
    private Integer level;

    @ApiModelProperty("分区")
    private String batch;

    @ApiModelProperty("数量")
    private Integer count;
}

package com.tgvs.wms.business.modules.dispatch.entity;


import java.util.Date;

import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.tgvs.wms.base.entity.BaseEntity;
import com.tgvs.wms.common.annotation.Dict;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TableName("wms_agv_dolly")
@ApiModel(value = "wms_agv_dolly对象", description = "缓存松布架管理")
@Data
public class AgvDolly extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键")
    private String id;

    @Excel(name = "站点编码", width = 15.0D, dictTable = "wms_vritual_site", dicText = "site_code", dicCode = "site_code")
    @Dict(dictTable = "wms_vritual_site", dicText = "site_code", dicCode = "site_code")
    @ApiModelProperty("站点编码")
    private String siteCode;

    @Excel(name = "松布架号", width = 15.0D)
    @ApiModelProperty("松布架号")
    private String dollyNo;

    @Excel(name = "松布架层数", width = 15.0D, dicCode = "dolly_layers_type")
    @Dict(dicCode = "dolly_layers_type")
    @ApiModelProperty("松布架层数")
    private Integer dollyLayers;

    @Excel(name = "松布架类型", width = 15.0D, dicCode = "dolly_type")
    @Dict(dicCode = "dolly_type")
    @ApiModelProperty("松布架类型")
    private Integer dollyType;

    @Excel(name = "区域", width = 15.0D, dictTable = "wms_base_area", dicText = "text", dicCode = "code")
    @Dict(dictTable = "wms_base_area", dicText = "text", dicCode = "code")
    @ApiModelProperty("区域")
    private String area;

    @Excel(name = "智能设备", width = 15.0D, dictTable = "wms_commncation_device", dicText = "deviceName", dicCode = "deviceNo")
    @Dict(dictTable = "wms_commncation_device", dicText = "deviceName", dicCode = "deviceNo")
    @ApiModelProperty("智能设备")
    private String robot;

    @Excel(name = "货位", width = 15.0D)
    @ApiModelProperty("货位")
    private Integer location;

    @Excel(name = "层位", width = 15.0D)
    @ApiModelProperty("层位")
    private Integer level;

    @Excel(name = "行位", width = 15.0D)
    @ApiModelProperty("行位")
    private Integer rowNum;

    @Excel(name = "列位", width = 15.0D)
    @ApiModelProperty("列位")
    private Integer columnNumber;

    @Excel(name = "层高度", width = 15.0D, dicCode = "location_height")
    @Dict(dicCode = "location_height")
    @ApiModelProperty("层高度")
    private Integer height;

    @Excel(name = "坯布重量", width = 15.0D)
    @ApiModelProperty("坯布重量")
    private Integer clothWeight;

    @Excel(name = "锁定", width = 15.0D, dicCode = "locked_status")
    @Dict(dicCode = "locked_status")
    @ApiModelProperty("锁定")
    private Integer locked;

    @Excel(name = "禁止搬运", width = 15.0D, dicCode = "yn")
    @Dict(dicCode = "yn")
    @ApiModelProperty("禁止搬运")
    private Integer disable;

    @Excel(name = "操作标记", width = 15.0D, dicCode = "yn")
    @Dict(dicCode = "yn")
    @ApiModelProperty("操作标记")
    private Integer operatetag;

    @Excel(name = "优先级", width = 15.0D)
    @ApiModelProperty("优先级")
    private Integer priority;

    @Excel(name = "货位状态", width = 15.0D, dicCode = "location_status")
    @Dict(dicCode = "location_status")
    @ApiModelProperty("货位状态")
    private Integer state;

    @Excel(name = "布匹条码", width = 15.0D)
    @ApiModelProperty("布匹条码")
    private String clothNo;

    @Excel(name = "是否有板", width = 15.0D, dicCode = "yn")
    @Dict(dicCode = "yn")
    @ApiModelProperty("是否有板")
    private Integer ispallet;

    @Excel(name = "装载类型", width = 15.0D, dicCode = "pallet_type")
    @Dict(dicCode = "pallet_type")
    @ApiModelProperty("装载类型")
    private Integer palletType;

    @Excel(name = "板高度", width = 15.0D, dicCode = "pallet_height")
    @Dict(dicCode = "pallet_height")
    @ApiModelProperty("板高度")
    private Integer palletHeight;

    @Excel(name = "拉布单号", width = 15.0D)
    @ApiModelProperty("拉布单号")
    private String billno;

    @Excel(name = "来源松布机", width = 15.0D)
    @ApiModelProperty("来源松布机")
    private String fromLatheNo;

    @Excel(name = "对应铺床", width = 15.0D)
    @ApiModelProperty("对应铺床")
    private String toLatheNo;

    @Excel(name = "铺布计划", width = 15.0D)
    @ApiModelProperty("铺布计划")
    private String pubuplan;

    @Excel(name = "缸号", width = 15.0D)
    @ApiModelProperty("缸号")
    private String vatno;

    @Excel(name = "铺布计划时间", width = 15.0D, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("铺布计划时间")
    private Date planDate;

    @Excel(name = "物料编码", width = 15.0D)
    @ApiModelProperty("物料编码")
    private String goodsCode;

    @Excel(name = "订单编号", width = 15.0D)
    @ApiModelProperty("订单编号")
    private String orderCode;

    @Excel(name = "工单号", width = 15.0D)
    @ApiModelProperty("工单号")
    private String workNo;

    @ApiModelProperty("创建人")
    private String createBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("更新时间")
    private Date updateTime;

    @Excel(name = "删除状态", width = 15.0D)
    @ApiModelProperty("删除状态")
    private Integer delFlag;
}

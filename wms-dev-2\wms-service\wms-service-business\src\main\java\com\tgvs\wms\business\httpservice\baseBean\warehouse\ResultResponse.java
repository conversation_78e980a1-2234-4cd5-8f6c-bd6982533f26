package com.tgvs.wms.business.httpservice.baseBean.warehouse;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

public class ResultResponse {
    private Integer id;

    @JsonProperty("messageName")
    private String messageName;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String wmsId;

    @JsonProperty("result")
    private Integer result;

    @JsonProperty("type")
    private Integer type;

    public void setId(Integer id) {
        this.id = id;
    }

    public void setMessageName(String messageName) {
        this.messageName = messageName;
    }

    public void setWmsId(String wmsId) {
        this.wmsId = wmsId;
    }

    public void setResult(Integer result) {
        this.result = result;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof ResultResponse))
            return false;
        ResultResponse other = (ResultResponse)o;
        if (!other.canEqual(this))
            return false;
        Object this$id = getId(), other$id = other.getId();
        if ((this$id == null) ? (other$id != null) : !this$id.equals(other$id))
            return false;
        Object this$messageName = getMessageName(), other$messageName = other.getMessageName();
        if ((this$messageName == null) ? (other$messageName != null) : !this$messageName.equals(other$messageName))
            return false;
        Object this$wmsId = getWmsId(), other$wmsId = other.getWmsId();
        if ((this$wmsId == null) ? (other$wmsId != null) : !this$wmsId.equals(other$wmsId))
            return false;
        Object this$result = getResult(), other$result = other.getResult();
        if ((this$result == null) ? (other$result != null) : !this$result.equals(other$result))
            return false;
        Object this$type = getType(), other$type = other.getType();
        return !((this$type == null) ? (other$type != null) : !this$type.equals(other$type));
    }

    protected boolean canEqual(Object other) {
        return other instanceof ResultResponse;
    }

    public int hashCode() {
        int PRIME = 59,
        result = 1;
        Object $id = getId();
        result = result * 59 + (($id == null) ? 43 : $id.hashCode());
        Object $messageName = getMessageName();
        result = result * 59 + (($messageName == null) ? 43 : $messageName.hashCode());
        Object $wmsId = getWmsId();
        result = result * 59 + (($wmsId == null) ? 43 : $wmsId.hashCode());
        Object $result = getResult();
        result = result * 59 + (($result == null) ? 43 : $result.hashCode());
        Object $type = getType();
        return result * 59 + (($type == null) ? 43 : $type.hashCode());
    }

    public String toString() {
        return "ResultResponse(id=" + getId() + ", messageName=" + getMessageName() + ", wmsId=" + getWmsId() + ", result=" + getResult() + ", type=" + getType() + ")";
    }

    public Integer getId() {
        return this.id;
    }

    public String getMessageName() {
        return this.messageName;
    }

    public String getWmsId() {
        return this.wmsId;
    }

    public Integer getResult() {
        return this.result;
    }

    public Integer getType() {
        return this.type;
    }
}

package com.tgvs.wms.business.enums;

import com.tgvs.wms.common.util.oConvertUtils;

public enum enumMsgType {

    msg_type_mes2abb("mes2abb", "mes_abb", "mesToAbb");

    private String value;

    private String code;

    private String text;

    enumMsgType(String value, String code, String text) {
        this.value = value;
        this.code = code;
        this.text = text;
    }

    public static enumMsgType getByValue(String value) {
        if (oConvertUtils.isEmpty(value))
            return null;
        for (enumMsgType val : values()) {
            if (val.getValue().equals(value))
                return val;
        }
        return null;
    }

    public String getValue() {
        return this.value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getText() {
        return this.text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public static enumMsgType toEnum(Integer Value) {
        for (enumMsgType e : values()) {
            if (e.getValue().equals(Value))
                return e;
        }
        return null;
    }
}

package com.tgvs.wms.business.enums;

import lombok.Getter;

/**
 * 物料类型枚举
 * 用于标识容器中存放的物料类型
 */
@Getter
public enum MaterialType {
    
    /**
     * 空容器 - 没有物料
     */
    EMPTY(0, "空容器", "容器中没有物料"),
    
    /**
     * 辅料 - 存放辅料
     */
    AUXILIARY(1, "辅料", "容器中存放辅料");
    
    /**
     * 类型值
     */
    private final int value;
    
    /**
     * 类型名称
     */
    private final String name;
    
    /**
     * 类型描述
     */
    private final String description;
    
    MaterialType(int value, String name, String description) {
        this.value = value;
        this.name = name;
        this.description = description;
    }
    
    /**
     * 根据类型值获取枚举
     * @param value 类型值
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static MaterialType getByValue(int value) {
        for (MaterialType type : values()) {
            if (type.getValue() == value) {
                return type;
            }
        }
        return null;
    }
    
    /**
     * 判断是否为空容器
     */
    public boolean isEmpty() {
        return this == EMPTY;
    }
    
    /**
     * 判断是否为辅料
     */
    public boolean isAuxiliary() {
        return this == AUXILIARY;
    }
}

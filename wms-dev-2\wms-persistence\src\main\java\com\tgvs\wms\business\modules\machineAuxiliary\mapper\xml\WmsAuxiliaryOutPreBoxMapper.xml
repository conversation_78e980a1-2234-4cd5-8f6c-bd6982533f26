<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tgvs.wms.business.modules.machineAuxiliary.mapper.WmsAuxiliaryOutPreBoxMapper">

    <!-- 根据出库单ID查询预占用记录 -->
    <select id="selectByOutboundIds" resultType="com.tgvs.wms.business.modules.machineAuxiliary.entity.WmsAuxiliaryOutPreBox">
        SELECT 
            id,
            outbound_id,
            outbound_no,
            box_item_id,
            box_code,
            container_type,
            shelf_code,
            grid_code,
            contract_no,
            style_no,
            material_code,
            material_name,
            material_spec,
            material_color,
            material_color_code,
            material_property,
            material_unit,
            reserved_quantity,
            original_stock_quantity,
            outbound_type,
            reservation_status,
            reservation_time,
            confirm_time,
            cancel_time,
            operator,
            remark,
            create_time,
            update_time,
            create_user,
            update_user,
            delete_flag
        FROM wms_auxiliary_outprebox
        WHERE outbound_id IN
        <foreach collection="outboundIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND delete_flag = 0
        ORDER BY create_time ASC
    </select>

    <!-- 根据物料编码查询预占用数量（排除指定出库单） -->
    <select id="selectReservedByMaterialCodes" resultType="com.tgvs.wms.business.modules.machineAuxiliary.entity.WmsAuxiliaryOutPreBox">
        SELECT 
            id,
            outbound_id,
            box_item_id,
            material_code,
            contract_no,
            style_no,
            material_color,
            material_spec,
            reserved_quantity,
            reservation_status
        FROM wms_auxiliary_outprebox
        WHERE material_code IN
        <foreach collection="materialCodes" item="materialCode" open="(" separator="," close=")">
            #{materialCode}
        </foreach>
        AND reservation_status = 1
        AND delete_flag = 0
        <if test="excludeOutboundIds != null and excludeOutboundIds.size() > 0">
            AND outbound_id NOT IN
            <foreach collection="excludeOutboundIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        ORDER BY reservation_time ASC
    </select>

    <!-- 根据库存项ID查询预占用数量 -->
    <select id="selectReservedByBoxItemIds" resultType="com.tgvs.wms.business.modules.machineAuxiliary.entity.WmsAuxiliaryOutPreBox">
        SELECT 
            id,
            outbound_id,
            box_item_id,
            material_code,
            reserved_quantity,
            reservation_status,
            reservation_time
        FROM wms_auxiliary_outprebox
        WHERE box_item_id IN
        <foreach collection="boxItemIds" item="boxItemId" open="(" separator="," close=")">
            #{boxItemId}
        </foreach>
        <if test="status != null">
            AND reservation_status = #{status}
        </if>
        AND delete_flag = 0
        ORDER BY reservation_time ASC
    </select>

    <!-- 批量更新预占用状态 -->
    <update id="updateStatusByOutboundIds">
        UPDATE wms_auxiliary_outprebox 
        SET 
            reservation_status = #{newStatus},
            update_time = NOW()
            <if test="newStatus == 2">
                ,confirm_time = NOW()
            </if>
            <if test="newStatus == 3">
                ,cancel_time = NOW()
            </if>
        WHERE outbound_id IN
        <foreach collection="outboundIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="oldStatus != null">
            AND reservation_status = #{oldStatus}
        </if>
        AND delete_flag = 0
    </update>

    <!-- 根据物料编码和合约信息查询预占用汇总 -->
    <select id="selectReservedSummaryByMaterial" resultType="map">
        SELECT 
            material_code,
            contract_no,
            style_no,
            material_color,
            material_spec,
            SUM(reserved_quantity) as total_reserved_quantity,
            COUNT(*) as reservation_count
        FROM wms_auxiliary_outprebox
        WHERE material_code = #{materialCode}
        <if test="contractNo != null and contractNo != ''">
            AND contract_no = #{contractNo}
        </if>
        <if test="styleNo != null and styleNo != ''">
            AND style_no = #{styleNo}
        </if>
        <if test="materialColor != null and materialColor != ''">
            AND material_color = #{materialColor}
        </if>
        <if test="materialSpec != null and materialSpec != ''">
            AND material_spec = #{materialSpec}
        </if>
        AND reservation_status = 1
        AND delete_flag = 0
        GROUP BY material_code, contract_no, style_no, material_color, material_spec
    </select>

    <!-- 查询即将超时的预占用记录 -->
    <select id="selectExpiredReservations" resultType="com.tgvs.wms.business.modules.machineAuxiliary.entity.WmsAuxiliaryOutPreBox">
        SELECT 
            id,
            outbound_id,
            outbound_no,
            material_code,
            reserved_quantity,
            reservation_time
        FROM wms_auxiliary_outprebox
        WHERE reservation_status = 1
        AND delete_flag = 0
        AND TIMESTAMPDIFF(MINUTE, reservation_time, NOW()) > #{timeoutMinutes}
        ORDER BY reservation_time ASC
        LIMIT #{limit}
    </select>

    <!-- 根据出库单号查询预占用记录 -->
    <select id="selectByOutboundNo" resultType="com.tgvs.wms.business.modules.machineAuxiliary.entity.WmsAuxiliaryOutPreBox">
        SELECT 
            id,
            outbound_id,
            outbound_no,
            material_code,
            material_name,
            reserved_quantity,
            reservation_status,
            reservation_time
        FROM wms_auxiliary_outprebox
        WHERE outbound_no = #{outboundNo}
        AND delete_flag = 0
        ORDER BY reservation_time ASC
    </select>

    <!-- 查询库存项的预占用统计 -->
    <select id="selectBoxItemReservationStat" resultType="map">
        SELECT 
            box_item_id,
            box_code,
            container_type,
            material_code,
            SUM(reserved_quantity) as total_reserved,
            COUNT(DISTINCT outbound_id) as outbound_count,
            MIN(reservation_time) as earliest_reservation,
            MAX(reservation_time) as latest_reservation
        FROM wms_auxiliary_outprebox
        WHERE box_item_id IN
        <foreach collection="boxItemIds" item="boxItemId" open="(" separator="," close=")">
            #{boxItemId}
        </foreach>
        AND reservation_status = 1
        AND delete_flag = 0
        GROUP BY box_item_id, box_code, container_type, material_code
        ORDER BY total_reserved DESC
    </select>

</mapper> 
package com.tgvs.wms.business.modules.container.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tgvs.wms.business.modules.container.Dto.materialOutBoundDetailDTO;
import com.tgvs.wms.business.modules.container.entity.BoxItem;
import com.tgvs.wms.business.modules.container.Dto.machineInfoConvergeBoxDTO;

import java.math.BigDecimal;
import java.util.List;

public interface BoxItemMapper extends BaseMapper<BoxItem> {
    /**
     *
     * @param materialCode ,boxType 查询条件
     */
    List<BoxItem> selectMaterialCode(String materialCode,int boxType);

    /**
     *
     * @param materialCode ,boxType 查询条件
     */
    List<BoxItem> selectMaterialCodeStock(String materialCode,int boxType);
    /**
     *
     * @param boxNo 查询条件
     */
    List<BoxItem> selectEmptyboxNo(String boxNo);
    /**
     *
     * @param boxType 查询条件 查询未满料架数据
     */
    List<BoxItem> selectTray(int boxType);

    /**
     *获取单个容器指定机物料库存,排除已完成的任务料箱
     * @param materialCode
     * @return
     */
    materialOutBoundDetailDTO materialOutBound(String materialCode, BigDecimal quantity);

    /**
     *获取指定机物料库存列表
     * @param materialCode
     * @return
     */
    List<materialOutBoundDetailDTO> materialOutBoundList(String materialCode);

    /**
     * 根据机物料编码校验库存是否足够
     * @param materialCode
     * @return
     */
    materialOutBoundDetailDTO checkInventory(String materialCode);

    /**
     *
     * @param boxNo
     * @param materialCode
     * @return
     */
    List<materialOutBoundDetailDTO> materialBoxGridSum(String boxNo,String materialCode);

    /**
     * 根据机物料编码获取料箱每一格的物料库存信息
     * @param materialCode
     * @return
     */
    List<materialOutBoundDetailDTO> boxGridInventoryList(String materialCode);
    /**
     *
     * @param  boxType 查询条件
     */
    List<BoxItem> selectMaterialCodeTray(int boxType);

    /**
     * 获取可合箱的库存数据
     * @return
     */
    List<machineInfoConvergeBoxDTO> machineInfoConvergeBoxList();
}

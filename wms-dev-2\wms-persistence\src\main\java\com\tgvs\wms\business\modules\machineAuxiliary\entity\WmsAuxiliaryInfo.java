package com.tgvs.wms.business.modules.machineAuxiliary.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.tgvs.wms.base.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 辅料基础信息表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("wms_auxiliary_info")
public class WmsAuxiliaryInfo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 分类
     */
    private String category;

    /**
     * 单位
     */
    private String unit;

    /**
     * 容积（箱容量）
     */
    private Integer capacity;

    /**
     * 优先料箱类型（格子数）
     * 1-1格箱, 2-2格箱, 3-3格箱, 4-4格箱, 6-6格箱, 8-8格箱
     * 用于智能容器分配，优先选择指定格子数的料箱
     */
    private Integer priorityContainerType;

    /**
     * 物料属性（0自身属性，1款式属性）
     */
    private Integer materialType;

    /**
     * 物料类型
     */
    private Integer materialCategory;

    /**
     * 是否入智能仓
     */
    private Integer isStore;

    /**
     * 备注
     */
    private String remark;

    /**
     * 物料颜色
     */
    private String materialColor;

    /**
     * 物料颜色Id
     */
    private String materialColorId;

    /**
     * 规格
     */
    private String specification;

    /**
     * 客户（供应商）
     */
    private String customerName;

    /**
     * 客户ID
     */
    private String customerId;
}

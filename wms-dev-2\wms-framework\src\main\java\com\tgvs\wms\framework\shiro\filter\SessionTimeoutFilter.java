package com.tgvs.wms.framework.shiro.filter;

import java.io.IOException;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.subject.Subject;
import org.apache.shiro.web.filter.AccessControlFilter;
import org.springframework.beans.factory.annotation.Value;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tgvs.wms.common.constant.Result;

/**
 * 会话超时检查过滤器
 * 专门用于处理需要用户认证的页面和接口的会话超时问题
 * 
 * <AUTHOR>
 */
public class SessionTimeoutFilter extends AccessControlFilter
{
    /**
     * 登录页面地址
     */
    @Value("${shiro:/index.html}")
    private String loginUrl;

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 表示是否允许访问；mappedValue就是[urls]配置中拦截器参数部分，如果允许访问返回true，否则false；
     */
    @Override
    protected boolean isAccessAllowed(ServletRequest request, ServletResponse response, Object mappedValue) throws Exception
    {
        Subject subject = getSubject(request, response);
        // 检查用户是否已认证
        boolean authenticated = subject.isAuthenticated();

        // 记录日志用于调试
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        System.out.println("SessionTimeoutFilter - 路径: " + httpRequest.getRequestURI() + ", 已认证: " + authenticated);

        return authenticated;
    }

    /**
     * 表示当访问拒绝时是否已经处理了；如果返回true表示需要继续处理；如果返回false表示该拦截器实例已经处理了，将直接返回即可。
     */
    @Override
    protected boolean onAccessDenied(ServletRequest request, ServletResponse response) throws Exception
    {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        
        // 清理可能存在的无效会话
        Subject subject = getSubject(request, response);
        if (subject != null && subject.getSession(false) != null) {
            try {
                subject.logout();
            } catch (Exception e) {
                // 忽略登出异常
            }
        }
        
        // 判断是否为Ajax请求
        String requestedWith = httpRequest.getHeader("X-Requested-With");
        String contentType = httpRequest.getHeader("Content-Type");
        String accept = httpRequest.getHeader("Accept");
        
        boolean isAjaxRequest = "XMLHttpRequest".equals(requestedWith) || 
                               (contentType != null && contentType.contains("application/json")) ||
                               (accept != null && accept.contains("application/json"));
        
        if (isAjaxRequest) {
            // Ajax请求返回JSON格式的错误信息
            handleAjaxRequest(httpResponse);
        } else {
            // 普通请求重定向到登录页面
            handlePageRequest(httpRequest, httpResponse);
        }
        
        return false;
    }
    
    /**
     * 处理Ajax请求的会话超时
     */
    private void handleAjaxRequest(HttpServletResponse response) throws IOException {
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json; charset=utf-8");
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        
        Result<?> result = Result.error("登录已过期，请重新登录");
        result.setCode(401);
        
        String jsonResponse = objectMapper.writeValueAsString(result);
        response.getWriter().write(jsonResponse);
        response.getWriter().flush();
    }
    
    /**
     * 处理页面请求的会话超时
     */
    private void handlePageRequest(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String requestUrl = request.getRequestURL().toString();
        String queryString = request.getQueryString();

        // 构建完整的请求URL
        if (queryString != null) {
            requestUrl += "?" + queryString;
        }

        // 保存原始请求URL到session中，登录成功后可以跳转回来
        try {
            request.getSession().setAttribute("savedRequest", requestUrl);
        } catch (Exception e) {
            // 忽略session异常
        }

        // 重定向到登录页面 - 确保URL正确
        String contextPath = request.getContextPath();
        String redirectUrl;
        if (contextPath != null && !contextPath.isEmpty()) {
            redirectUrl = contextPath + loginUrl;
        } else {
            redirectUrl = loginUrl;
        }

        System.out.println("SessionTimeoutFilter - 重定向到: " + redirectUrl);
        response.sendRedirect(redirectUrl);
    }
    
    public void setLoginUrl(String loginUrl) {
        this.loginUrl = loginUrl;
    }
}

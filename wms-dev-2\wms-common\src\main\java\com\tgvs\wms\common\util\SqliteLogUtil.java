package com.tgvs.wms.common.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.sql.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;

/**
 * SQLite日志工具类 - 增强版
 * 支持数据清理、大小限制、监控告警
 */
@Slf4j
@Component
public class SqliteLogUtil {

    private static final String DB_PATH = "d:/tmp/log/api_log.db";
    private static final String DB_URL = "jdbc:sqlite:" + DB_PATH;

    // 智能配置参数
    @Value("${wms.log.sqlite.disk-usage-percent:80}")
    private int maxDiskUsagePercent = 80; // 最大磁盘使用百分比

    @Value("${wms.log.sqlite.error-retention-days:90}")
    private int errorRetentionDays = 90; // 错误日志保留天数

    @Value("${wms.log.sqlite.normal-retention-days:7}")
    private int normalRetentionDays = 7; // 普通日志保留天数

    @Value("${wms.log.sqlite.enable-response-data:false}")
    private boolean enableResponseData = false; // 是否记录响应数据

    @Value("${wms.log.sqlite.enable-request-data:true}")
    private boolean enableRequestData = true; // 是否记录请求数据

    @Value("${wms.log.sqlite.important-modules:}")
    private String importantModules = ""; // 重要模块列表，用逗号分隔

    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
    
    @PostConstruct
    public void init() {
        // 显式加载SQLite JDBC驱动
        try {
            Class.forName("org.sqlite.JDBC");
            log.info("SQLite JDBC驱动加载成功");
        } catch (ClassNotFoundException e) {
            log.error("SQLite JDBC驱动加载失败", e);
            return;
        }



        createTableIfNotExists();

        // 启动定期清理任务
        startCleanupTask();

        // 启动监控任务
        startMonitorTask();
    }
    
    /**
     * 创建表（如果不存在）
     */
    private void createTableIfNotExists() {
        // 确保日志目录存在
        java.io.File dbFile = new java.io.File(DB_PATH);
        java.io.File parentDir = dbFile.getParentFile();
        if (!parentDir.exists()) {
            boolean created = parentDir.mkdirs();
            if (created) {
                log.info("创建日志目录: {}", parentDir.getAbsolutePath());
            } else {
                log.warn("创建日志目录失败: {}", parentDir.getAbsolutePath());
            }
        }
        
        String sql = "CREATE TABLE IF NOT EXISTS api_log (" +
                "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                "request_id TEXT," +
                "module TEXT," +
                "class_name TEXT," +
                "method_name TEXT," +
                "request_url TEXT," +
                "request_method TEXT," +
                "client_ip TEXT," +
                "user_agent TEXT," +
                "request_params TEXT," +
                "response_data TEXT," +
                "execution_time INTEGER," +
                "status TEXT," +
                "error_message TEXT," +
                "create_time TEXT" +
                ")";

        try (Connection conn = DriverManager.getConnection(DB_URL);
             Statement stmt = conn.createStatement()) {
            stmt.execute(sql);

            // 创建索引以提高查询性能
            stmt.execute("CREATE INDEX IF NOT EXISTS idx_create_time ON api_log(create_time)");
            stmt.execute("CREATE INDEX IF NOT EXISTS idx_module ON api_log(module)");
            stmt.execute("CREATE INDEX IF NOT EXISTS idx_status ON api_log(status)");

            log.info("SQLite日志表初始化成功: {}", DB_PATH);
        } catch (SQLException e) {
            log.error("创建SQLite日志表失败", e);
        }
    }
    
    /**
     * 异步保存日志到SQLite（增强版）
     */
    public void saveLogAsync(String requestId, String module, String className, String methodName,
                           String requestUrl, String requestMethod, String clientIp, String userAgent,
                           String requestParams, String responseData, Long executionTime,
                           String status, String errorMessage) {

        // 使用线程池异步执行，避免影响接口性能
        new Thread(() -> {
            try {
                // 确保驱动已加载
                Class.forName("org.sqlite.JDBC");
            } catch (ClassNotFoundException e) {
                log.error("SQLite JDBC驱动未找到", e);
                return;
            }

            // 智能数据处理
            String finalRequestParams = processRequestData(requestParams, module);
            String finalResponseData = processResponseData(responseData, module, status);

            String sql = "INSERT INTO api_log (request_id, module, class_name, method_name, request_url, " +
                    "request_method, client_ip, user_agent, request_params, response_data, " +
                    "execution_time, status, error_message, create_time) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

            try (Connection conn = DriverManager.getConnection(DB_URL);
                 PreparedStatement pstmt = conn.prepareStatement(sql)) {

                pstmt.setString(1, requestId);
                pstmt.setString(2, module);
                pstmt.setString(3, className);
                pstmt.setString(4, methodName);
                pstmt.setString(5, requestUrl);
                pstmt.setString(6, requestMethod);
                pstmt.setString(7, clientIp);
                pstmt.setString(8, userAgent);
                pstmt.setString(9, finalRequestParams);
                pstmt.setString(10, finalResponseData);
                pstmt.setLong(11, executionTime != null ? executionTime : 0);
                pstmt.setString(12, status);
                pstmt.setString(13, errorMessage);
                pstmt.setString(14, LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

                pstmt.executeUpdate();
                log.debug("接口日志保存到SQLite成功: {}", requestId);

            } catch (SQLException e) {
                log.error("保存接口日志到SQLite失败: {}", e.getMessage(), e);
            }
        }).start();
    }

    /**
     * 智能处理请求数据
     */
    private String processRequestData(String requestParams, String module) {
        if (!enableRequestData || requestParams == null) {
            return null;
        }
        return requestParams;
    }

    /**
     * 智能处理响应数据
     */
    private String processResponseData(String responseData, String module, String status) {
        if (responseData == null) {
            return null;
        }

        // 如果配置关闭了响应数据记录，但这是失败的请求，仍然记录以便调试
        if (!enableResponseData) {
            // 对于失败状态的请求，强制记录响应数据以便调试
            if ("FAILED".equals(status)) {
                return responseData; // 失败请求强制记录
            }
            return null; // 成功请求不记录
        }
        return responseData;
    }

    /**
     * 判断是否为重要模块
     */
    private boolean isImportantModule(String module) {
        if (module == null || importantModules.isEmpty()) {
            return false;
        }

        String[] modules = importantModules.split(",");
        for (String importantModule : modules) {
            if (module.contains(importantModule.trim())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 启动定期清理任务
     */
    private void startCleanupTask() {
        scheduler.scheduleAtFixedRate(() -> {
            try {
                cleanupOldLogs();
            } catch (Exception e) {
                log.error("清理历史日志失败", e);
            }
        }, 1, 24, java.util.concurrent.TimeUnit.HOURS); // 每24小时执行一次
    }

    /**
     * 启动监控任务
     */
    private void startMonitorTask() {
        scheduler.scheduleAtFixedRate(() -> {
            try {
                monitorDatabaseSize();
            } catch (Exception e) {
                log.error("监控数据库大小失败", e);
            }
        }, 1, 1, java.util.concurrent.TimeUnit.HOURS); // 每小时检查一次
    }

    /**
     * 智能清理历史日志
     */
    private void cleanupOldLogs() {
        try (Connection conn = DriverManager.getConnection(DB_URL);
             Statement stmt = conn.createStatement()) {

            // 分别清理不同类型的日志
            // 1. 清理普通日志（保留较短时间）
            String normalSql = "DELETE FROM api_log WHERE status = 'SUCCESS' AND create_time < datetime('now', '-" + normalRetentionDays + " days')";
            int normalDeleted = stmt.executeUpdate(normalSql);

            // 2. 清理错误日志（保留较长时间）
            String errorSql = "DELETE FROM api_log WHERE status = 'FAILED' AND create_time < datetime('now', '-" + errorRetentionDays + " days')";
            int errorDeleted = stmt.executeUpdate(errorSql);

            if (normalDeleted > 0 || errorDeleted > 0) {
                log.info("智能清理完成，删除普通日志 {} 条，错误日志 {} 条", normalDeleted, errorDeleted);

                // 执行VACUUM以回收空间
                stmt.execute("VACUUM");
                log.info("数据库空间回收完成");
            }

        } catch (SQLException e) {
            log.error("清理历史日志失败", e);
        }
    }

    /**
     * 智能监控数据库大小
     */
    private void monitorDatabaseSize() {
        java.io.File dbFile = new java.io.File(DB_PATH);
        if (!dbFile.exists()) {
            return;
        }

        long fileSizeMB = dbFile.length() / (1024 * 1024);
        log.debug("SQLite数据库当前大小: {}MB", fileSizeMB);

        // 获取磁盘可用空间
        long totalSpace = dbFile.getParentFile().getTotalSpace() / (1024 * 1024);
        long freeSpace = dbFile.getParentFile().getFreeSpace() / (1024 * 1024);
        long usedPercent = ((totalSpace - freeSpace) * 100) / totalSpace;

        // 基于磁盘使用率进行智能清理
        if (usedPercent > maxDiskUsagePercent) {
            log.warn("⚠️ 磁盘使用率过高！当前: {}%, 限制: {}%", usedPercent, maxDiskUsagePercent);
            emergencyCleanup();
        } else if (fileSizeMB > 100) { // 数据库超过100MB时开始关注
            log.info("数据库大小: {}MB, 磁盘使用率: {}%", fileSizeMB, usedPercent);
        }
    }

    /**
     * 紧急清理
     */
    private void emergencyCleanup() {
        // 删除7天前的数据
        String sql = "DELETE FROM api_log WHERE create_time < datetime('now', '-7 days')";

        try (Connection conn = DriverManager.getConnection(DB_URL);
             Statement stmt = conn.createStatement()) {

            int deletedRows = stmt.executeUpdate(sql);
            log.warn("紧急清理完成，删除 {} 条记录", deletedRows);

            // 执行VACUUM以回收空间
            stmt.execute("VACUUM");

        } catch (SQLException e) {
            log.error("紧急清理失败", e);
        }
    }

    /**
     * 获取数据库统计信息
     */
    public DatabaseStats getDatabaseStats() {
        DatabaseStats stats = new DatabaseStats();

        try (Connection conn = DriverManager.getConnection(DB_URL);
             Statement stmt = conn.createStatement()) {

            // 获取总记录数
            ResultSet rs = stmt.executeQuery("SELECT COUNT(*) FROM api_log");
            if (rs.next()) {
                stats.totalRecords = rs.getLong(1);
            }

            // 获取今日记录数
            rs = stmt.executeQuery("SELECT COUNT(*) FROM api_log WHERE date(create_time) = date('now')");
            if (rs.next()) {
                stats.todayRecords = rs.getLong(1);
            }

            // 获取数据库文件大小
            java.io.File dbFile = new java.io.File(DB_PATH);
            if (dbFile.exists()) {
                stats.fileSizeMB = dbFile.length() / (1024 * 1024);
            }

        } catch (SQLException e) {
            log.error("获取数据库统计信息失败", e);
        }

        return stats;
    }

    /**
     * 数据库统计信息
     */
    public static class DatabaseStats {
        public long totalRecords;
        public long todayRecords;
        public long fileSizeMB;

        @Override
        public String toString() {
            return String.format("总记录数: %d, 今日记录数: %d, 文件大小: %dMB",
                    totalRecords, todayRecords, fileSizeMB);
        }
    }
}
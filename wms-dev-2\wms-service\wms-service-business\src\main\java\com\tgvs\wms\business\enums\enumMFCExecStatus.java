package com.tgvs.wms.business.enums;

import com.tgvs.wms.common.util.oConvertUtils;

public enum enumMFCExecStatus {
    create(Integer.valueOf(0), "create", "创建"),
    allot(Integer.valueOf(1), "allot", "分配"),
    execute(Integer.valueOf(2), "execute", "执行中"),
    complete(Integer.valueOf(3), "complete", "完成"),
    cancel(Integer.valueOf(4), "cancel", "立库系统取消"),
    inboundcancel(Integer.valueOf(9), "inboundcancel", "入库取消"),
    empty(Integer.valueOf(12), "empty", "空出");

    private Integer value;

    private String code;

    private String text;

    enumMFCExecStatus(Integer value, String code, String text) {
        this.value = value;
        this.code = code;
        this.text = text;
    }

    public static enumMFCExecStatus getByValue(int value) {
        if (oConvertUtils.isEmpty(Integer.valueOf(value)))
            return null;
        for (enumMFCExecStatus val : values()) {
            if (val.getValue().equals(Integer.valueOf(value)))
                return val;
        }
        return null;
    }

    public Integer getValue() {
        return this.value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getText() {
        return this.text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public static enumMFCExecStatus toEnum(Integer Value) {
        for (enumMFCExecStatus e : values()) {
            if (e.getValue().equals(Value))
                return e;
        }
        return null;
    }
}

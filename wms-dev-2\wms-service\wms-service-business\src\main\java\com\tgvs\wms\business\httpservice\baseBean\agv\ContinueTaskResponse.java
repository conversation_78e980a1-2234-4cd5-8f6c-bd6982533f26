package com.tgvs.wms.business.httpservice.baseBean.agv;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

/**
 * 海康AGV任务继续接口响应实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ContinueTaskResponse extends AgvBaseResponse {

    /**
     * 业务数据
     */
    private ContinueTaskResponseData data;
    
    /**
     * 任务继续接口响应数据
     */
    @Data
    public static class ContinueTaskResponseData {
        
        /**
         * 任务号，全局唯一
         */
        private String robotTaskCode;
        
        /**
         * 自定义扩展字段
         */
        private Map<String, Object> extra;
    }
}

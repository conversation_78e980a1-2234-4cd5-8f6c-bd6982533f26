package com.tgvs.wms.business.httpservice.baseBean.warehouse;


import java.util.List;

public class MFCstationStatus {
    private Integer id;

    private String messageName;

    private List<MFCstationPD> pd;

    public void setId(Integer id) {
        this.id = id;
    }

    public void setMessageName(String messageName) {
        this.messageName = messageName;
    }

    public void setPd(List<MFCstationPD> pd) {
        this.pd = pd;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof MFCstationStatus))
            return false;
        MFCstationStatus other = (MFCstationStatus)o;
        if (!other.canEqual(this))
            return false;
        Object this$id = getId(), other$id = other.getId();
        if ((this$id == null) ? (other$id != null) : !this$id.equals(other$id))
            return false;
        Object this$messageName = getMessageName(), other$messageName = other.getMessageName();
        if ((this$messageName == null) ? (other$messageName != null) : !this$messageName.equals(other$messageName))
            return false;
        List<MFCstationPD> this$pd = (List<MFCstationPD>)getPd(), other$pd = (List<MFCstationPD>)other.getPd();
        return !((this$pd == null) ? (other$pd != null) : !this$pd.equals(other$pd));
    }

    protected boolean canEqual(Object other) {
        return other instanceof MFCstationStatus;
    }

    public int hashCode() {
        int PRIME = 59,
        result = 1;
        Object $id = getId();
        result = result * 59 + (($id == null) ? 43 : $id.hashCode());
        Object $messageName = getMessageName();
        result = result * 59 + (($messageName == null) ? 43 : $messageName.hashCode());
        List<MFCstationPD> $pd = (List<MFCstationPD>)getPd();
        return result * 59 + (($pd == null) ? 43 : $pd.hashCode());
    }

    public String toString() {
        return "MFCstationStatus(id=" + getId() + ", messageName=" + getMessageName() + ", pd=" + getPd() + ")";
    }

    public Integer getId() {
        return this.id;
    }

    public String getMessageName() {
        return this.messageName;
    }

    public List<MFCstationPD> getPd() {
        return this.pd;
    }
}

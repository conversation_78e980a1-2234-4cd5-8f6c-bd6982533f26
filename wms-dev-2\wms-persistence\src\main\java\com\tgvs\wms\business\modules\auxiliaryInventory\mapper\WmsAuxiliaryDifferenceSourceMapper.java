package com.tgvs.wms.business.modules.auxiliaryInventory.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tgvs.wms.business.modules.auxiliaryInventory.entity.WmsAuxiliaryDifferenceSource;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 辅料差异数据源 Mapper接口
 * 
 * <AUTHOR> Generated
 * @date 2025-01-24
 */
@Mapper
public interface WmsAuxiliaryDifferenceSourceMapper extends BaseMapper<WmsAuxiliaryDifferenceSource> {

    /**
     * 分页查询差异数据源列表
     * 
     * @param page 分页参数
     * @param materialCode 物料编码
     * @param materialName 物料名称
     * @param contractNo 合约号
     * @param itemNo 款号
     * @param boxNo 容器号
     * @param diffType 差异类型
     * @param status 状态
     * @param sourceBatch 导入批次号
     * @return 分页结果
     */
    IPage<WmsAuxiliaryDifferenceSource> selectDifferenceSourcePage(
            Page<WmsAuxiliaryDifferenceSource> page,
            @Param("materialCode") String materialCode,
            @Param("materialName") String materialName,
            @Param("contractNo") String contractNo,
            @Param("itemNo") String itemNo,
            @Param("boxNo") String boxNo,
            @Param("diffType") String diffType,
            @Param("status") String status,
            @Param("sourceBatch") String sourceBatch
    );

    /**
     * 查询待处理的差异数据列表
     * 
     * @return 待处理的差异数据列表
     */
    List<WmsAuxiliaryDifferenceSource> selectPendingDifferenceList();

    /**
     * 根据差异类型统计数量
     * 
     * @param diffType 差异类型
     * @param status 状态
     * @return 统计数量
     */
    int countByDiffTypeAndStatus(@Param("diffType") String diffType, @Param("status") String status);

    /**
     * 批量更新状态
     * 
     * @param ids ID列表
     * @param status 新状态
     * @param updateBy 更新人
     * @return 更新数量
     */
    int batchUpdateStatus(@Param("ids") List<String> ids, @Param("status") String status, @Param("updateBy") String updateBy);
}
package com.tgvs.wms.business.modules.bd.service.impl;

import com.tgvs.wms.base.service.impl.BaseServiceImpl;
import com.tgvs.wms.business.modules.bd.entity.OutboundConfig;
import com.tgvs.wms.business.modules.bd.mapper.OutboundConfigMapper;
import com.tgvs.wms.business.modules.bd.service.IOutboundConfigService;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class OutboundConfigServiceImpl extends BaseServiceImpl<OutboundConfigMapper, OutboundConfig> implements IOutboundConfigService {
    @Override
    public void updateChecked(String id) {
        lambdaUpdate().eq(OutboundConfig::getChecked,1)
                .set(OutboundConfig::getChecked,0)
                .update();
        lambdaUpdate().eq(OutboundConfig::getId,id)
                .set(OutboundConfig::getChecked,1)
                .set(OutboundConfig::getUpdateTime,new Date())
                .update();
    }
}

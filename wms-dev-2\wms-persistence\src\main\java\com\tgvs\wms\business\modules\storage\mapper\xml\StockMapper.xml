<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tgvs.wms.business.modules.storage.mapper.StockMapper">
    <select id="selectBycode" parameterType="java.lang.String" resultType="com.tgvs.wms.business.modules.storage.entity.Stock">
		SELECT *
		FROM  wms_stock
		WHERE
			 code = #{code} 	</select>


	<select id="getLevelCount"  resultType="com.tgvs.wms.business.modules.storage.entity.StockLevel">
		select level,batch,count(level) as count from wms_stock
		WHERE  state = #{state} and locked = #{locked}  and box_empty = #{box_empty}
		GROUP BY level,batch
		HAVING count(level) > 0
		ORDER BY count #{orderBy}
	</select>

	<!-- 辅料库存分页查询 - 联表查询，使用MyBatis Plus自动映射 -->
	<select id="pageAuxiliaryStockList" resultType="com.tgvs.wms.common.core.domain.AuxiliaryStockDto">
		SELECT 
			bi.id,
			bi.box_type,
			bi.batch_no,
			bi.box_no,
			bi.grid_id,
			bi.grid_status,
			bi.grid_volume,
			bi.contract_no,
			bi.contract_no_po,
			bi.item_no,
			bi.material_code,
			ai.material_name,
			bi.material_color,
			bi.material_model,
			bi.material_size,
			bi.material_quantity,
			ai.material_type,
			bi.status,
			bi.create_time,
			bi.update_time,
			s.`column` as `column`,
			s.`row` as `row`,
			s.level as level,
			s.code as code,
			ai.material_type
		FROM wms_box_item bi
		INNER JOIN wms_auxiliary_info ai ON bi.material_code = ai.material_code
		LEFT JOIN wms_box_shelf s ON bi.box_no = s.box_no AND s.delete_flag = 0
		<where>
			bi.delete_flag = 0 
			AND bi.status = 2
			<if test="searchParams != null">
				<if test="searchParams.materialCode != null and searchParams.materialCode != ''">
					AND bi.material_code LIKE CONCAT('%', #{searchParams.materialCode}, '%')
				</if>
				<if test="searchParams.materialName != null and searchParams.materialName != ''">
					AND (ai.material_name LIKE CONCAT('%', #{searchParams.materialName}, '%') 
					     OR bi.material_name LIKE CONCAT('%', #{searchParams.materialName}, '%'))
				</if>
				<if test="searchParams.boxNo != null and searchParams.boxNo != ''">
					AND bi.box_no LIKE CONCAT('%', #{searchParams.boxNo}, '%')
				</if>
				<if test="searchParams.contractNo != null and searchParams.contractNo != ''">
					AND bi.contract_no LIKE CONCAT('%', #{searchParams.contractNo}, '%')
				</if>
				<if test="searchParams.itemNo != null and searchParams.itemNo != ''">
					AND bi.item_no LIKE CONCAT('%', #{searchParams.itemNo}, '%')
				</if>
				<if test="searchParams.materialType != null and searchParams.materialType != ''">
					AND ai.material_type = #{searchParams.materialType}
				</if>
				<if test="searchParams.boxType != null and searchParams.boxType != ''">
					AND bi.box_type = #{searchParams.boxType}
				</if>
				<if test="searchParams.code != null and searchParams.code != ''">
					AND s.code LIKE CONCAT('%', #{searchParams.code}, '%')
				</if>
			</if>
		</where>
		ORDER BY bi.update_time DESC
	</select>


</mapper>
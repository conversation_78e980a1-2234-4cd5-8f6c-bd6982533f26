<?xml version="1.0" encoding="UTF-8" ?>
<included>
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="com.tgvs.wms.common.trace.logging.layout.LogStandardLayout">
                <format>${LOG_FORMAT}</format>
            </layout>
            <charset>utf8</charset>
        </encoder>
        <file>${LOG_PATH}/${LOG_FILE_PREFIX_NAME}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>
                ${LOG_PATH}/${LOG_FILE_PREFIX_NAME}.%d{yyyyMMdd}.%i.log
            </fileNamePattern>
            <maxFileSize>200mb</maxFileSize>
        </rollingPolicy>
    </appender>

    <appender name="FORMAT-CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="com.tgvs.wms.common.trace.logging.layout.LogStandardLayout">
                <format>${LOG_FORMAT}</format>
            </layout>
            <charset>utf8</charset>
        </encoder>
    </appender>

</included>
package com.tgvs.wms.business.modules.bd.entity;


import java.io.Serializable;
import java.util.Date;

import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.tgvs.wms.common.annotation.Dict;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TableName("wms_ptl_config")
@ApiModel(value = "wms_ptl_config对象", description = "电子标签显示配置")
@Data
public class PtlConfig  implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("id")
    private String id;

    @Excel(name = "播种颜色", width = 15.0D, dictTable = "wms_ptl_show_color", dicText = "text", dicCode = "value")
    @Dict(dictTable = "wms_ptl_show_color", dicText = "text", dicCode = "value")
    @ApiModelProperty("播种颜色")
    private Integer colorValue;

    @Excel(name = "拣选颜色", width = 15.0D, dictTable = "wms_ptl_show_color", dicText = "text", dicCode = "value")
    @Dict(dictTable = "wms_ptl_show_color", dicText = "text", dicCode = "value")
    @ApiModelProperty("拣选颜色")
    private Integer colorValue2;

    @Excel(name = "显示模式", width = 15.0D, dictTable = "wms_ptl_show_type", dicText = "text", dicCode = "value")
    @Dict(dictTable = "wms_ptl_show_type", dicText = "text", dicCode = "value")
    @ApiModelProperty("显示模式")
    private Integer showType;

    @Excel(name = "描述", width = 15.0D)
    @ApiModelProperty("描述")
    private String text;

    @Excel(name = "关联设备", width = 15.0D)
    @ApiModelProperty("关联设备")
    private String code;

    @Excel(name = "所属系统", width = 15.0D)
    @ApiModelProperty("所属系统")
    private String sysid;

    @ApiModelProperty("创建人")
    private String createBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("更新时间")
    private Date updateTime;

    @Excel(name = "删除状态", width = 15.0D)
    @ApiModelProperty("删除状态")
    private Integer delFlag;

}

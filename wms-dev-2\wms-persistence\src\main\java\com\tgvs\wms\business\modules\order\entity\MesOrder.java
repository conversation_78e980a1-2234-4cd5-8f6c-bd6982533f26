package com.tgvs.wms.business.modules.order.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.tgvs.wms.base.entity.BaseEntity;
import com.tgvs.wms.common.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@TableName("mes_order")
@ApiModel(value = "mes_order对象", description = "工单表")
public class MesOrder extends BaseEntity {

    @ApiModelProperty("单编号")
    private String bookNo;

    @ApiModelProperty("描述")
    private String description;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("计划日期")
    private Date planTime;

    @Dict(dicCode = "mes_order_status")
    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("完成时间")
    private Date endTime;

    @ApiModelProperty("删除状态")
    private Integer delFlag;

}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tgvs.wms.business.modules.container.mapper.AuxiliaryPreboxMapper">

    <!-- 根据容器号分组查询预装箱信息 -->
    <select id="queryPreboxGroupByContainer" resultType="java.util.Map">
        SELECT
            a.container_no, a.contract_no, a.item_no, a.material_code, a.material_name,
            a.material_model, a.material_size, a.material_color, a.material_property, a.po_no,
            c.box_type, c.box_container_type,
            SUM(a.actual_inbound_qty) as total_qty
        FROM wms_auxiliary_prebox a
        LEFT JOIN wms_box c ON a.container_no = c.box_no
        WHERE c.delete_flag = 0
        GROUP BY a.container_no, a.contract_no, a.item_no, a.material_code
    </select>
    
    <!-- 根据条件按容器号分组查询预装箱信息 -->
    <select id="queryPreboxGroupByCondition" resultType="java.util.Map">
        SELECT
            a.container_no, a.contract_no, a.item_no, a.material_code, a.material_name,
            a.material_model, a.material_size, a.material_color, a.material_property, a.po_no,
            c.box_type, c.box_container_type, a.container_type,
            SUM(a.actual_inbound_qty) as total_qty
        FROM wms_auxiliary_prebox a
        LEFT JOIN wms_box c ON a.container_no = c.box_no
        WHERE c.delete_flag = 0
        <if test="contractNo != null and contractNo != ''">
            AND a.contract_no = #{contractNo}
        </if>
        <if test="itemNo != null and itemNo != ''">
            AND a.item_no = #{itemNo}
        </if>
        <if test="materialCode != null and materialCode != ''">
            AND a.material_code = #{materialCode}
        </if>
        <if test="containerNo != null and containerNo != ''">
            AND a.container_no = #{containerNo}
        </if>
        <if test="containerType != null">
            AND a.container_type = #{containerType}
        </if>
        <if test="materialProperty != null and materialProperty != ''">
            AND a.material_property = #{materialProperty}
        </if>
        <if test="status != null">
            AND a.status = #{status}
        </if>
        <if test="groupByFields != null and groupByFields != ''">
            GROUP BY ${groupByFields}
        </if>
        <if test="groupByFields == null or groupByFields == ''">
            GROUP BY a.container_no, a.contract_no, a.item_no, a.material_code
        </if>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
        <if test="orderBy == null or orderBy == ''">
            ORDER BY a.create_time DESC
        </if>
    </select>
    
    <!-- 根据容器号查询预装箱明细信息 -->
    <select id="queryPreboxDetailByContainer" resultType="com.tgvs.wms.business.modules.container.entity.AuxiliaryPrebox">
        SELECT * FROM wms_auxiliary_prebox 
        WHERE container_no = #{containerNo} 
        ORDER BY grid_id
    </select>
    
    <!-- 使用QueryModel直接查询预装箱信息，增加任务状态过滤 -->
   <select id="queryPreboxByQueryModel" resultType="com.tgvs.wms.business.modules.container.entity.AuxiliaryPrebox">
    SELECT
        a.id, a.stock_batch_id, a.stock_in_id, a.stock_in_no,
        a.container_no, a.contract_no, a.item_no, a.material_code, a.material_name,
        a.material_quantity, a.actual_inbound_qty, a.material_model, a.material_size, a.material_color,
        a.grid_id, a.box_volume, a.container_type, a.material_property,
        a.prebox_order, a.po_no, a.status, a.create_time, a.update_time,
        c.box_type, c.box_container_type,
        t.task_status, t.task_order
    FROM wms_auxiliary_prebox a
    LEFT JOIN wms_box c ON a.container_no = c.box_no
    LEFT JOIN (
        SELECT box_no, task_status, task_order,
               ROW_NUMBER() OVER (PARTITION BY box_no ORDER BY create_time DESC) as rn
        FROM wms_box_task_list
        WHERE delete_flag = 0
    ) t ON a.container_no = t.box_no AND t.rn = 1
    WHERE c.delete_flag = 0 and a.status=0
    <!-- 根据查询参数决定是否过滤任务状态 -->
    <if test="queryModel != null and queryModel.searchParams != null and queryModel.searchParams.filterByTaskStatus != null and queryModel.searchParams.filterByTaskStatus == 'true'">
        <!-- 只返回任务状态为3（输送线运输）的料箱 -->
        AND t.task_status in(4,5)
    </if>
    <if test="queryModel != null and queryModel.searchParams != null and queryModel.searchParams.includeTaskStatus != null and queryModel.searchParams.includeTaskStatus != ''">
        <!-- 根据指定的任务状态进行过滤 -->
        AND t.task_status IN (${queryModel.searchParams.includeTaskStatus})
    </if>
    <if test="queryModel != null and queryModel.searchParams != null">
        <if test="queryModel.searchParams.contract_no != null and queryModel.searchParams.contract_no != ''">
            AND a.contract_no = #{queryModel.searchParams.contract_no}
        </if>
        <if test="queryModel.searchParams.item_no != null and queryModel.searchParams.item_no != ''">
            AND a.item_no = #{queryModel.searchParams.item_no}
        </if>
        <if test="queryModel.searchParams.material_code != null and queryModel.searchParams.material_code != ''">
            AND a.material_code = #{queryModel.searchParams.material_code}
        </if>
        <if test="queryModel.searchParams.containerNo != null and queryModel.searchParams.containerNo != ''">
            AND a.container_no = #{queryModel.searchParams.containerNo}
        </if>
        <if test="queryModel.searchParams.containerType != null and queryModel.searchParams.containerType != ''">
            AND a.container_type = #{queryModel.searchParams.containerType}
        </if>
        <if test="queryModel.searchParams.materialProperty != null and queryModel.searchParams.materialProperty != ''">
            AND a.material_property = #{queryModel.searchParams.materialProperty}
        </if>
        <if test="queryModel.searchParams.stockBatchId != null and queryModel.searchParams.stockBatchId != ''">
            AND a.stock_batch_id = #{queryModel.searchParams.stockBatchId}
        </if>
    </if>
    <if test="queryModel != null and queryModel.sortList != null and queryModel.sortList.size() > 0">
        ORDER BY
        <foreach collection="queryModel.sortList" item="sort" separator=",">
            <if test="sort.sortType == 1">
                ${sort.column} ASC
            </if>
            <if test="sort.sortType == 2">
                ${sort.column} DESC
            </if>
        </foreach>
    </if>
    <if test="queryModel == null or queryModel.sortList == null or queryModel.sortList.isEmpty()">
        ORDER BY a.create_time DESC, a.prebox_order DESC
    </if>
</select>
 
    <!-- 根据前缀查询最大批次号 -->
    <select id="selectMaxBatchNoByPrefix" resultType="java.lang.String">
        SELECT MAX(stock_batch_id)
        FROM wms_auxiliary_prebox
        WHERE stock_batch_id LIKE CONCAT(#{prefix}, '%')
    </select>
</mapper> 
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tgvs.wms.business.modules.machineMaterials.mapper.WmsMachineOutboundMapper">
    
    <!-- 自定义条件查询机物料出库列表 -->
    <select id="selectWmsMachineOutboundCustom" resultType="com.tgvs.wms.business.modules.machineMaterials.entity.WmsMachineOutbound">
        select id, out_store_number, material_code, out_quantity, priority,
               create_by, create_time, update_by, update_time, delete_flag, remark
        from wms_machine_outbound
        <where>
            <if test="outStoreNumber != null and outStoreNumber != ''">
                AND out_store_number = #{outStoreNumber}
            </if>
            <if test="materialCode != null and materialCode != ''">
                AND material_code = #{materialCode}
            </if>
            <if test="outQuantity != null">
                AND out_quantity = #{outQuantity}
            </if>
            <if test="priority != null">
                AND priority = #{priority}
            </if>
            AND delete_flag = 0
        </where>
        order by create_time desc
    </select>
    
    <!-- 根据出库单号查询机物料出库信息 -->
    <select id="selectWmsMachineOutboundByNumber" resultType="com.tgvs.wms.business.modules.machineMaterials.entity.WmsMachineOutbound">
        select id, out_store_number, material_code, out_quantity, priority,
               create_by, create_time, update_by, update_time, delete_flag, remark
        from wms_machine_outbound
        where out_store_number = #{outStoreNumber}
        and delete_flag = 0
        limit 1
    </select>
    
    <!-- 批量逻辑删除机物料出库信息 -->
    <update id="deleteWmsMachineOutboundByIds">
        update wms_machine_outbound set delete_flag = 1 
        where id in
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    
</mapper> 
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tgvs.wms.business.modules.storage.mapper.ShelfMapper">
    <select id="selectListCount" parameterType="java.lang.String" resultType="com.tgvs.wms.business.modules.storage.entity.ShelfPie">
-- 		select box_type as item, count(id) as count from wms_box_shelf  GROUP BY box_type ORDER BY box_type DESC

		select material_type as item, count(id) as count
		from wms_box
		GROUP BY material_type
		ORDER BY material_type DESC
	</select>

	<select id="selectBycode" parameterType="java.lang.String" resultType="com.tgvs.wms.business.modules.storage.entity.Shelf">
		SELECT *
		FROM wms_box_shelf
		WHERE code = #{code}
	</select>

	<select id="selectInBound" parameterType="java.lang.Integer" resultType="com.tgvs.wms.business.modules.storage.entity.ShelfPie">
		select t.area as item, SUM(t.count) as count from
		(
		select area, COUNT(area) as count from wms_box_shelf 
		WHERE box_type = '1' and state = 0 and locked = 0 and box_no = '' 
		GROUP BY area HAVING count(area) > 0
		UNION
		SELECT to_site as area, 0 - COUNT(to_site) as count 
		from wms_task_box WHERE box_type=1 and state &lt; 15 
		GROUP BY to_site) as t 
		GROUP BY t.area ORDER BY SUM(t.count) desc
	</select>

	<select id="selectInBoundByBookNo" resultType="com.tgvs.wms.business.modules.storage.entity.ShelfPie">
		select t.area as item, SUM(t.count) as count from
		(
		select area, COUNT(area) as count from wms_box_shelf 
		WHERE box_type = '1' and state = 0 and locked = 0 and box_no = '' 
		GROUP BY area HAVING count(area) > 0
		UNION
		SELECT to_site as area, 0 - COUNT(to_site) as count 
		from wms_task_box WHERE box_type=1 and state &lt;15
		GROUP BY to_site) as t 
		GROUP BY t.area ORDER BY SUM(t.count) desc
	</select>

	<select id="selectInBoundEmpty" resultType="com.tgvs.wms.business.modules.storage.entity.ShelfPie">
		select t.area as item, SUM(t.count) as count from
		(
		select area, COUNT(area) as count from wms_box_shelf 
		WHERE box_type = '1' and state = 1 and locked = 0 and box_no&lt;&gt;'' and box_type = #{boxType}
		GROUP BY area HAVING count(area) > 0
		UNION
		SELECT to_site as area, COUNT(to_site) as count 
		from wms_task_box WHERE box_type=1 and box_type = #{boxType} 
		GROUP BY to_site) as t 
		GROUP BY t.area ORDER BY SUM(t.count)
	</select>

	<select id="selectAllotByBookNoTwo" parameterType="java.lang.String" resultType="com.tgvs.wms.business.modules.storage.entity.ShelfBookLevel">
		select b.level, a.count from
		(select t.level, SUM(t.count) as count from
		(select level, count(level) as count from wms_box_shelf
		GROUP BY level) as t GROUP BY t.level ORDER BY SUM(t.count)) as a
		RIGHT JOIN
		(select level, COUNT(level) as havenull from wms_box_shelf
		WHERE box_type = '1' and state = 0 and locked = 0 and box_no = '' and box_type is null
		GROUP BY level HAVING count(level) > 0) b
		on a.`level`=b.`level` and b.havenull > 1 ORDER BY a.count, b.havenull DESC
	</select>

	<select id="selectAllotByBoxEmptyTwo" parameterType="java.lang.Integer" resultType="com.tgvs.wms.business.modules.storage.entity.ShelfBookLevel">
		select b.level, a.count from
		(select t.level, SUM(t.count) as count from
		(select level, count(level) as count from wms_box_shelf
		WHERE box_type = #{boxType}
		GROUP BY level) as t GROUP BY t.level ORDER BY SUM(t.count)) as a
		RIGHT JOIN
		(select level, COUNT(level) as havenull from wms_box_shelf
		WHERE box_type = '1' and state = 0 and locked = 0 and box_no = '' and box_type is null and level&lt;&gt;8
		GROUP BY level HAVING count(level) &gt; 0) b
		on a.`level`=b.`level` and b.havenull &gt; 1 ORDER BY a.count, b.havenull DESC
	</select>

	<select id="selectAllotByBookNoFour" resultType="com.tgvs.wms.business.modules.storage.entity.ShelfBookLevel">
		select b.area, b.level, a.count from
		(select t.area, t.level, SUM(t.count) as count from
		(select area, level, count(level) as count from wms_box_shelf
		WHERE area = #{area}
		GROUP BY level) as t GROUP BY t.level ORDER BY SUM(t.count)) as a
		RIGHT JOIN
		(select area, level, COUNT(level) as havenull from wms_box_shelf
		WHERE box_type = '1' and state = 0 and locked = 0 and box_no = '' and box_type is null and area = #{area}
		GROUP BY level HAVING count(level) &gt; 0) b
		on a.`level`=b.`level` and b.havenull &gt; 1 ORDER BY a.count, b.havenull DESC
	</select>

	<select id="selectAllotByBoxEmptyFour" resultType="com.tgvs.wms.business.modules.storage.entity.ShelfBookLevel">
		select b.area, b.level, a.count from
		(select t.area, t.level, SUM(t.count) as count from
		(select area, level, count(level) as count from wms_box_shelf
		WHERE box_type = #{boxType} and area = #{area}
		GROUP BY level) as t GROUP BY t.level ORDER BY SUM(t.count)) as a
		RIGHT JOIN
		(select area, level, COUNT(level) as havenull from wms_box_shelf
		WHERE box_type = '1' and state = 0 and locked = 0 and box_no = '' and box_type is null and area = #{area}
		GROUP BY level HAVING count(level) &gt; 0) b
		on a.`level`=b.`level` and b.havenull &gt; 1 ORDER BY a.count, b.havenull DESC
	</select>
	
	<select id="selectEmtpyBox" resultType="com.tgvs.wms.business.modules.storage.entity.Shelf">
		SELECT f.* FROM wms_box_shelf f
		inner join wms_box x on f.box_no = x.box_no
		WHERE x.box_type = #{boxType} AND f.state =1 AND f.locked = 0  AND x.box_empty = 0
		ORDER BY RAND()
	</select>

	<select id="selectEmtpyBoxType" resultType="com.tgvs.wms.business.modules.storage.entity.Shelf">
		SELECT f.* FROM wms_box_shelf f
							inner join wms_box x on f.box_no = x.box_no
		WHERE x.box_type = #{boxType} AND f.state =1 AND f.locked = 0  AND x.box_empty = 0 AND x.box_empty_status = 0 AND x.box_container_type = #{boxContainerType}
		ORDER BY RAND()
	</select>

	<select id="selectByBoxNo" resultType="com.tgvs.wms.business.modules.storage.entity.Shelf">
		SELECT f.* FROM wms_box_shelf f
							inner join wms_box x on f.box_no = x.box_no
		WHERE x.box_no = #{boxNo} AND f.state =1 AND f.locked = 0 AND f.delete_flag = 0
		ORDER BY RAND()
	</select>
</mapper>
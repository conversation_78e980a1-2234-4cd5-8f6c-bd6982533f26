<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tgvs.wms.business.modules.task.mapper.TaskReportQueueMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tgvs.wms.business.modules.task.entity.TaskReportQueue">
        <id column="id" property="id" />
        <result column="task_order" property="taskOrder" />
        <result column="task_type" property="taskType" />
        <result column="box_no" property="boxNo" />
        <result column="box_type" property="boxType" />
        <result column="report_type" property="reportType" />
        <result column="report_status" property="reportStatus" />
        <result column="priority" property="priority" />
        <result column="retry_count" property="retryCount" />
        <result column="max_retry_count" property="maxRetryCount" />
        <result column="next_retry_time" property="nextRetryTime" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="first_report_time" property="firstReportTime" />
        <result column="last_report_time" property="lastReportTime" />
        <result column="success_report_time" property="successReportTime" />
        <result column="error_message" property="errorMessage" />
        <result column="error_code" property="errorCode" />
        <result column="material_type" property="materialType" />
        <result column="batch_no" property="batchNo" />
        <result column="remark" property="remark" />
        <result column="version" property="version" />
        <result column="delete_flag" property="deleteFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, task_order, task_type, box_no, box_type, report_type, report_status, priority,
        retry_count, max_retry_count, next_retry_time, create_time, update_time,
        first_report_time, last_report_time, success_report_time, error_message, error_code,
        material_type, batch_no, remark, version, delete_flag
    </sql>

    <!-- 分页查询待上报的任务 -->
    <select id="selectPendingReportTasks" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM wms_task_report_queue
        WHERE delete_flag = 0
        <if test="reportType != null and reportType != ''">
            AND report_type = #{reportType}
        </if>
        <if test="reportStatus != null">
            AND report_status = #{reportStatus}
        </if>
        <if test="priority != null">
            AND priority = #{priority}
        </if>
        ORDER BY priority ASC, create_time ASC
    </select>

    <!-- 查询需要重试的任务列表 -->
    <select id="selectRetryTasks" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM wms_task_report_queue
        WHERE delete_flag = 0
          AND report_status = 2
          AND retry_count &lt; #{maxRetryCount}
          AND (next_retry_time IS NULL OR next_retry_time &lt;= #{currentTime})
        ORDER BY priority ASC, create_time ASC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 批量更新任务状态 -->
    <update id="batchUpdateReportStatus">
        UPDATE wms_task_report_queue
        SET report_status = #{reportStatus},
            last_report_time = NOW(),
            update_time = NOW()
            <if test="errorCode != null and errorCode != ''">
                , error_code = #{errorCode}
            </if>
            <if test="errorMessage != null and errorMessage != ''">
                , error_message = #{errorMessage}
            </if>
            <if test="reportStatus == 1">
                , success_report_time = NOW()
                , error_code = NULL
                , error_message = NULL
            </if>
        WHERE id IN
        <foreach collection="taskIds" item="taskId" open="(" separator="," close=")">
            #{taskId}
        </foreach>
        AND delete_flag = 0
    </update>

    <!-- 根据优先级和创建时间查询待上报任务 -->
    <select id="selectTasksByPriorityAndTime" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM wms_task_report_queue
        WHERE delete_flag = 0
        <if test="reportType != null and reportType != ''">
            AND report_type = #{reportType}
        </if>
        <if test="reportStatus != null">
            AND report_status = #{reportStatus}
        </if>
        ORDER BY priority ASC, create_time ASC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 统计上报任务数量 -->
    <select id="countReportTasks" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM wms_task_report_queue
        WHERE delete_flag = 0
        <if test="reportType != null and reportType != ''">
            AND report_type = #{reportType}
        </if>
        <if test="reportStatus != null">
            AND report_status = #{reportStatus}
        </if>
        <if test="startTime != null">
            AND create_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
    </select>

    <!-- 查询上报成功率统计 -->
    <select id="selectReportSuccessRate" resultType="java.util.Map">
        SELECT
            report_type,
            COUNT(1) as total_count,
            SUM(CASE WHEN report_status = 1 THEN 1 ELSE 0 END) as success_count,
            SUM(CASE WHEN report_status = 2 THEN 1 ELSE 0 END) as failed_count,
            SUM(CASE WHEN report_status = 0 THEN 1 ELSE 0 END) as pending_count,
            ROUND(SUM(CASE WHEN report_status = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(1), 2) as success_rate
        FROM wms_task_report_queue
        WHERE delete_flag = 0
        <if test="reportType != null and reportType != ''">
            AND report_type = #{reportType}
        </if>
        <if test="startTime != null">
            AND create_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
        GROUP BY report_type
    </select>

    <!-- 删除过期的已完成任务记录 -->
    <delete id="deleteExpiredCompletedTasks">
        DELETE FROM wms_task_report_queue
        WHERE report_status = #{reportStatus}
          AND success_report_time &lt; #{expireTime}
          AND delete_flag = 0
    </delete>

    <!-- 查询错误统计信息 -->
    <select id="selectErrorStatistics" resultType="java.util.Map">
        SELECT
            error_code,
            COUNT(1) as error_count,
            error_message as sample_error_message
        FROM wms_task_report_queue
        WHERE delete_flag = 0
          AND report_status = 2
          AND error_code IS NOT NULL
        <if test="startTime != null">
            AND create_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
        GROUP BY error_code, error_message
        ORDER BY error_count DESC
    </select>

    <!-- 重置失败任务的重试次数 -->
    <update id="resetRetryCount">
        UPDATE wms_task_report_queue
        SET retry_count = 0,
            next_retry_time = NULL,
            report_status = 0,
            error_code = NULL,
            error_message = NULL,
            update_time = NOW()
        WHERE id IN
        <foreach collection="taskIds" item="taskId" open="(" separator="," close=")">
            #{taskId}
        </foreach>
        AND delete_flag = 0
    </update>

    <!-- 查询长时间未上报的任务 -->
    <select id="selectTimeoutTasks" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM wms_task_report_queue
        WHERE delete_flag = 0
          AND report_status = 0
          AND create_time &lt; DATE_SUB(NOW(), INTERVAL #{timeoutMinutes} MINUTE)
        ORDER BY create_time ASC
    </select>

    <!-- 自定义查询：按任务类型统计 -->
    <select id="selectTaskTypeStatistics" resultType="java.util.Map">
        SELECT
            task_type,
            COUNT(1) as task_count,
            SUM(CASE WHEN report_status = 1 THEN 1 ELSE 0 END) as success_count,
            SUM(CASE WHEN report_status = 2 THEN 1 ELSE 0 END) as failed_count
        FROM wms_task_report_queue
        WHERE delete_flag = 0
        <if test="startTime != null">
            AND create_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
        GROUP BY task_type
        ORDER BY task_type ASC
    </select>

    <!-- 自定义查询：按优先级统计 -->
    <select id="selectPriorityStatistics" resultType="java.util.Map">
        SELECT
            priority,
            COUNT(1) as task_count,
            AVG(retry_count) as avg_retry_count,
            MAX(retry_count) as max_retry_count
        FROM wms_task_report_queue
        WHERE delete_flag = 0
        <if test="startTime != null">
            AND create_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
        GROUP BY priority
        ORDER BY priority ASC
    </select>

    <!-- 自定义查询：查询重试次数最多的任务 -->
    <select id="selectMostRetriedTasks" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM wms_task_report_queue
        WHERE delete_flag = 0
          AND retry_count > 0
        ORDER BY retry_count DESC, create_time ASC
        LIMIT #{limit}
    </select>

    <!-- 根据任务单号查询物料信息用于ROS上报 -->
    <select id="selectMatInfosByTaskOrder" resultType="java.util.Map">
        SELECT
            d.req_list_id AS ROListID,
            d.contract_no,
            CASE
                WHEN d.quantity = 0 THEN
                    0
                ELSE
                    ROUND(
                            CAST(p.actual_inbound_qty AS DECIMAL (20, 2)) / CAST(d.quantity AS DECIMAL (20, 2)) * CAST(d.base_quantity AS DECIMAL (20, 2)),
                            2 -- 明确指定保留2位小数
                    )
                END AS Qty
        FROM wms_task_report_queue q
        LEFT JOIN wms_auxiliary_prebox p ON BINARY q.task_order = BINARY p.task_order
        LEFT JOIN wms_auxiliary_detail d ON FIND_IN_SET(BINARY d.id, BINARY p.detail_ids)
        WHERE q.task_order =#{taskOrder}
          AND q.delete_flag = 0 and q.material_type=2
          AND COALESCE(p.is_deleted, 0) = 0
          AND COALESCE(d.delete_flag, 0) = 0
          AND d.req_list_id IS NOT NULL
          AND COALESCE(p.actual_inbound_qty, d.quantity) > 0
    </select>

    <!-- 根据任务单号查询出库物料信息用于ROS上报 -->
    <select id="selectOutboundMatInfosByTaskOrder" resultType="java.util.Map">
        SELECT
            d.req_list_id AS ROListID,
            d.contract_no,
            CASE
                WHEN d.quantity = 0 THEN
                    0
                ELSE
                    ROUND(
                            CAST(op.actual_quantity AS DECIMAL (20, 8)) / CAST(d.quantity AS DECIMAL (20, 8)) * CAST(d.base_quantity AS DECIMAL (20, 8)),
                            2
                    )
                END AS Qty
        FROM wms_task_report_queue q
        LEFT JOIN wms_box_task_list bt ON BINARY q.task_order = BINARY bt.task_order
        LEFT JOIN wms_auxiliary_outprebox op ON BINARY bt.task_order = BINARY op.task_order
        LEFT JOIN wms_auxiliary_detail d ON BINARY op.outbound_no = BINARY d.ref_number
                                         AND BINARY op.material_code = BINARY d.material_code
        WHERE BINARY q.task_order = BINARY #{taskOrder}
          AND q.delete_flag = 0
          AND COALESCE(bt.delete_flag, 0) = 0
          AND COALESCE(d.delete_flag, 0) = 0
          AND d.operation_type = 1
          AND d.req_list_id IS NOT NULL
          AND COALESCE(
              CAST(op.actual_quantity AS DECIMAL(10,2)),
              CAST(op.reserved_quantity AS DECIMAL(10,2)),
              CAST(d.quantity AS DECIMAL(10,2))
          ) > 0
          AND (op.id IS NULL OR COALESCE(op.reservation_status, 1) IN (1, 2))
    </select>

    <!-- 批量根据任务单号查询入库物料信息用于ROS上报 -->
    <select id="selectBatchMatInfosByTaskOrders" resultType="java.util.Map">
        SELECT
            d.req_list_id AS ROListID,
            d.contract_no,
            CASE
                WHEN d.quantity = 0 THEN
                    0
                ELSE
                    ROUND(
                            CAST(p.actual_inbound_qty AS DECIMAL (20, 2)) / CAST(d.quantity AS DECIMAL (20, 2)) * CAST(d.base_quantity AS DECIMAL (20, 2)),
                            2 -- 明确指定保留2位小数
                    )
                END AS Qty
        FROM wms_task_report_queue q
        LEFT JOIN wms_auxiliary_prebox p ON BINARY q.task_order = BINARY p.task_order
        LEFT JOIN wms_auxiliary_detail d ON FIND_IN_SET(BINARY d.id, BINARY p.detail_ids)
        WHERE q.task_order IN
        <foreach collection="taskOrders" item="taskOrder" open="(" separator="," close=")">
            #{taskOrder}
        </foreach>
          AND q.delete_flag = 0 and q.material_type=2
          AND COALESCE(p.is_deleted, 0) = 0
          AND COALESCE(d.delete_flag, 0) = 0
          AND d.req_list_id IS NOT NULL
          AND COALESCE(p.actual_inbound_qty, d.quantity) > 0
    </select>

    <!-- 批量根据任务单号查询出库物料信息用于ROS上报 -->
    <select id="selectBatchOutboundMatInfosByTaskOrders" resultType="java.util.Map">
        SELECT
        d.req_list_id AS ROListID,
        d.contract_no,
        CASE
        WHEN d.quantity = 0 THEN 0
        ELSE ROUND(
        CAST(SUM(COALESCE(op.actual_quantity, op.reserved_quantity, 0)) AS DECIMAL(20, 8)) / CAST(d.quantity AS DECIMAL(20, 8)) * CAST(d.base_quantity AS DECIMAL(20, 8)),
        2
        )
        END AS Qty
        FROM wms_auxiliary_detail d
        INNER JOIN wms_auxiliary_outprebox op ON BINARY op.outbound_no = BINARY d.ref_number
        AND (
        CONCAT(',', d.prebox_id, ',') LIKE CONCAT('%,', op.id, ',%')
        )
        INNER JOIN wms_box_task_list bt ON BINARY bt.task_order = BINARY op.task_order
        INNER JOIN wms_task_report_queue q ON BINARY q.task_order = BINARY bt.task_order
        WHERE q.task_order IN
        <foreach collection="taskOrders" item="taskOrder" open="(" separator="," close=")">
            #{taskOrder}
        </foreach>
        AND q.delete_flag = 0
        AND bt.delete_flag = 0
        AND d.delete_flag = 0
        AND d.operation_type = 1
        AND d.req_list_id IS NOT NULL
        AND COALESCE(op.actual_quantity, op.reserved_quantity, 0) > 0
        AND COALESCE(op.reservation_status, 1) IN (1, 2)
        GROUP BY d.req_list_id, d.contract_no, d.quantity, d.base_quantity
        HAVING SUM(COALESCE(op.actual_quantity, op.reserved_quantity, 0)) > 0
    </select>

</mapper>
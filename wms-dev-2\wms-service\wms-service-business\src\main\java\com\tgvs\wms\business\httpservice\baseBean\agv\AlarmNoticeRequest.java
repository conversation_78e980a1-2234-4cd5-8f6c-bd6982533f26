package com.tgvs.wms.business.httpservice.baseBean.agv;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * 告警推送通知请求类，用于RCS-2000告警推送通知接口的入参
 */
@Data
public class AlarmNoticeRequest {
    @JSONField(name = "alarmCode")
    private String alarmCode; // 告警代码，唯一标识一种告警类型

    @JSONField(name = "alarmMessage")
    private String alarmMessage; // 告警详细信息

    @JSONField(name = "deviceId")
    private String deviceId; // 设备ID，标识告警来源设备
}

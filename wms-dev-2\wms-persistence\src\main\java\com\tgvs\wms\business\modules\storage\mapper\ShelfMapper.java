package com.tgvs.wms.business.modules.storage.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tgvs.wms.business.modules.storage.entity.Shelf;
import com.tgvs.wms.business.modules.storage.entity.ShelfBookLevel;
import com.tgvs.wms.business.modules.storage.entity.ShelfPie;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ShelfMapper extends BaseMapper<Shelf> {
    /**
     * 按箱子类型分组统计货架数量
     * @param paramString 区域参数
     * @return 箱子类型及对应数量列表
     */
    List<ShelfPie> selectListCount(@Param("area") String paramString);

    /**
     * 查询可用于入库的货架位置
     * @param paramInteger 高度参数
     * @return 按区域分组的可用货架数量
     */
    List<ShelfPie> selectInBound(@Param("height") Integer paramInteger);

    /**
     * 根据预订号查询可用于入库的货架位置
     * @param paramString 预订号
     * @param paramInteger 高度参数
     * @return 按区域分组的可用货架数量
     */
    List<ShelfPie> selectInBoundByBookNo(@Param("bookno") String paramString, @Param("height") Integer paramInteger);

    /**
     * 查询指定箱子类型的已使用但未锁定的货架位置
     * @param paramInteger1 箱子类型
     * @param paramInteger2 高度参数
     * @return 按区域分组的货架数量
     */
    List<ShelfPie> selectInBoundEmpty(@Param("boxempty") Integer paramInteger1, @Param("height") Integer paramInteger2);

    /**
     * 根据货架编码查询货架详细信息
     * @param paramString 货架编码
     * @return 货架信息
     */
    Shelf selectBycode(@Param("code") String paramString);

    /**
     * 根据预订号查询适合分配的二级货架
     * @param paramString 预订号
     * @return 适合分配的货架层级信息列表
     */
    List<ShelfBookLevel> selectAllotByBookNoTwo(@Param("bookno") String paramString);

    /**
     * 根据预订号和提升区域查询适合分配的四级货架
     * @param paramString1 预订号
     * @param paramString2 提升区域
     * @return 适合分配的货架层级信息列表
     */
    List<ShelfBookLevel> selectAllotByBookNoFour(@Param("bookno") String paramString1, @Param("lift_area") String paramString2);

    /**
     * 根据箱子空置状态查询适合分配的二级货架
     * @param paramInteger 箱子类型
     * @return 适合分配的货架层级信息列表
     */
    List<ShelfBookLevel> selectAllotByBoxEmptyTwo(@Param("boxempty") Integer paramInteger);

    /**
     * 根据箱子空置状态和提升区域查询适合分配的四级货架
     * @param paramInteger 箱子类型
     * @param paramString 提升区域
     * @return 适合分配的货架层级信息列表
     */
    List<ShelfBookLevel> selectAllotByBoxEmptyFour(@Param("boxempty") Integer paramInteger, @Param("lift_area") String paramString);
    
    /**
     * 查询货位表中的空容器
     * @param boxType 容器类型
     * @return 空托盘货位信息
     */
    List<Shelf> selectEmtpyBox(@Param("boxType") Integer boxType);

    /**
     * 查询货位表中的空容器
     * @param boxType 容器类型
     * @return 空托盘货位信息
     */
    List<Shelf> selectEmtpyBoxType(@Param("boxType") Integer boxType,@Param("boxContainerType") Integer boxContainerType);

    /**
     * 根据箱子编号查询货架信息
     * @param boxNo 箱子编号
     * @return 货架信息
     */
    Shelf selectByBoxNo(@Param("boxNo") String boxNo);
}

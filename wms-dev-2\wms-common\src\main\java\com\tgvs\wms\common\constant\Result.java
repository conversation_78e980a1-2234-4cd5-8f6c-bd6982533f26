package com.tgvs.wms.common.constant;

import java.io.Serializable;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;

@Data
@Schema(description = "API响应")
public class Result<T> implements Serializable {

    @Schema(description = "是否成功", example = "true")
    private boolean success = true;
    
    @Schema(description = "响应码", example = "0")
    private Integer Code = Integer.valueOf(0);
    
    @Schema(description = "响应消息", example = "操作成功")
    private String message = null;
    
    @Schema(description = "总记录数")
    private Long total = null;
    
    @Schema(description = "响应数据")
    private T data;

    public static <T> Result<T> ok(){
        Result result = new Result();

        result.setSuccess(true);
        result.setCode(CommonConstant.RESULT_CODE_200);
        result.setMessage(CommonConstant.RESULT_MESSAGE_SUCCESS);

        return result;
    }

    public static <T> Result<T> ok(T data){
        Result result = new Result();

        result.setSuccess(true);
        result.setCode(CommonConstant.RESULT_CODE_200);
        result.setMessage(CommonConstant.RESULT_MESSAGE_SUCCESS);
        result.setData(data);

        return result;
    }

    public static <T> Result<T> OK(T data){
        Result result = new Result();

        result.setSuccess(true);
        result.setCode(CommonConstant.RESULT_CODE_200);
        result.setMessage(CommonConstant.RESULT_MESSAGE_SUCCESS);
        result.setData(data);

        return result;
    }

    public static <T> Result<T> okPage(IPage<T> pageResult){
        Result result = new Result();

        result.setSuccess(true);
        result.setCode(CommonConstant.RESULT_CODE_200);
        result.setMessage(CommonConstant.RESULT_MESSAGE_SUCCESS);
        result.setData(pageResult.getRecords());
        result.setTotal(pageResult.getTotal());

        return result;
    }

    public static <T> Result<T> ok(String message, T data){
        Result result = new Result();

        result.setSuccess(true);
        result.setCode(CommonConstant.RESULT_CODE_200);
        result.setMessage(message);
        result.setData(data);

        return result;
    }

    public static <T> Result<T> error(){
        Result result = new Result();

        result.setSuccess(false);
        result.setCode(CommonConstant.RESULT_CODE_500);
        result.setMessage(CommonConstant.RESULT_MESSAGE_ERROR);

        return result;
    }

    public static <T> Result<T> error(String message){
        Result result = new Result();

        result.setSuccess(false);
        result.setCode(CommonConstant.RESULT_CODE_500);
        result.setMessage(message);

        return result;
    }
    
    /**
     * 失败响应，使用指定错误码
     *
     * @param code    错误码
     * @param message 错误消息
     */
    public static <T> Result<T> error(String code, String message){
        Result result = new Result();

        result.setSuccess(false);
        result.setCode(Integer.valueOf(code));
        result.setMessage(message);

        return result;
    }

    public static <T> Result<T> result(boolean isSuccess){
        return isSuccess ? ok() : error();
    }
}

package com.tgvs.wms.business.modules.bd.entity;

import java.util.Date;

import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.tgvs.wms.base.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TableName("wms_base_area")
@ApiModel(value = "wms_base_area对象", description = "作业区域管理")
@Data
public class Area extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键")
    private String id;

    @Excel(name = "区域值", width = 15.0D)
    @ApiModelProperty("区域值")
    private Integer value;

    @Excel(name = "区域代码", width = 15.0D)
    @ApiModelProperty("区域代码")
    private String code;

    @Excel(name = "区域描述", width = 15.0D)
    @ApiModelProperty("区域描述")
    private String text;

    @ApiModelProperty("创建人")
    private String createBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("更新时间")
    private Date updateTime;

    @Excel(name = "删除状态", width = 15.0D)
    @ApiModelProperty("删除状态")
    private Integer delFlag;
}

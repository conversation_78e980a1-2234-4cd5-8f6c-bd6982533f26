package com.tgvs.wms.business.enums;


import com.tgvs.wms.common.util.oConvertUtils;

public enum enumWebApiResultError {
    E_DEFAULT(Integer.valueOf(0), "0000", "未定义的异常"),
    E_SONGBU_SITE_TASK_NOT_FINISH(Integer.valueOf(1052), "1052", "松布工位有未完成的任务"),
    E_SONGBU_SITE_TASK_NOT_FINISH2(Integer.valueOf(1060), "1060", "松布工位有未完成的任务"),
    E_CUTTING_SITE_TASK_NOT_FINISH(Integer.valueOf(1053), "1025", "裁剪工位有未完成的任务"),
    E_SITE_NOT_SONGBU(Integer.valueOf(1064), "1064", "位置不是松布工位"),
    E_DOLLY_NOT_EXIST(Integer.valueOf(1007), "1007", "松布架号不存在"),
    E_DOLLY_OTHER_SITE(Integer.valueOf(1069), "1069", "松布架已在其他位置"),
    E_NOT_LOCATION(Integer.valueOf(1012), "1012", "未找到空储位"),
    E_NOT_ENOUGH_LOCATION(Integer.valueOf(1044), "1044", "在松布静置区未找到空储位"),
    E_NOT_ENOUGH_DOLLY_SURPLUS(Integer.valueOf(1017), "1017", "未找到余布松布架"),
    E_NOT_ENOUGH_DOLLY_EMPTY(Integer.valueOf(1016), "1016", "未找到空松布架"),
    E_UPDATE_DATABASE_FAULT(Integer.valueOf(1008), "1008", "数据库更新失败"),
    E_CLOTH_TASK_NOT_FINISH(Integer.valueOf(1041), "1041", "布匹有未完成的任务"),
    E_CLOTH_NOT_EXIST(Integer.valueOf(1067), "1067", "布匹不存在"),
    E_CLOTH_NOT_EXIST2(Integer.valueOf(1076), "1076", "布匹不存在"),
    E_SITE_NOT_EXIST(Integer.valueOf(1028), "1028", "工位/储位号不存在");

    private Integer value;

    private String code;

    private String text;

    enumWebApiResultError(Integer value, String code, String text) {
        this.value = value;
        this.code = code;
        this.text = text;
    }

    public static enumWebApiResultError getByValue(int value) {
        if (oConvertUtils.isEmpty(Integer.valueOf(value)))
            return null;
        for (enumWebApiResultError val : values()) {
            if (val.getValue().equals(Integer.valueOf(value)))
                return val;
        }
        return null;
    }

    public Integer getValue() {
        return this.value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getText() {
        return this.text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public static enumWebApiResultError toEnum(Integer Value) {
        for (enumWebApiResultError e : values()) {
            if (e.getValue().equals(Value))
                return e;
        }
        return null;
    }

    public static enumWebApiResultError toEnum(String code) {
        for (enumWebApiResultError e : values()) {
            if (e.getCode().equals(code))
                return e;
        }
        return E_DEFAULT;
    }
}

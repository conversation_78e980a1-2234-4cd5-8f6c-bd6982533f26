package com.tgvs.wms.business.modules.system.dto;

import com.tgvs.wms.business.modules.system.entity.ApiLog;
import lombok.Data;

import java.util.List;

/**
 * API日志分页结果
 */
@Data
public class ApiLogPageResult {

    /**
     * 数据列表
     */
    private List<ApiLog> records;

    /**
     * 总记录数
     */
    private Long total;

    /**
     * 当前页码
     */
    private Integer page;

    /**
     * 每页大小
     */
    private Integer limit;

    /**
     * 总页数
     */
    private Integer totalPages;

    public ApiLogPageResult() {}

    public ApiLogPageResult(List<ApiLog> records, Long total, Integer page, Integer limit) {
        this.records = records;
        this.total = total;
        this.page = page;
        this.limit = limit;
        this.totalPages = (int) Math.ceil((double) total / limit);
    }
}

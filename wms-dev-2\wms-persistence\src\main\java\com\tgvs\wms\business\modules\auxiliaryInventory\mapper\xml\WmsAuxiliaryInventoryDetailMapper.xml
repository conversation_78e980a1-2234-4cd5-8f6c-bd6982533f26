<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tgvs.wms.business.modules.auxiliaryInventory.mapper.WmsAuxiliaryInventoryDetailMapper">

    <!-- 通用结果映射 -->
    <resultMap id="BaseResultMap" type="com.tgvs.wms.business.modules.auxiliaryInventory.entity.WmsAuxiliaryInventoryDetail">
        <id column="id" property="id"/>
        <result column="inventory_id" property="inventoryId"/>
        <result column="unit" property="unit"/>
        <result column="location_code" property="locationCode"/>
        <result column="location_name" property="locationName"/>
        <result column="batch_no" property="batchNo"/>
        <result column="system_qty" property="systemQty"/>
        <result column="diff_qty" property="diffQty"/>
        <result column="diff_reason" property="diffReason"/>
        <result column="status" property="status"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 根据盘点单ID查询明细列表 -->
    <select id="selectByInventoryId" resultMap="BaseResultMap">
        SELECT
            id, inventory_id, unit,
            location_code, location_name, batch_no, system_qty,
            diff_qty, diff_reason,
            status,   create_by, create_time, update_by, update_time
        FROM wms_auxiliary_inventory_detail
        WHERE inventory_id = #{inventoryId}
        ORDER BY auxiliary_code, location_code
    </select>

    <!-- 分页查询盘点明细 -->
    <select id="selectDetailPage" resultMap="BaseResultMap">
        SELECT 
            id, inventory_id, auxiliary_code, auxiliary_name, auxiliary_spec, unit,
            location_code, location_name, batch_no, system_qty, first_count_qty,
            second_count_qty, final_count_qty, diff_qty, diff_reason, count_times,
            status, first_count_by, first_count_time, second_count_by, second_count_time,
            final_count_by, final_count_time, remark, create_by, create_time, update_by, update_time
        FROM wms_auxiliary_inventory_detail
        WHERE inventory_id = #{inventoryId}
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        ORDER BY auxiliary_code, location_code
    </select>

    <!-- 统计盘点进度 -->
    <select id="countProgressByInventoryId" resultType="com.tgvs.wms.business.modules.auxiliaryInventory.mapper.WmsAuxiliaryInventoryDetailMapper$InventoryProgressVO">
        SELECT 
            status,
            COUNT(1) as count
        FROM wms_auxiliary_inventory_detail
        WHERE inventory_id = #{inventoryId}
        GROUP BY status
    </select>

    <!-- 根据库位和辅料查询明细 -->
    <select id="selectByLocationAndAuxiliary" resultMap="BaseResultMap">
        SELECT 
            id, inventory_id, auxiliary_code, auxiliary_name, auxiliary_spec, unit,
            location_code, location_name, batch_no, system_qty, first_count_qty,
            second_count_qty, final_count_qty, diff_qty, diff_reason, count_times,
            status, first_count_by, first_count_time, second_count_by, second_count_time,
            final_count_by, final_count_time, remark, create_by, create_time, update_by, update_time
        FROM wms_auxiliary_inventory_detail
        WHERE inventory_id = #{inventoryId}
          AND auxiliary_code = #{auxiliaryCode}
          AND location_code = #{locationCode}
        LIMIT 1
    </select>

    <!-- 批量更新盘点数量 -->
    <update id="batchUpdateCountQty">
        <foreach collection="details" item="detail" separator=";">
            UPDATE wms_auxiliary_inventory_detail
            SET 
                <choose>
                    <when test="detail.firstCountQty != null">
                        first_count_qty = #{detail.firstCountQty},
                        first_count_by = #{detail.firstCountBy},
                        first_count_time = #{detail.firstCountTime},
                        count_times = count_times + 1,
                        status = 'FIRST_COUNT'
                    </when>
                    <when test="detail.secondCountQty != null">
                        second_count_qty = #{detail.secondCountQty},
                        second_count_by = #{detail.secondCountBy},
                        second_count_time = #{detail.secondCountTime},
                        count_times = count_times + 1,
                        status = 'SECOND_COUNT'
                    </when>
                    <otherwise>
                        final_count_qty = #{detail.finalCountQty},
                        final_count_by = #{detail.finalCountBy},
                        final_count_time = #{detail.finalCountTime},
                        status = 'COMPLETED'
                    </otherwise>
                </choose>
                , diff_qty = #{detail.diffQty}
                , update_by = #{detail.updateBy}
                , update_time = #{detail.updateTime}
            WHERE id = #{detail.id}
        </foreach>
    </update>

    <!-- 统计差异项目 -->
    <select id="selectDiffItems" resultMap="BaseResultMap">
        SELECT 
            id, inventory_id, auxiliary_code, auxiliary_name, auxiliary_spec, unit,
            location_code, location_name, batch_no, system_qty, first_count_qty,
            second_count_qty, final_count_qty, diff_qty, diff_reason, count_times,
            status, first_count_by, first_count_time, second_count_by, second_count_time,
            final_count_by, final_count_time, remark, create_by, create_time, update_by, update_time
        FROM wms_auxiliary_inventory_detail
        WHERE inventory_id = #{inventoryId}
          AND diff_qty != 0
        ORDER BY ABS(diff_qty) DESC
    </select>

</mapper>
package com.tgvs.wms.business.modules.container.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tgvs.wms.base.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;



@EqualsAndHashCode(callSuper = true)
@TableName ( "wms_box_item" )
@ApiModel(value = "wms_box_item对象", description = "容器明细")
@Data
public class BoxItem extends BaseEntity {

	private static final long serialVersionUID =  8101313278621292761L;

	/**
	 * 主键
	 */
   	@TableId(value = "id", type = IdType.AUTO)
	private String id;

	/**
	 * 容器类型：1-料箱 2-托盘号
	 */
   	@TableField(value = "box_type")
	private Integer boxType;

	/**
	 * 批次号
	 */
   	@TableField(value = "batch_no")
	private String batchNo;

	/**
	 * 箱号/托盘号
	 */
   	@TableField(value = "box_no")
	private String boxNo;

	/**
	 * 格子号
	 */
   	@TableField(value = "grid_id")
	private Integer gridId;

	/**
	 * 格子状态：0-空 1-25% 2-50% 3-75% 4-满格
	 */
   	@TableField(value = "grid_status")
	private Integer gridStatus;


	/**
	 * 托盘容量
	 */
	@TableField(value = "grid_volume")
	private Integer gridVolume;

	/**
	 * 合约号
	 */
   	@TableField(value = "contract_no")
	private String contractNo;
	/**
	 * 合约号-Po号（款式属性才有)
	 */
	@TableField(value = "contract_no_po")
	   private String contractNoPo;

	/**
	 * 款号
	 */
   	@TableField(value = "item_no")
	private String itemNo;

	/**
	 * 物料编码
	 */
   	@TableField(value = "material_code")
	private String materialCode;

	/**
	 * 物料名称
	 */
   	@TableField(value = "material_name")
	private String materialName;

	/**
	 * 物料颜色
	 */
   	@TableField(value = "material_color")
	private String materialColor;

	/**
	 * 物料规格
	 */
   	@TableField(value = "material_model")
	private String materialModel;

	/**
	 * 尺码
	 */
   	@TableField(value = "material_size")
	private String materialSize;

	/**
	 * 物料数量(每格)
	 */
   	@TableField(value = "material_quantity")
	private BigDecimal materialQuantity;

	/**
	 * 物料属性(0-自身属性 1-款式属性)
	 */
	@TableField(value = "material_property")
	private Integer materialProperty;

	/**
	 * 存储批次ID - 用于标识混合存储场景下的同批次物料
	 * 同一格子中的多个SKU可以共享同一个存储批次ID
	 */
	@TableField(value = "storage_batch_id")
	private String storageBatchId;

	/**
	 * 预装数量(每格)
	 */
	@TableField(value = "pre_installed_quantity")
	private Integer preInstalledQantity;
	/**
	 * 预装状态：0.未使用，1.预装，2.已装入库，3拣货出库完成.
	 */
   	@TableField(value = "status")
	private Integer status;

	/**
	 * 创建人
	 */
   	@TableField(value = "create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
   	@TableField(value = "create_time")
	private Date createTime;

	/**
	 * 更新人
	 */
   	@TableField(value = "update_by")
	private String updateBy;

	/**
	 * 更新时间
	 */
   	@TableField(value = "update_time")
	private Date updateTime;

	/**
	 * 删除标志:0.未删除，1.已删除
	 */
   	@TableField(value = "delete_flag")
	private Integer deleteFlag;

}

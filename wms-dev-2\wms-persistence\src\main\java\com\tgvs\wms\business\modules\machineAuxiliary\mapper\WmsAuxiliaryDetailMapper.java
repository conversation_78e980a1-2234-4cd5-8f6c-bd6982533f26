package com.tgvs.wms.business.modules.machineAuxiliary.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tgvs.wms.business.modules.machineAuxiliary.entity.WmsAuxiliaryDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 辅料出入库明细Mapper接口
 */
public interface WmsAuxiliaryDetailMapper extends BaseMapper<WmsAuxiliaryDetail> {
    
    /**
     * 根据关联单号查询明细列表
     *
     * @param refNumber 关联单号
     * @return 明细列表
     */
    List<WmsAuxiliaryDetail> selectByRefNumber(@Param("refNumber") String refNumber);

    /**
     * 根据物料编码查询明细列表
     *
     * @param materialCode 物料编码
     * @return 明细列表
     */
    List<WmsAuxiliaryDetail> selectByMaterialCode(@Param("materialCode") String materialCode);

    /**
     * 根据操作类型查询明细列表
     *
     * @param operationType 操作类型(0入库,1出库)
     * @return 明细列表
     */
    List<WmsAuxiliaryDetail> selectByOperationType(@Param("operationType") Integer operationType);

    /**
     * 根据物料编码统计数量
     *
     * @param materialCode 物料编码
     * @param operationType 操作类型(0入库,1出库)
     * @return 数量
     */
    Integer sumQuantityByMaterial(@Param("materialCode") String materialCode, @Param("operationType") Integer operationType);
}
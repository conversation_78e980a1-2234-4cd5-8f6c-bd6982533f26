package com.tgvs.wms.business.enums;

import com.tgvs.wms.common.util.oConvertUtils;

public enum enumBoxEmptyStatus {
    unknow(Integer.valueOf(-1), "unknow", "无箱"),
    emptyin(Integer.valueOf(0), "emptyin", "小空箱"),
    empty(Integer.valueOf(1), "empty", "大空箱"),
    full(Integer.valueOf(2), "full", "实箱");

    private Integer value;

    private String code;

    private String text;

    enumBoxEmptyStatus(Integer value, String code, String text) {
        this.value = value;
        this.code = code;
        this.text = text;
    }

    public static enumBoxEmptyStatus getByValue(int value) {
        if (oConvertUtils.isEmpty(Integer.valueOf(value)))
            return null;
        for (enumBoxEmptyStatus val : values()) {
            if (val.getValue().equals(Integer.valueOf(value)))
                return val;
        }
        return null;
    }

    public Integer getValue() {
        return this.value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getText() {
        return this.text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public static enumBoxEmptyStatus toEnum(Integer Value) {
        for (enumBoxEmptyStatus e : values()) {
            if (e.getValue() == Value)
                return e;
        }
        return null;
    }

    public static enumBoxEmptyStatus toEnum(String Value) {
        for (enumBoxEmptyStatus e : values()) {
            if (e.getValue().toString().equals(Value))
                return e;
        }
        return null;
    }
}

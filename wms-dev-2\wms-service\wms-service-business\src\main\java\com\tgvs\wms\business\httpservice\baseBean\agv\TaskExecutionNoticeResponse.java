package com.tgvs.wms.business.httpservice.baseBean.agv;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

/**
 * 任务执行过程回馈接口响应类
 */
@Data
public class TaskExecutionNoticeResponse {
    /**
     * 消息码
     */
    private String code;

    /**
     * 消息内容
     */
    private String message;

    /**
     * 任务号，全局唯一
     */
    private String robotTaskCode;

    /**
     * 其他业务数据
     */
    private JSONObject data;

    /**
     * 创建成功响应
     *
     * @param robotTaskCode 任务号
     * @return 成功响应
     */
    public static TaskExecutionNoticeResponse success(String robotTaskCode) {
        TaskExecutionNoticeResponse response = new TaskExecutionNoticeResponse();
        response.setCode("SUCCESS");
        response.setMessage("成功");
        response.setRobotTaskCode(robotTaskCode);
        
        JSONObject data = new JSONObject();
        data.put("robotTaskCode", robotTaskCode);
        response.setData(data);
        
        return response;
    }
    public static TaskExecutionNoticeResponse Error(String robotTaskCode,String Msg) {
        TaskExecutionNoticeResponse response = new TaskExecutionNoticeResponse();
        response.setCode("Error");
        response.setMessage("请求失败:"+Msg);
        response.setRobotTaskCode(robotTaskCode);

        JSONObject data = new JSONObject();
        data.put("robotTaskCode", robotTaskCode);
        response.setData(data);

        return response;
    }

} 
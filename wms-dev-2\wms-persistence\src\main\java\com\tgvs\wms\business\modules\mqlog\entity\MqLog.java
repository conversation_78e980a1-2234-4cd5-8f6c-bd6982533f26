package com.tgvs.wms.business.modules.mqlog.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.tgvs.wms.common.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@TableName("wms_mq_log")
@ApiModel(value = "wms_mq_log对象", description = "通讯日志")
@Data
public class MqLog implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键")
    private String id;

    @Excel(name = "通讯系统", width = 15.0D, dictTable = "wms_commncation_config", dicText = "sysname", dicCode = "sysid")
    @Dict(dictTable = "wms_commncation_config", dicText = "sysname", dicCode = "sysid")
    @ApiModelProperty("通讯系统")
    private String sysid;

    @Excel(name = "通讯设备", width = 15.0D, dictTable = "wms_commncation_device", dicText = "deviceName", dicCode = "deviceNo")
    @Dict(dictTable = "wms_commncation_device", dicText = "deviceName", dicCode = "deviceNo")
    @ApiModelProperty("通讯设备")
    private String device;

    @Excel(name = "发送者", width = 15.0D)
    @ApiModelProperty("发送者")
    private String sender;

    @Excel(name = "接收者", width = 15.0D)
    @ApiModelProperty("接收者")
    private String receive;

    @Excel(name = "消息类型", width = 15.0D, dictTable = "wms_mq_type", dicText = "text", dicCode = "code")
    @Dict(dictTable = "wms_mq_type", dicText = "text", dicCode = "code")
    @ApiModelProperty("消息类型")
    private String type;

    @Excel(name = "消息序号", width = 15.0D)
    @ApiModelProperty("消息序号")
    private String mqNumber;

    @Excel(name = "日期", width = 15.0D, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("时间")
    private Date mqTime;

    @ApiModelProperty("创建人")
    private String createBy;

    @Excel(name = "时间", width = 20.0D, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @Excel(name = "消息文本", width = 15.0D)
    @ApiModelProperty("消息文本")
    private String msg;

    @ApiModelProperty("更新人")
    private String updateBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("更新时间")
    private Date updateTime;
}

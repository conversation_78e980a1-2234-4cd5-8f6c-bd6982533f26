package com.tgvs.wms.business.modules.system.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * SQLite API日志实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ApiLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 请求唯一ID
     */
    private String requestId;

    /**
     * 模块名称
     */
    private String module;

    /**
     * 类名
     */
    private String className;

    /**
     * 方法名
     */
    private String methodName;

    /**
     * 请求URL
     */
    private String requestUrl;

    /**
     * 请求方式(GET/POST)
     */
    private String requestMethod;

    /**
     * 客户端IP
     */
    private String clientIp;

    /**
     * 用户代理
     */
    private String userAgent;

    /**
     * 请求参数
     */
    private String requestParams;

    /**
     * 响应数据
     */
    private String responseData;

    /**
     * 执行时间(毫秒)
     */
    private Long executionTime;

    /**
     * 执行状态(SUCCESS/FAILED)
     */
    private String status;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 执行时间格式化显示
     */
    public String getExecutionTimeDisplay() {
        if (executionTime == null) {
            return "0ms";
        }
        if (executionTime < 1000) {
            return executionTime + "ms";
        } else {
            return String.format("%.2fs", executionTime / 1000.0);
        }
    }

    /**
     * 状态显示
     */
    public String getStatusDisplay() {
        if ("SUCCESS".equals(status)) {
            return "成功";
        } else if ("FAILED".equals(status)) {
            return "失败";
        }
        return status;
    }

    /**
     * 请求参数简化显示（前100个字符）
     */
    public String getRequestParamsShort() {
        if (requestParams == null) {
            return "";
        }
        if (requestParams.length() <= 100) {
            return requestParams;
        }
        return requestParams.substring(0, 100) + "...";
    }

    /**
     * 提取关键业务参数（容器号、任务号、订单号等）
     */
    public String getKeyBusinessParams() {
        if (requestParams == null || requestParams.trim().isEmpty()) {
            return "-";
        }

        StringBuilder keyParams = new StringBuilder();
        String params = requestParams.toLowerCase();

        // 提取容器号
        String containerNo = extractParam(params, new String[]{"containerno", "container_no", "containernum", "boxno", "box_no"});
        if (containerNo != null) {
            keyParams.append("容器:").append(containerNo).append(" ");
        }

        // 提取任务号
        String taskNo = extractParam(params, new String[]{"taskno", "task_no", "taskid", "task_id", "tasknumber"});
        if (taskNo != null) {
            keyParams.append("任务:").append(taskNo).append(" ");
        }

        // 提取订单号
        String orderNo = extractParam(params, new String[]{"orderno", "order_no", "orderid", "order_id", "ordernumber"});
        if (orderNo != null) {
            keyParams.append("订单:").append(orderNo).append(" ");
        }

        // 提取批次号
        String batchNo = extractParam(params, new String[]{"batchno", "batch_no", "batchid", "batch_id"});
        if (batchNo != null) {
            keyParams.append("批次:").append(batchNo).append(" ");
        }

        return keyParams.length() > 0 ? keyParams.toString().trim() : "-";
    }

    /**
     * 从参数字符串中提取指定字段的值
     */
    private String extractParam(String params, String[] fieldNames) {
        for (String fieldName : fieldNames) {
            // 匹配 "fieldName":"value" 或 "fieldName":value 格式
            String pattern1 = "\"" + fieldName + "\":\"([^\"]+)\"";
            String pattern2 = "\"" + fieldName + "\":([^,}\\]\\s]+)";
            String pattern3 = fieldName + "=([^&\\s]+)";

            String value = extractByPattern(params, pattern1);
            if (value == null) {
                value = extractByPattern(params, pattern2);
            }
            if (value == null) {
                value = extractByPattern(params, pattern3);
            }

            if (value != null && !value.trim().isEmpty() && !"null".equals(value.trim())) {
                return value.trim();
            }
        }
        return null;
    }

    /**
     * 使用正则表达式提取值
     */
    private String extractByPattern(String text, String pattern) {
        try {
            java.util.regex.Pattern p = java.util.regex.Pattern.compile(pattern, java.util.regex.Pattern.CASE_INSENSITIVE);
            java.util.regex.Matcher m = p.matcher(text);
            if (m.find()) {
                return m.group(1);
            }
        } catch (Exception e) {
            // 忽略正则表达式错误
        }
        return null;
    }

    /**
     * 响应数据简化显示（前100个字符）
     */
    public String getResponseDataShort() {
        if (responseData == null) {
            return "";
        }
        if (responseData.length() <= 100) {
            return responseData;
        }
        return responseData.substring(0, 100) + "...";
    }
}

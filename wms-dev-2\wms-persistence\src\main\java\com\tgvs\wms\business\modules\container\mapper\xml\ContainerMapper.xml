<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tgvs.wms.business.modules.container.mapper.ContainerMapper">

    <select id="selectBoxNo" resultType="com.tgvs.wms.business.modules.container.entity.Container">
        select *
        from wms_box
        <where>
            <if test="boxNo != null and boxNo != ''">
                AND box_no = #{boxNo}
            </if>
        </where>

    </select>

    <select id="selectTrayNo" resultType="com.tgvs.wms.business.modules.container.entity.Container">
        select *
        from wms_box
        <where>
            box_empty=0
            <if test="boxType != null and boxType != ''">
                AND box_type = #{boxType}
            </if>
        </where>

    </select>

    <select id="selectContainerType" resultType="com.tgvs.wms.business.modules.container.entity.Container">
        select *
        from wms_box
        <where>
            <if test="materialType != null and materialType != ''">
                AND material_type = #{materialType}
            </if>
            <if test="boxEmpty != null and boxEmpty != ''">
                AND box_empty = #{boxEmpty}
            </if>
            <if test="boxContainerType != null and boxContainerType != ''">
                AND box_container_type &lt;= #{boxContainerType}
            </if>
            AND box_empty_status &lt; box_container_type
            ORDER BY box_container_type desc, box_empty_status DESC
        </where>

    </select>

    <select id="selectEmptyBoxList" resultType="com.tgvs.wms.business.modules.container.entity.Container">
        select  *
        from wms_box
        <where>
            box_empty_status=0
            <if test="boxContainerType != null and boxContainerType != ''">
                AND box_container_type &lt;= #{boxContainerType}
            </if>
            ORDER BY box_container_type desc
        </where>
    </select>

    <select id="selectNotFullBoxList" resultType="com.tgvs.wms.business.modules.container.entity.Container">
        select  *
        from wms_box
        <where>
            box_empty=0
            <if test="boxContainerType != null and boxContainerType != ''">
                AND box_container_type &lt;= #{boxContainerType}
            </if>
        </where>
        AND box_empty_status &lt; box_container_type
        ORDER BY box_container_type desc,box_empty_status DESC
    </select>

    <!-- 根据区域查询空容器列表 (通过 JOIN shelf 表实现) -->
    <!-- SQL 已根据 Container 和 Shelf 实体类进行修正 -->
    <select id="selectEmptyContainersByZone" resultType="com.tgvs.wms.business.modules.container.entity.Container">
        SELECT
            c.id,
            c.material_type,
            c.box_no,
            c.contract_no,
            c.box_type,
            c.box_container_type,
            c.box_empty_status,
            c.box_empty,       
            c.create_by,
            c.create_time,
            c.update_by,
            c.update_time,
            c.delete_flag         
        FROM
            wms_box c            
        INNER JOIN
            wms_box_shelf s       
                ON c.box_no = s.box_no  
        WHERE
            c.box_empty = 0             
            AND c.delete_flag = 0        
            AND s.area = #{zone}  
            ORDER BY c.box_container_type desc
            limit 1 
    </select>
    <!-- 根据料箱号查询容器列表 (通过 JOIN shelf 表实现) -->
    <!-- SQL 已根据 Container 和 Shelf 实体类进行修正 -->
    <select id="selectContainerShelfByBoxNo" resultType="com.tgvs.wms.business.modules.container.entity.Container">
        SELECT
            c.id,
            c.material_type,
            c.box_no,
            c.contract_no,
            c.box_type,
            c.box_container_type,
            c.box_empty_status,
            c.box_empty,
            c.create_by,
            c.create_time,
            c.update_by,
            c.update_time,
            c.delete_flag
        FROM
            wms_box c
                INNER JOIN
            wms_box_shelf s
            ON c.box_no = s.box_no
        WHERE
            c.box_no = #{boxNo}
          AND c.delete_flag = 0
        ORDER BY c.box_container_type desc
            limit 1
    </select>
    <!-- 根据多种条件查询容器类型列表 -->
    <select id="selectContainerTypeList" resultType="com.tgvs.wms.business.modules.container.entity.Container">
        SELECT
            c.id,
            c.material_type,
            c.box_no,
            c.contract_no,
            c.box_type,
            c.box_container_type,
            c.box_empty_status,
            c.box_empty,
            c.create_by,
            c.create_time,
            c.update_by,
            c.update_time,
            c.delete_flag
        FROM wms_box c
        <where>
            c.delete_flag = 0
            <if test="boxType != null">
                AND c.box_type = #{boxType}
            </if>
            <if test="materialType != null">
                AND c.material_type = #{materialType}
            </if>
            <if test="boxEmpty != null">
                AND c.box_empty = #{boxEmpty}
            </if>
            <if test="boxContainerType != null">
                AND c.box_container_type = #{boxContainerType}
            </if>
            <if test="contractNo != null and contractNo != ''">
                AND c.contract_no = #{contractNo}
            </if>
            <if test="boxNo != null and boxNo != ''">
                AND c.box_no LIKE CONCAT('%', #{boxNo}, '%')
            </if>
            <if test="hasShelf != null and hasShelf == true">
                AND EXISTS (
                    SELECT 1 FROM wms_box_shelf s 
                    WHERE s.box_no = c.box_no 
                        AND s.delete_flag = 0 
                        AND s.locked = 0 
                        AND s.state = 0
                )
            </if>
            <if test="hasShelf != null and hasShelf == false">
                AND NOT EXISTS (
                    SELECT 1 FROM wms_box_shelf s 
                    WHERE s.box_no = c.box_no 
                        AND s.delete_flag = 0
                )
            </if>
            <if test="area != null and area != ''">
                AND EXISTS (
                    SELECT 1 FROM wms_box_shelf s 
                    WHERE s.box_no = c.box_no 
                        AND s.area = #{area}
                        AND s.delete_flag = 0 
                        AND s.locked = 0 
                        AND s.state = 0
                )
            </if>
        </where>
        ORDER BY 
            c.box_type ASC,
            c.box_container_type ASC,
            c.create_time DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 检查是否有可用的空容器数量（辅料用） -->
    <select id="checkAvailableEmptyContainersCount" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT c.id)
        FROM wms_box c
        INNER JOIN wms_box_shelf s ON c.box_no = s.box_no
        WHERE c.delete_flag = 0
          AND c.box_type = #{containerTypeValue}
          AND c.material_type != 2
          AND c.box_empty = 0
          AND s.delete_flag = 0
          AND s.locked = 0
          AND s.state = 1
    </select>

    <!-- 查询可用的空容器列表（辅料用） -->
    <select id="selectAvailableEmptyContainers" resultType="com.tgvs.wms.business.modules.container.entity.Container">
        SELECT 
            c.id,
            c.material_type,
            c.box_no,
            c.contract_no,
            c.box_type,
            c.box_container_type,
            c.box_empty_status,
            c.box_empty,
            c.create_by,
            c.create_time,
            c.update_by,
            c.update_time,
            c.delete_flag,
            c.version,
            s.area
        FROM wms_box c
        INNER JOIN wms_box_shelf s ON c.box_no = s.box_no
        LEFT JOIN  wms_box_item m on c.box_no=m.box_no
        WHERE c.delete_flag = 0
          AND c.box_type = #{containerTypeValue}
          AND c.material_type != 2
          AND c.box_empty = 0
          AND s.delete_flag = 0
          AND s.locked = 0
          AND (s.state = 0 or s.state=1)
          AND m.grid_volume=0
        GROUP BY c.id, c.material_type, c.box_no, c.contract_no, c.box_type, 
                 c.box_container_type, c.box_empty_status, c.box_empty, 
                 c.create_by, c.create_time, c.update_by, c.update_time, 
                 c.delete_flag, c.version, s.area
        ORDER BY
            s.area ASC,
            c.box_container_type DESC,
            c.create_time ASC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 查询可用的空料架列表（机物料用） -->
    <select id="selectEmptyTray" resultType="com.tgvs.wms.business.modules.container.entity.Container">
        SELECT * FROM wms_box AS A
        LEFT JOIN wms_box_shelf AS B ON A.box_no=B.box_no
        WHERE A.material_type=0
          AND B.box_type=2
          AND B.locked=0
          AND B.state=1
    </select>

</mapper>
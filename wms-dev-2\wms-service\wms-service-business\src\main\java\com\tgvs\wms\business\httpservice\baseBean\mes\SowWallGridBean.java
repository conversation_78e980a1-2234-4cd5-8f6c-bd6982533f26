package com.tgvs.wms.business.httpservice.baseBean.mes;


import com.alibaba.fastjson.annotation.JSONField;

public class SowWallGridBean {
    @JSONField(name = "GridNo")
    private String GridNo;

    public String toString() {
        return "SowWallGridBean(GridNo=" + getGridNo() + ")";
    }

    public int hashCode() {
        int PRIME = 59,
        result = 1;
        Object $GridNo = getGridNo();
        return result * 59 + (($GridNo == null) ? 43 : $GridNo.hashCode());
    }

    protected boolean canEqual(Object other) {
        return other instanceof SowWallGridBean;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof SowWallGridBean))
            return false;
        SowWallGridBean other = (SowWallGridBean)o;
        if (!other.canEqual(this))
            return false;
        Object this$GridNo = getGridNo(), other$GridNo = other.getGridNo();
        return !((this$GridNo == null) ? (other$GridNo != null) : !this$GridNo.equals(other$GridNo));
    }

    public void setGridNo(String GridNo) {
        this.GridNo = GridNo;
    }

    public String getGridNo() {
        return this.GridNo;
    }

    public SowWallGridBean(String gridNo) {
        setGridNo(gridNo);
    }
}

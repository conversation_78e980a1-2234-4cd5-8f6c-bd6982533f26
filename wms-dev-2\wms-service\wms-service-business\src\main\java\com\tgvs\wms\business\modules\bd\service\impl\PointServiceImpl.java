package com.tgvs.wms.business.modules.bd.service.impl;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tgvs.wms.base.service.impl.BaseServiceImpl;
import com.tgvs.wms.business.modules.bd.entity.Point;
import com.tgvs.wms.business.modules.bd.mapper.PointMapper;
import com.tgvs.wms.business.modules.bd.service.IPointService;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class PointServiceImpl extends BaseServiceImpl<PointMapper, Point> implements IPointService {

    public Point getPointByNo(String pointno) {
        log.info("pointno====456789==:" + pointno);
        return ((PointMapper)this.baseMapper).getPointByNo(pointno);
    }
}

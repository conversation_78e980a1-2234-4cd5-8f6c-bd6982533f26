package com.tgvs.wms.business.modules.auxiliaryInventory.Controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tgvs.wms.business.modules.auxiliaryInventory.entity.WmsAuxiliaryDifferenceSource;
import com.tgvs.wms.business.modules.auxiliaryInventory.service.IWmsAuxiliaryDifferenceSourceService;
import com.tgvs.wms.common.annotation.AutoLog;
import com.tgvs.wms.common.constant.Result;
import com.tgvs.wms.common.core.domain.QueryModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 辅料差异数据源 Controller
 * 
 * <AUTHOR> Generated
 * @date 2025-01-24
 */
@Api(tags = "辅料差异数据源管理")
@RestController
@RequestMapping("/auxiliary/differenceSource")
@Slf4j
public class WmsAuxiliaryDifferenceSourceController {

    @Autowired
    private IWmsAuxiliaryDifferenceSourceService differenceSourceService;

    /**
     * 分页查询差异数据源列表
     */
    @AutoLog("辅料差异数据源-分页列表查询")
    @ApiOperation(value = "辅料差异数据源-分页列表查询", notes = "辅料差异数据源-分页列表查询")
    @RequiresPermissions("auxiliary:differenceSource:list")
    @PostMapping("/list")
    public Result<?> queryPageList(@RequestBody(required = false) QueryModel queryModel) {
        try {
            IPage<WmsAuxiliaryDifferenceSource> pageList = differenceSourceService.pageDifferenceSourceList(queryModel);
            Result<List<WmsAuxiliaryDifferenceSource>> result = Result.ok(pageList.getRecords());
            result.setTotal(pageList.getTotal());
            return result;
        } catch (Exception e) {
            log.error("分页查询差异数据源列表异常", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 查询待处理的差异数据列表(用于盘点单创建)
     */
    @AutoLog("辅料差异数据源-待处理列表查询")
    @ApiOperation(value = "辅料差异数据源-待处理列表查询", notes = "查询待处理的差异数据列表，用于差异盘点单创建")
    @PostMapping("/pendingList")
    public Result<?> queryPendingList() {
        try {
            List<WmsAuxiliaryDifferenceSource> pendingList = differenceSourceService.listPendingDifference();
            return Result.ok(pendingList);
        } catch (Exception e) {
            log.error("查询待处理差异数据列表异常", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取差异数据统计
     */
    @AutoLog("辅料差异数据源-统计查询")
    @ApiOperation(value = "辅料差异数据源-统计查询", notes = "获取差异数据统计信息")
    @GetMapping("/statistics")
    public Result<?> getDifferenceStatistics() {
        try {
            Map<String, Object> statistics = differenceSourceService.getDifferenceStatistics();
            return Result.ok(statistics);
        } catch (Exception e) {
            log.error("获取差异数据统计异常", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 添加差异数据源
     */
    @AutoLog("辅料差异数据源-添加")
    @ApiOperation(value = "辅料差异数据源-添加", notes = "辅料差异数据源-添加")
    @RequiresPermissions("auxiliary:differenceSource:add")
    @PostMapping("/add")
    public Result<?> add(@RequestBody WmsAuxiliaryDifferenceSource differenceSource) {
        try {
            differenceSourceService.save(differenceSource);
            return Result.ok("添加成功！");
        } catch (Exception e) {
            log.error("添加差异数据源异常", e);
            return Result.error("添加失败：" + e.getMessage());
        }
    }

    /**
     * 编辑差异数据源
     */
    @AutoLog("辅料差异数据源-编辑")
    @ApiOperation(value = "辅料差异数据源-编辑", notes = "辅料差异数据源-编辑")
    @RequiresPermissions("auxiliary:differenceSource:edit")
    @PostMapping("/edit")
    public Result<?> edit(@RequestBody WmsAuxiliaryDifferenceSource differenceSource) {
        try {
            differenceSourceService.updateById(differenceSource);
            return Result.ok("编辑成功！");
        } catch (Exception e) {
            log.error("编辑差异数据源异常", e);
            return Result.error("编辑失败：" + e.getMessage());
        }
    }

    /**
     * 通过id删除差异数据源
     */
    @AutoLog("辅料差异数据源-通过id删除")
    @ApiOperation(value = "辅料差异数据源-通过id删除", notes = "辅料差异数据源-通过id删除")
    @RequiresPermissions("auxiliary:differenceSource:delete")
    @PostMapping("/delete")
    public Result<?> delete(@RequestBody String id) {
        try {
            differenceSourceService.removeById(id);
            return Result.ok("删除成功!");
        } catch (Exception e) {
            log.error("删除差异数据源异常", e);
            return Result.error("删除失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除差异数据源
     */
    @AutoLog("辅料差异数据源-批量删除")
    @ApiOperation(value = "辅料差异数据源-批量删除", notes = "辅料差异数据源-批量删除")
    @RequiresPermissions("auxiliary:differenceSource:deleteBatch")
    @PostMapping("/deleteBatch")
    public Result<?> deleteBatch(@RequestBody String ids) {
        try {
            differenceSourceService.removeByIds(Arrays.asList(ids.split(",")));
            return Result.ok("批量删除成功!");
        } catch (Exception e) {
            log.error("批量删除差异数据源异常", e);
            return Result.error("批量删除失败：" + e.getMessage());
        }
    }

    /**
     * 通过id查询差异数据源
     */
    @AutoLog("辅料差异数据源-通过id查询")
    @ApiOperation(value = "辅料差异数据源-通过id查询", notes = "辅料差异数据源-通过id查询")
    @PostMapping("/queryById")
    public Result<?> queryById(@RequestBody String id) {
        try {
            WmsAuxiliaryDifferenceSource differenceSource = differenceSourceService.getById(id);
            if (differenceSource == null) {
                return Result.error("未找到对应数据");
            }
            return Result.ok(differenceSource);
        } catch (Exception e) {
            log.error("通过id查询差异数据源异常", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 批量更新差异数据状态
     */
    @AutoLog("辅料差异数据源-批量更新状态")
    @ApiOperation(value = "辅料差异数据源-批量更新状态", notes = "批量更新差异数据状态，用于盘点流程")
    @RequiresPermissions("auxiliary:differenceSource:updateStatus")
    @PostMapping("/batchUpdateStatus")
    public Result<?> batchUpdateStatus(@RequestBody Map<String, Object> params) {
        try {
            @SuppressWarnings("unchecked")
            List<String> ids = (List<String>) params.get("ids");
            String status = (String) params.get("status");
            String updateBy = (String) params.get("updateBy");
            
            if (ids == null || ids.isEmpty()) {
                return Result.error("请选择要更新的数据");
            }
            if (status == null || status.trim().isEmpty()) {
                return Result.error("请指定要更新的状态");
            }
            
            boolean result = differenceSourceService.batchUpdateStatus(ids, status, updateBy);
            return result ? Result.ok("状态更新成功") : Result.error("状态更新失败");
            
        } catch (Exception e) {
            log.error("批量更新差异数据状态异常", e);
            return Result.error("状态更新失败：" + e.getMessage());
        }
    }

    /**
     * 根据ID列表获取差异数据(用于创建盘点单)
     */
    @AutoLog("辅料差异数据源-根据ID列表查询")
    @ApiOperation(value = "辅料差异数据源-根据ID列表查询", notes = "根据ID列表获取差异数据，用于创建盘点单")
    @PostMapping("/queryByIds")
    public Result<?> queryByIds(@RequestBody List<String> ids) {
        try {
            if (ids == null || ids.isEmpty()) {
                return Result.error("请提供要查询的ID列表");
            }
            
            List<WmsAuxiliaryDifferenceSource> differenceList = differenceSourceService.listByIds(ids);
            return Result.ok(differenceList);
            
        } catch (Exception e) {
            log.error("根据ID列表查询差异数据异常", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 批量导入差异数据(第三方系统接口)
     */
    @AutoLog("辅料差异数据源-批量导入")
    @ApiOperation(value = "辅料差异数据源-批量导入", notes = "第三方系统批量导入差异数据")
    @RequiresPermissions("auxiliary:differenceSource:import")
    @PostMapping("/batchImport")
    public Result<?> batchImport(@RequestBody Map<String, Object> params) {
        try {
            @SuppressWarnings("unchecked")
            List<WmsAuxiliaryDifferenceSource> differenceList = (List<WmsAuxiliaryDifferenceSource>) params.get("differenceList");
            String sourceBatch = (String) params.get("sourceBatch");
            String sourceSystem = (String) params.get("sourceSystem");
            
            if (differenceList == null || differenceList.isEmpty()) {
                return Result.error("导入数据不能为空");
            }
            if (sourceBatch == null || sourceBatch.trim().isEmpty()) {
                return Result.error("请指定导入批次号");
            }
            if (sourceSystem == null || sourceSystem.trim().isEmpty()) {
                return Result.error("请指定来源系统");
            }
            
            boolean result = differenceSourceService.batchImportDifferenceData(differenceList, sourceBatch, sourceSystem);
            return result ? Result.ok("数据导入成功，共导入 " + differenceList.size() + " 条记录") 
                         : Result.error("数据导入失败");
            
        } catch (Exception e) {
            log.error("批量导入差异数据异常", e);
            return Result.error("数据导入失败：" + e.getMessage());
        }
    }
}
package com.tgvs.wms.business.modules.task.mapper;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tgvs.wms.business.modules.task.entity.TaskBox;
import com.tgvs.wms.business.modules.task.entity.TaskBoxChart;

public interface TaskBoxMapper extends BaseMapper<TaskBox> {

    void updateTreeNodeStatus(@Param("id") String paramString1, @Param("status") String paramString2);

    List<TaskBoxChart> selectListCount();

    List<TaskBoxChart> selectListCountByDate(@Param("dayStart") Date paramDate1, @Param("dayEnd") Date paramDate2);
}

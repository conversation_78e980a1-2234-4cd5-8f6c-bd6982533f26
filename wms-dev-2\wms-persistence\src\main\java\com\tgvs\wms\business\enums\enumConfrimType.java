package com.tgvs.wms.business.enums;

import com.tgvs.wms.common.util.oConvertUtils;

public enum enumConfrimType {
    unknow(Integer.valueOf(-100), "unknow", "未定义代码"),
    apiexception(Integer.valueOf(-2), "apiexception", "调用API时发生异常"),
    error(Integer.valueOf(-1), "error", "控制器返回错误信息"),
    confrimbutton(Integer.valueOf(0), "confrimbutton", "电子标签拍灯返回"),
    confrimscreen(Integer.valueOf(1), "confrimscreen", "显示屏拍灯返回"),
    confrimscaner(Integer.valueOf(2), "confrimscaner", "扫描枪返回返回"),
    sent(Integer.valueOf(3), "sent", "发送命令结束返回"),
    connected(Integer.valueOf(4), "connected", "完成控制器连接返回"),
    closed(Integer.valueOf(5), "closed", "关闭控制器控制器返回"),
    apicreating(Integer.valueOf(10), "apicreating", "调用API_开始创建控制器返回"),
    apideleting(Integer.valueOf(11), "apideleting", "调用API_开始删除控制器返回"),
    apiconnecting(Integer.valueOf(12), "apiconnecting", "调用API_开始连接控制器返回"),
    apidisconnecting(Integer.valueOf(13), "apidisconnecting", "调用API_开始断开控制器返回"),
    apiisconnected(Integer.valueOf(14), "apiisconnected", "调用API_判断控制器是否处于连接状态"),
    apisendA(Integer.valueOf(30), "apisendA", "调用API_开始发送维护命令"),
    apisendZ(Integer.valueOf(31), "apisendZ", "调用API_开始发送初始化命令"),
    apiclosing(Integer.valueOf(32), "apiclosing", "调用API_开始关闭硬件"),
    apiopeningscan(Integer.valueOf(33), "apiopeningscan", "调用API_开始打开扫描枪"),
    apiclosingscan(Integer.valueOf(34), "apiclosingscan", "调用API_开始关闭扫描枪"),
    apisendinglight(Integer.valueOf(35), "apisendinglight", "调用API_开始发送电子标签/显示屏/信号灯命令"),
    apisendingpps(Integer.valueOf(39), "apisendingpps", "调用API_开始发送PPS亮灯命令");

    private Integer value;

    private String code;

    private String text;

    enumConfrimType(Integer value, String code, String text) {
        this.value = value;
        this.code = code;
        this.text = text;
    }

    public static enumConfrimType getByValue(int value) {
        if (oConvertUtils.isEmpty(Integer.valueOf(value)))
            return null;
        for (enumConfrimType val : values()) {
            if (val.getValue().equals(Integer.valueOf(value)))
                return val;
        }
        return null;
    }

    public Integer getValue() {
        return this.value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getText() {
        return this.text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public static enumConfrimType toEnum(Integer Value) {
        for (enumConfrimType e : values()) {
            if (e.getValue().equals(Value))
                return e;
        }
        return unknow;
    }
}

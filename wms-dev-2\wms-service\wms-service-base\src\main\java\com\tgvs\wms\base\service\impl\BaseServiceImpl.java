package com.tgvs.wms.base.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tgvs.wms.base.entity.BaseEntity;
import com.tgvs.wms.base.service.BaseService;
import com.tgvs.wms.common.constant.UserConstants;
import com.tgvs.wms.common.core.domain.QueryModel;
import com.tgvs.wms.common.query.QueryGenerator;
import com.tgvs.wms.common.util.StringUtils;
import java.util.HashMap;

public class BaseServiceImpl<M extends BaseMapper<T>, T extends BaseEntity> extends ServiceImpl<M, T> implements BaseService<T> {

    protected String checkColumnUnique(T menu, String columnName, String value) {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(columnName, value);

        T info = getOne(queryWrapper);
        if (StringUtils.isNotNull(info) && StringUtils.equalsIgnoreCase(info.getId(), menu.getId())) {
            return UserConstants.COLUMN_VALUE_NOT_UNIQUE;
        }
        return UserConstants.COLUMN_VALUE_UNIQUE;
    }


    @Override
    public IPage<T> pageList(QueryModel queryModel) {
        // 处理queryModel为null的情况
        if (queryModel == null) {
            queryModel = new QueryModel();
            queryModel.setPage(1);
            queryModel.setLimit(10);
        }
        
        // 确保分页参数不为null
        if (queryModel.getPage() == null) {
            queryModel.setPage(1);
        }
        if (queryModel.getLimit() == null) {
            queryModel.setLimit(10);
        }
        
        // 安全地获取searchParams，避免空指针异常
        QueryWrapper<T> roleLambdaQueryWrapper = QueryGenerator.initQueryWrapper(
            queryModel.getSearchParams() != null ? queryModel.getSearchParams() : new HashMap<>()
        );

        //排序
        if(StringUtils.isNotEmpty(queryModel.getSortList())){
            for(QueryModel.SortModel sortModel : queryModel.getSortList()){
                if(sortModel.getSortType() ==1){
                    roleLambdaQueryWrapper.orderByAsc(sortModel.getColumn());
                }else if(sortModel.getSortType() ==2){
                    roleLambdaQueryWrapper.orderByDesc(sortModel.getColumn());
                }
            }
        }

        Page<T> result = new Page<>(queryModel.getPage() , queryModel.getLimit());

        return  baseMapper.selectPage(result , roleLambdaQueryWrapper);
    }
}

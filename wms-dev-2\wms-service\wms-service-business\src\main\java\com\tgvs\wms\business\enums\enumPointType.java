package com.tgvs.wms.business.enums;

import com.tgvs.wms.common.util.oConvertUtils;

public enum enumPointType {
    inbound(Integer.valueOf(1), "inbound", "入库站台"),
    outbound(Integer.valueOf(2), "outbound", "出库站台"),
    inbounddecide(Integer.valueOf(3), "inbounddecide", "入库决策点"),
    decide(Integer.valueOf(4), "decide", "决策点"),
    operateinto(Integer.valueOf(5), "operateinto", "操作入口"),
    scan(Integer.valueOf(6), "scan", "验证条码"),
    outbounddecide(Integer.valueOf(8), "outbounddecide", "出库决策点"),
    liftin(Integer.valueOf(10), "liftin", "换楼层入口"),
    liftout(Integer.valueOf(11), "liftout", "换楼层出库"),
    songbu(Integer.valueOf(15), "songbu", "松布"),
    songbupickup(Integer.valueOf(16), "songbupickup", "松布布匹抓取位"),
    songbuputdwon(Integer.valueOf(17), "songbuputdwon", "松布空板放置位"),
    cutting(Integer.valueOf(20), "cutting", "裁剪"),
    spacesite(Integer.valueOf(25), "spacesite", "搬运"),
    cache(Integer.valueOf(30), "cache", "缓存"),
    surplus(Integer.valueOf(35), "surplus", "余布处理节点"),
    packaging(Integer.valueOf(40), "packaging", "装箱"),
    matching(Integer.valueOf(45), "matching", "对包"),
    operate(Integer.valueOf(50), "operate", "操作"),
    emptyline(Integer.valueOf(55), "emptyline", "空箱补给线"),
    picking(Integer.valueOf(60), "picking", "分拣"),
    fault(Integer.valueOf(255), "fault", "异常处理点");

    private Integer value;

    private String code;

    private String text;

    enumPointType(Integer value, String code, String text) {
        this.value = value;
        this.code = code;
        this.text = text;
    }

    public static enumPointType getByValue(int value) {
        if (oConvertUtils.isEmpty(Integer.valueOf(value)))
            return null;
        for (enumPointType val : values()) {
            if (val.getValue().equals(Integer.valueOf(value)))
                return val;
        }
        return null;
    }

    public Integer getValue() {
        return this.value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getText() {
        return this.text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public static enumPointType toEnum(Integer Value) {
        for (enumPointType e : values()) {
            if (e.getValue().equals(Value))
                return e;
        }
        return null;
    }
}

package com.tgvs.wms.business.modules.bd.entity;

import java.io.Serializable;
import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.tgvs.wms.base.entity.BaseEntity;
import com.tgvs.wms.common.annotation.Dict;
import com.tgvs.wms.common.annotation.Excel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("wms_point")
@ApiModel(value = "wms_point对象", description = "设备节点管理")
public class Point extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键")
    private String id;

    @Excel(name = "节点编号", width = 15.0D)
    @ApiModelProperty("节点编号")
    private String pointNo;

    @Excel(name = "节点名称", width = 15.0D)
    @ApiModelProperty("节点名称")
    private String pointName;

    @Excel(name = "mes系统编码", width = 15.0D)
    @ApiModelProperty("mes系统编码")
    private String mesCode;

    @Excel(name = "机械手系统编码", width = 15.0D)
    @ApiModelProperty("机械手系统编码")
    private String rmsCode;

    @Excel(name = "所属系统", width = 15.0D, dictTable = "wms_commncation_config", dicText = "sysname", dicCode = "sysid")
    @Dict(dictTable = "wms_commncation_config", dicText = "sysname", dicCode = "sysid")
    @ApiModelProperty("所属系统")
    private String sysid;

    @Excel(name = "关联设备编号", width = 15.0D, dictTable = "wms_commncation_device", dicText = "deviceName", dicCode = "deviceNo")
    @Dict(dictTable = "wms_commncation_device", dicText = "deviceName", dicCode = "deviceNo")
    @ApiModelProperty("关联设备编号")
    private String deviceno;

    @ApiModelProperty("设备类型")
    private Integer devicType;

    @Excel(name = "节点类型", width = 15.0D, dictTable = "wms_point_type", dicText = "text", dicCode = "value")
    @Dict(enumType = "PointType", dicText = "text", dicCode = "value")
    @ApiModelProperty("节点类型")
    private Integer pointType;

    @Excel(name = "节点方向", width = 15.0D)
    @ApiModelProperty("节点方向")
    private Integer pointDirection;

    @Excel(name = "所在区域", width = 15.0D, dictTable = "wms_base_area", dicText = "text", dicCode = "code")
    @Dict(dictTable = "wms_base_area", dicText = "text", dicCode = "code")
    @ApiModelProperty("所在区域")
    private String area;

    @Excel(name = "楼层", width = 15.0D)
    @ApiModelProperty("楼层")
    private Integer floor;

    @Excel(name = "设备状态", width = 15.0D, dicCode = "site_status")
    @Dict(dicCode = "site_status")
    @ApiModelProperty("设备状态")
    private Integer state;

    @Excel(name = "占用情况", width = 15.0D)
    @ApiModelProperty("占用情况")
    private Integer occupy;

    @Excel(name = "锁定", width = 15.0D, dicCode = "locked_status")
    @Dict(dicCode = "locked_status")
    @ApiModelProperty("锁定")
    private Integer locked;

    @Excel(name = "容器号", width = 15.0D)
    @ApiModelProperty("容器号")
    private String boxNo;

    @Excel(name = "在线数量", width = 15.0D)
    @ApiModelProperty("在线数量")
    private Integer onlineSum;

    @Excel(name = "描述", width = 15.0D)
    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("创建人")
    private String createBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("更新时间")
    private Date updateTime;

    @Excel(name = "删除状态", width = 15.0D)
    @ApiModelProperty("删除状态")
    private Integer delFlag;
}

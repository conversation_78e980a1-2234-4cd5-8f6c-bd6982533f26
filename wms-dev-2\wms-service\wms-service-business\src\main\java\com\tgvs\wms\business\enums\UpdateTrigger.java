package com.tgvs.wms.business.enums;

import lombok.Getter;

/**
 * 容器状态更新触发器枚举
 * 用于标识容器状态更新的触发场景
 */
@Getter
public enum UpdateTrigger {
    
    /**
     * 预装箱创建 - 在创建预装箱记录时触发
     */
    PREBOX_CREATE("PREBOX_CREATE", "预装箱创建", "在创建预装箱记录时触发容器状态更新"),
    
    /**
     * 入库完成 - 在入库操作完成时触发
     */
    INBOUND_COMPLETE("INBOUND_COMPLETE", "入库完成", "在入库操作完成时触发容器状态更新"),
    
    /**
     * 绑定入库 - 在绑定入库操作时触发
     */
    BINDING_INBOUND("BINDING_INBOUND", "绑定入库", "在绑定入库操作时触发容器状态更新"),
    
    /**
     * 预装箱阶段 - 在预装箱阶段触发
     */
    PREBOX_STAGE("PREBOX_STAGE", "预装箱阶段", "在预装箱阶段触发容器状态更新"),
    
    /**
     * 出库完成 - 在出库操作完成时触发
     */
    OUTBOUND_COMPLETE("OUTBOUND_COMPLETE", "出库完成", "在出库操作完成时触发容器状态更新"),

    /**
     * 剩余物料入库 - 在剩余物料入库时触发
     */
    REMAINING_INBOUND("REMAINING_INBOUND", "剩余物料入库", "在剩余物料入库时触发容器状态更新"),

    /**
     * 托盘拆零 - 在托盘拆零操作时触发
     */
    PALLET_BREAK("PALLET_BREAK", "托盘拆零", "在托盘拆零操作时触发容器状态更新"),

    /**
     * 容器清空 - 在容器完全清空时触发状态重置
     */
    CONTAINER_CLEAR("CONTAINER_CLEAR", "容器清空", "在容器完全清空时触发状态重置"),

    /**
     * 取消预出库 - 在取消预出库时触发状态回滚
     */
    CANCEL_OUTBOUND("CANCEL_OUTBOUND", "取消预出库", "在取消预出库时触发容器状态回滚"),

    /**
     * 手动更新 - 手动触发的状态更新
     */
    MANUAL_UPDATE("MANUAL_UPDATE", "手动更新", "手动触发的容器状态更新");
    
    /**
     * 触发器代码
     */
    private final String code;
    
    /**
     * 触发器名称
     */
    private final String name;
    
    /**
     * 触发器描述
     */
    private final String description;
    
    UpdateTrigger(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }
    
    /**
     * 根据代码获取枚举
     * @param code 触发器代码
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static UpdateTrigger getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (UpdateTrigger trigger : values()) {
            if (trigger.getCode().equals(code)) {
                return trigger;
            }
        }
        return null;
    }
    
    /**
     * 判断是否为预装箱相关触发器
     */
    public boolean isPreboxRelated() {
        return this == PREBOX_CREATE || this == PREBOX_STAGE;
    }
    
    /**
     * 判断是否为入库相关触发器
     */
    public boolean isInboundRelated() {
        return this == INBOUND_COMPLETE || this == BINDING_INBOUND || this == REMAINING_INBOUND;
    }

    /**
     * 判断是否为出库相关触发器
     */
    public boolean isOutboundRelated() {
        return this == OUTBOUND_COMPLETE || this == REMAINING_INBOUND ||
               this == PALLET_BREAK || this == CANCEL_OUTBOUND;
    }

    /**
     * 判断是否为容器清理相关触发器
     */
    public boolean isContainerClearRelated() {
        return this == CONTAINER_CLEAR || this == OUTBOUND_COMPLETE;
    }
}

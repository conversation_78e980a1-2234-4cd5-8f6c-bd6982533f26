package com.tgvs.wms.business.httpservice.baseBean.dps;


import com.alibaba.fastjson.annotation.JSONField;

public class HeartbeatBean {
    @JSO<PERSON>ield(name = "MsgType")
    private int MsgType;

    @J<PERSON><PERSON>ield(name = "Key")
    private int Key;

    @<PERSON><PERSON><PERSON>ield(name = "Msg")
    private String Msg;

    @J<PERSON><PERSON>ield(name = "ControllerID")
    private int ControllerID;

    @JSONField(name = "IPAddress")
    private String IPAddress;

    @JSONField(name = "Port")
    private int Port;

    public void setMsgType(int MsgType) {
        this.MsgType = MsgType;
    }

    public void setKey(int Key) {
        this.Key = Key;
    }

    public void setMsg(String Msg) {
        this.Msg = Msg;
    }

    public void setControllerID(int ControllerID) {
        this.ControllerID = ControllerID;
    }

    public void setIPAddress(String IPAddress) {
        this.IPAddress = IPAddress;
    }

    public void setPort(int Port) {
        this.Port = Port;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof HeartbeatBean))
            return false;
        HeartbeatBean other = (HeartbeatBean)o;
        if (!other.canEqual(this))
            return false;
        if (getMsgType() != other.getMsgType())
            return false;
        if (getKey() != other.getKey())
            return false;
        Object this$Msg = getMsg(), other$Msg = other.getMsg();
        if ((this$Msg == null) ? (other$Msg != null) : !this$Msg.equals(other$Msg))
            return false;
        if (getControllerID() != other.getControllerID())
            return false;
        Object this$IPAddress = getIPAddress(), other$IPAddress = other.getIPAddress();
        return ((this$IPAddress == null) ? (other$IPAddress != null) : !this$IPAddress.equals(other$IPAddress)) ? false : (!(getPort() != other.getPort()));
    }

    protected boolean canEqual(Object other) {
        return other instanceof HeartbeatBean;
    }

    public int hashCode() {
        int PRIME = 59,
        result = 1;
        result = result * 59 + getMsgType();
        result = result * 59 + getKey();
        Object $Msg = getMsg();
        result = result * 59 + (($Msg == null) ? 43 : $Msg.hashCode());
        result = result * 59 + getControllerID();
        Object $IPAddress = getIPAddress();
        result = result * 59 + (($IPAddress == null) ? 43 : $IPAddress.hashCode());
        return result * 59 + getPort();
    }

    public String toString() {
        return "HeartbeatBean(MsgType=" + getMsgType() + ", Key=" + getKey() + ", Msg=" + getMsg() + ", ControllerID=" + getControllerID() + ", IPAddress=" + getIPAddress() + ", Port=" + getPort() + ")";
    }

    public int getMsgType() {
        return this.MsgType;
    }

    public int getKey() {
        return this.Key;
    }

    public String getMsg() {
        return this.Msg;
    }

    public int getControllerID() {
        return this.ControllerID;
    }

    public String getIPAddress() {
        return this.IPAddress;
    }

    public int getPort() {
        return this.Port;
    }
}

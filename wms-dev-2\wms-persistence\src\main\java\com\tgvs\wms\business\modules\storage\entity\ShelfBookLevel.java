package com.tgvs.wms.business.modules.storage.entity;

import java.io.Serializable;

import org.jeecgframework.poi.excel.annotation.Excel;

import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TableName("wms_box_shelf")
@ApiModel(value = "wms_box_shelf对象", description = "货位管理")
@Data
public class ShelfBookLevel implements Serializable {
    private static final long serialVersionUID = 1L;

    @Excel(name = "区域", width = 15.0D)
    @ApiModelProperty("区域")
    private String area;

    @Excel(name = "层位", width = 15.0D)
    @ApiModelProperty("层位")
    private Integer level;

    @Excel(name = "数量", width = 15.0D)
    @ApiModelProperty("数量")
    private Integer count;
}

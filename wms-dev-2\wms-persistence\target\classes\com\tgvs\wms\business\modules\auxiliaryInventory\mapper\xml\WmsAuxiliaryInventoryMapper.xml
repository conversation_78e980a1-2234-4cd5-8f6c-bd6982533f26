<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tgvs.wms.business.modules.auxiliaryInventory.mapper.WmsAuxiliaryInventoryMapper">

    <!-- 通用结果映射 -->
    <resultMap id="BaseResultMap" type="com.tgvs.wms.business.modules.auxiliaryInventory.entity.WmsAuxiliaryInventory">
        <id column="id" property="id"/>
        <result column="inventory_no" property="inventoryNo"/>
        <result column="inventory_name" property="inventoryName"/>
        <result column="inventory_type" property="inventoryType"/>
        <result column="warehouse_code" property="warehouseCode"/>
        <result column="warehouse_name" property="warehouseName"/>
        <result column="status" property="status"/>
        <result column="plan_start_time" property="planStartTime"/>
        <result column="plan_end_time" property="planEndTime"/>
        <result column="actual_start_time" property="actualStartTime"/>
        <result column="actual_end_time" property="actualEndTime"/>
        <result column="total_items" property="totalItems"/>
        <result column="counted_items" property="countedItems"/>
        <result column="diff_items" property="diffItems"/>
        <result column="remark" property="remark"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

    <!-- 分页查询盘点单列表 -->
    <select id="selectInventoryPage" resultMap="BaseResultMap">
        SELECT 
            id, inventory_no, inventory_name, inventory_type, warehouse_code, warehouse_name,
            status, plan_start_time, plan_end_time, actual_start_time, actual_end_time,
            total_items, counted_items, diff_items, remark, create_by, create_time, update_by, update_time
        FROM wms_auxiliary_inventory
        WHERE del_flag = 0
        <if test="inventory.inventoryNo != null and inventory.inventoryNo != ''">
            AND inventory_no LIKE CONCAT('%', #{inventory.inventoryNo}, '%')
        </if>
        <if test="inventory.inventoryName != null and inventory.inventoryName != ''">
            AND inventory_name LIKE CONCAT('%', #{inventory.inventoryName}, '%')
        </if>
        <if test="inventory.status != null and inventory.status != ''">
            AND status = #{inventory.status}
        </if>
        <if test="inventory.inventoryType != null and inventory.inventoryType != ''">
            AND inventory_type = #{inventory.inventoryType}
        </if>
        <if test="inventory.warehouseCode != null and inventory.warehouseCode != ''">
            AND warehouse_code = #{inventory.warehouseCode}
        </if>
        <if test="inventory.auxiliaryCode != null and inventory.auxiliaryCode != ''">
            AND auxiliary_code = #{inventory.auxiliaryCode}
        </if>
        <if test="inventory.createTime != null">
            AND DATE(create_time) >= DATE(#{inventory.createTime})
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 根据盘点单号查询 -->
    <select id="selectByInventoryNo" resultMap="BaseResultMap">
        SELECT 
            id, inventory_no, inventory_name, inventory_type, warehouse_code, warehouse_name,
            auxiliary_code, auxiliary_name, auxiliary_category, status, freeze_flag, freeze_time,
            unfreeze_time, plan_start_time, plan_end_time, actual_start_time, actual_end_time,
            total_items, counted_items, diff_items, total_system_qty, total_actual_qty,
            total_diff_qty, remark, create_by, create_time, update_by, update_time
        FROM wms_auxiliary_inventory
        WHERE inventory_no = #{inventoryNo} AND del_flag = 0
    </select>

    <!-- 统计状态数量 -->
    <select id="countByStatus" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM wms_auxiliary_inventory
        WHERE status = #{status} AND del_flag = 0
    </select>

</mapper>
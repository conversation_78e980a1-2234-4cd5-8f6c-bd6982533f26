
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>WMS智慧仓储管理系统</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta http-equiv="Access-Control-Allow-Origin" content="*">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <link rel="icon" type="image/png" href="images/logo.png">
    <link rel="stylesheet" href="lib/layui-v2.11.0/css/layui.css" media="all">
    <link rel="stylesheet" href="css/common.css" media="all">
    <link rel="stylesheet" href="css/font-awesome.min.css" media="all">
    <!--[if lt IE 9]>
    <script src="https://cdn.staticfile.org/html5shiv/r29/html5.min.js"></script>
    <script src="https://cdn.staticfile.org/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->
    <style>
        .main-body {top:50%;left:50%;position:absolute;-webkit-transform:translate(-50%,-50%);-moz-transform:translate(-50%,-50%);-ms-transform:translate(-50%,-50%);-o-transform:translate(-50%,-50%);transform:translate(-50%,-50%);overflow:hidden;}
        .login-main .login-bottom .center .item input {display:inline-block;width:227px;height:34px;padding:0;position:absolute;border:0;outline:0;font-size:16px;letter-spacing:0px;}
        .login-main .login-bottom .center .item .icon-1 {background:url(images/icon-login.png) no-repeat 1px 0;}
        .login-main .login-bottom .center .item .icon-2 {background:url(images/icon-login.png) no-repeat -54px 0;}
        .login-main .login-bottom .center .item .icon-3 {background:url(images/icon-login.png) no-repeat -106px 0;}
        .login-main .login-bottom .center .item .icon-4 {background:url(images/icon-login.png) no-repeat 0 -43px;position:absolute;right:-10px;cursor:pointer;}
        .login-main .login-bottom .center .item .icon-5 {background:url(images/icon-login.png) no-repeat -55px -43px;}
        .login-main .login-bottom .center .item .icon-6 {background:url(images/icon-login.png) no-repeat 0 -93px;position:absolute;right:-10px;margin-top:8px;cursor:pointer;}
        .login-main .login-bottom .tip .icon-nocheck {display:inline-block;width:10px;height:10px;border-radius:2px;border:solid 1px #9abcda;position:relative;top:2px;margin:1px 8px 1px 1px;cursor:pointer;}
        .login-main .login-bottom .tip .icon-check {margin:0 7px 0 0;width:14px;height:14px;border:none;background:url(images/icon-login.png) no-repeat -111px -48px;}
        .login-main .login-bottom .center .item .icon {display:inline-block;width:33px;height:22px;}
        .login-main .login-bottom .center .item {width:288px;height:35px;border-bottom:1px solid #dae1e6;margin-bottom:35px;}
        .login-main {width:428px;position:relative;float:left;}
        .login-main .login-top {height:100px;background-color:#148be4;border-radius:12px 12px 0 0;font-family:SourceHanSansCN-Regular;font-size:30px;font-weight:400;font-stretch:normal;letter-spacing:0;color:#fff;line-height:100px;text-align:center;overflow:hidden;-webkit-transform:rotate(0);-moz-transform:rotate(0);-ms-transform:rotate(0);-o-transform:rotate(0);transform:rotate(0);}
        .login-main .login-top .bg1 {display:inline-block;width:74px;height:74px;background:#fff;opacity:.1;border-radius:0 74px 0 0;position:absolute;left:0;top:43px;}
        .login-main .login-top .bg2 {display:inline-block;width:94px;height:94px;background:#fff;opacity:.1;border-radius:50%;position:absolute;right:-16px;top:-16px;}
        .login-main .login-bottom {width:428px;background:#fff;border-radius:0 0 12px 12px;padding-bottom:53px;}
        .login-main .login-bottom .center {width:288px;margin:0 auto;padding-top:40px;padding-bottom:15px;position:relative;}
        .login-main .login-bottom .tip {clear:both;height:16px;line-height:16px;width:288px;margin:0 auto;}
        body {background:url(images/loginbg.png) 0% 0% / cover no-repeat;position:static;font-size:12px;}
        input::-webkit-input-placeholder {color:#a6aebf;}
        input::-moz-placeholder {/* Mozilla Firefox 19+ */            color:#a6aebf;}
        input:-moz-placeholder {/* Mozilla Firefox 4 to 18 */            color:#a6aebf;}
        input:-ms-input-placeholder {/* Internet Explorer 10-11 */            color:#a6aebf;}
        input:-webkit-autofill {/* 取消Chrome记住密码的背景颜色 */            -webkit-box-shadow:0 0 0 1000px white inset !important;}
        html {height:100%;}
        .login-main .login-bottom .tip {clear:both;height:16px;line-height:16px;width:288px;margin:0 auto;}
        .login-main .login-bottom .tip .login-tip {font-family:MicrosoftYaHei;font-size:12px;font-weight:400;font-stretch:normal;letter-spacing:0;color:#9abcda;cursor:pointer;}
        .login-main .login-bottom .tip .forget-password {font-stretch:normal;letter-spacing:0;color:#1391ff;text-decoration:none;position:absolute;right:62px;}
        .login-main .login-bottom .login-btn {width:288px;height:40px;background-color:#1E9FFF;border-radius:16px;margin:2px auto 0;text-align:center;line-height:40px;color:#fff;font-size:14px;letter-spacing:0;cursor:pointer;border:none;}
        .login-main .login-bottom .center .item .validateImg {position:absolute;right:2px;cursor:pointer;width:100px;height:32px;border:1px solid #e6e6e6;}
        .footer {left:0;bottom:0;color:#fff;width:100%;position:absolute;text-align:center;line-height:30px;padding-bottom:10px;text-shadow:#000 0.1em 0.1em 0.1em;font-size:14px;z-index: -100}
        .padding-5 {padding:5px !important;}
        .footer a,.footer span {color:#fff;}
        @media screen and (max-width:428px) {.login-main {width:360px !important;}
            .login-main .login-top {width:360px !important;}
            .login-main .login-bottom {width:360px !important;}
        }
    </style>
</head>
<body>
<div class="main-body">
    <div class="login-main">
        <div class="login-top">
            <span>WMS系统登录</span>
            <span class="bg1"></span>
            <span class="bg2"></span>
        </div>
        <form id="loginForm" class="layui-form login-bottom">
            <div class="center">
                <div class="item">
                    <span class="icon icon-2" style="margin-top:5px"></span>
                    <input type="text" name="username" placeholder="请输入登录账号" maxlength="24" autocomplete="username"/>
                </div>

                <div class="item">
                    <span class="icon icon-3" style="margin-top:5px"></span>
                    <input type="password" name="password" placeholder="请输入密码" maxlength="20" autocomplete="current-password">
                    <span class="bind-password icon icon-4" style="margin-top:5px"></span>
                </div>

                <div id="validatePanel" class="item" style="width: 160px;">
                    <span class="icon icon-1" style="margin-top:5px"></span>
                    <input type="text" name="validateCode" placeholder="请输入验证码" maxlength="4" style="width: 160px;">
                    <img id="refreshCaptcha" class="validateImg" >
                </div>

            </div>
            <div class="layui-form-item" style="text-align:center; width:100%;height:100%;margin:0px;">
                <input type="button" class="login-btn" value="立即登录"/>
            </div>
        </form>
    </div>
</div>
<div class="footer">©2025金复凯 版权所有<span class="padding-5">|</span>
</div>

<script src="js/base-config.js" charset="utf-8"></script>
<script src="js/jquery.min.js" charset="utf-8"></script>
<script src="js/validate/jquery.validate.min.js" charset="utf-8"></script>
<script src="js/layui/layui.js" charset="utf-8"></script>
<script src="js/common-util.js" charset="utf-8"></script>
<script>
$(document).ready(function(){
    $(document).on('click','.login-btn',function(){
        loginClick();
    });
    $(document).on('click','.validateImg',function(){
        refreshCaptchaClick();
    });
    $(document).on('click','.bind-password',function(){
        togglePassword(this);
    });
    $(document).on('keydown','input',function(event){
        if(event.keyCode ==13){
            $(".login-btn").trigger("click");
        }
    });
    refreshCaptchaClick();
    validateRule();
});

function loginClick(){
    if(!$("#loginForm").valid()){
        return ;
    }
    $.operate.post(
        "sys/login",
        $("#loginForm").form2json(),
        function(result){
            if(result.code ==200){
                location.href = 'main.html';
            }else{
                refreshCaptchaClick();
            }
        }
    );
}
function refreshCaptchaClick(){
    var url = ctx + "captcha/captchaImage?type=" + captchaType + "&s=" + Math.random();
    $(".validateImg").attr("src", url);
}
function togglePassword(obj){
    if ($(obj).hasClass('icon-5')) {
        $(obj).removeClass('icon-5');
        $("input[name='password']").attr('type', 'password');
    } else {
        $(obj).addClass('icon-5');
        $("input[name='password']").attr('type', 'text');
    }
}
function validateRule() {
    var icon = "<i class='fa fa-times-circle'></i> ";
    $("#loginForm").validate({
        rules: {
            username: {
                required: true
            },
            password: {
                required: true
            },
            captcha:{
                required: true
            }
        },
        messages: {
            username: {
                required: icon + "请输入登录账号",
            },
            password: {
                required: icon + "请输入密码",
            },
            captcha: {
                required: icon + "请输入验证码",
            }
        }
    })
}
</script>
</body>
</html>
package com.tgvs.wms.business.modules.storage.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tgvs.wms.business.modules.storage.entity.Stock;
import com.tgvs.wms.business.modules.storage.entity.StockLevel;
import com.tgvs.wms.common.core.domain.AuxiliaryStockDto;


public interface StockMapper extends BaseMapper<Stock> {
    Stock selectBycode(@Param("code") String paramString);

    List<StockLevel> getLevelCount(@Param("state") Integer paramInteger1, @Param("locked") Integer paramInteger2, @Param("box_empty") Integer paramInteger3, @Param("orderBy") String paramString);

    /**
     * 分页查询辅料库存信息（联表查询）
     * 
     * @param page 分页对象
     * @param searchParams 查询参数
     * @return 辅料库存信息分页结果
     */
    IPage<AuxiliaryStockDto> pageAuxiliaryStockList(IPage<AuxiliaryStockDto> page, @Param("searchParams") Map<String, String> searchParams);
}
package com.tgvs.wms.business.modules.commncation.entity;

import java.io.Serializable;
import java.util.Date;

import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.tgvs.wms.base.entity.BaseEntity;
import com.tgvs.wms.common.annotation.Dict;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TableName("wms_commncation_config")
@ApiModel(value = "wms_commncation_config对象", description = "系统通讯配置")
@Data
public class CommncationConfig extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键")
    private String id;

    @Excel(name = "系统编号", width = 15.0D)
    @ApiModelProperty("系统编号")
    private String sysid;

    @Excel(name = "系统名称", width = 15.0D)
    @ApiModelProperty("系统名称")
    private String sysname;

    @Excel(name = "设备编号", width = 15.0D)
    @ApiModelProperty("设备编号")
    private String deviceno;

    @Excel(name = "IP地址", width = 15.0D)
    @ApiModelProperty("IP地址")
    private String address;

    @Excel(name = "端口", width = 15.0D)
    @ApiModelProperty("端口")
    private Integer port;

    @Excel(name = "URL地址", width = 15.0D)
    @ApiModelProperty("URL地址")
    private String url;

    @Excel(name = "序列号", width = 15.0D)
    @ApiModelProperty("序列号")
    private String license;

    @Excel(name = "用户名", width = 15.0D)
    @ApiModelProperty("用户名")
    private String username;

    @Excel(name = "密码", width = 15.0D)
    @ApiModelProperty("密码")
    private String password;

    @Excel(name = "token", width = 15.0D)
    @ApiModelProperty("token")
    private String token;

    @Excel(name = "通讯方式", width = 15.0D, dicCode = "connect_type")
    @Dict(dicCode = "connect_type")
    @ApiModelProperty("通讯方式")
    private Integer msgType;

    @Excel(name = "通讯状态", width = 15.0D, dicCode = "connect_status")
    @Dict(dicCode = "connect_status")
    @ApiModelProperty("通讯状态")
    private Integer state;

    @Excel(name = "运行状态", width = 15.0D, dicCode = "run_status")
    @Dict(dicCode = "run_status")
    @ApiModelProperty("运行状态")
    private Integer runstate;

    @Excel(name = "消息格式", width = 15.0D)
    @ApiModelProperty("消息格式")
    private String msgFormat;

    @Excel(name = "内容类型标签", width = 15.0D)
    @ApiModelProperty("内容类型标签")
    private String contentType;

    @Excel(name = "字符编码", width = 15.0D)
    @ApiModelProperty("字符编码")
    private String acceptEncoding;

    @Excel(name = "超时时间", width = 15.0D)
    @ApiModelProperty("超时时间")
    private Integer timeout;

    @Excel(name = "描述", width = 15.0D)
    @ApiModelProperty("描述")
    private String description;

    @Excel(name = "消息提示", width = 15.0D)
    @ApiModelProperty("消息提示")
    private String msg;

    @ApiModelProperty("创建人")
    private String createBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("更新时间")
    private Date updateTime;

    @Excel(name = "删除状态", width = 15.0D)
    @ApiModelProperty("删除状态")
    private Integer delFlag;
}

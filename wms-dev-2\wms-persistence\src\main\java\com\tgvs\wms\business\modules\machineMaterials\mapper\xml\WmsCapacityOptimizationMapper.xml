<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tgvs.wms.business.modules.machineMaterials.mapper.WmsCapacityOptimizationMapper">


    <select id="machineInfoConvergeBoxList" resultType="com.tgvs.wms.business.modules.container.Dto.machineInfoConvergeBoxDTO">
        SELECT B.box_no,B.grid_id,B.material_code,B.material_name,B.material_quantity,A.box_container_type,C.capacity/A.box_container_type AS capacity
        FROM wms_box_item AS B
                 LEFT JOIN wms_box AS A ON B.box_no=A.box_no
                 LEFT JOIN wms_machine_info AS C ON B.material_code=C.material_code
        WHERE 1=1
          AND B.material_code IN
              (
                  SELECT material_code FROM
                      (
                          SELECT * FROM
                              (
                                  SELECT B.box_no,B.grid_id,B.material_code,B.material_name,B.material_quantity,A.box_container_type,C.capacity/A.box_container_type AS capacity
                                  FROM wms_box_item AS B
                                           LEFT JOIN wms_box AS A ON B.box_no=A.box_no
                                           LEFT JOIN wms_machine_info AS C ON B.material_code=C.material_code
                                  WHERE capacity>0
                              ) AS D
                          WHERE  D.capacity > D.material_quantity
                      ) AS E
                  GROUP BY material_code
                  HAVING COUNT(*) > 1
              )
          AND B.material_quantity &lt; capacity
    </select>

</mapper>
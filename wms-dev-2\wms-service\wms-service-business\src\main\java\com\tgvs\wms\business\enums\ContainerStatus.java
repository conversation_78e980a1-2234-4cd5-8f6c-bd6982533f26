package com.tgvs.wms.business.enums;

import lombok.Getter;

/**
 * 容器状态枚举
 * 用于统一管理容器的空满状态
 */
@Getter
public enum ContainerStatus {
    
    /**
     * 空容器 - 没有任何物料
     */
    EMPTY(0, "空容器", "容器中没有任何物料"),
    
    /**
     * 部分占用 - 有物料但未满
     */
    PARTIAL(1, "部分占用", "容器中有物料但未满"),
    
    /**
     * 满容器 - 容器已满或达到100%填充
     */
    FULL(2, "满容器", "容器已满或达到100%填充");
    
    /**
     * 状态值
     */
    private final int value;
    
    /**
     * 状态名称
     */
    private final String name;
    
    /**
     * 状态描述
     */
    private final String description;
    
    ContainerStatus(int value, String name, String description) {
        this.value = value;
        this.name = name;
        this.description = description;
    }
    
    /**
     * 根据状态值获取枚举
     * @param value 状态值
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static ContainerStatus getByValue(int value) {
        for (ContainerStatus status : values()) {
            if (status.getValue() == value) {
                return status;
            }
        }
        return null;
    }
    
    /**
     * 判断是否为空容器
     */
    public boolean isEmpty() {
        return this == EMPTY;
    }
    
    /**
     * 判断是否为满容器
     */
    public boolean isFull() {
        return this == FULL;
    }
    
    /**
     * 判断是否为部分占用
     */
    public boolean isPartial() {
        return this == PARTIAL;
    }
}

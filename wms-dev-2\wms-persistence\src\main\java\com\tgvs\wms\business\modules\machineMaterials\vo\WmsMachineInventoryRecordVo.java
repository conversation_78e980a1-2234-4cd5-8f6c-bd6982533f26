package com.tgvs.wms.business.modules.machineMaterials.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
public class WmsMachineInventoryRecordVo implements Serializable {

    private String materialCode;

    private String materialName;

    private String objectId;

    /**
     * 盘点数量
     */
    private Integer inQuantity;
    /**
     * 盘点单类型
     */
    private Integer inventoryStatus;
}

package com.tgvs.wms.business.httpservice.baseBean.mes;


import com.alibaba.fastjson.annotation.JSONField;

public class CallEmptyBoxBean {
    @J<PERSON>NField(name = "SiteNo")
    private String SiteNo;

    @J<PERSON>NField(name = "OrderNo")
    private Integer OrderNo;

    @J<PERSON><PERSON>ield(name = "Quantity")
    private Integer Quantity;

    @JSONField(name = "Height")
    private Integer Height;

    @JSONField(name = "IsEmpty")
    private Integer IsEmpty;

    @JSONField(name = "UserNo")
    private String UserNo;

    public void setSiteNo(String SiteNo) {
        this.SiteNo = SiteNo;
    }

    public void setOrderNo(Integer OrderNo) {
        this.OrderNo = OrderNo;
    }

    public void setQuantity(Integer Quantity) {
        this.Quantity = Quantity;
    }

    public void setHeight(Integer Height) {
        this.Height = Height;
    }

    public void setIsEmpty(Integer IsEmpty) {
        this.IsEmpty = IsEmpty;
    }

    public void setUserNo(String UserNo) {
        this.UserNo = UserNo;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof CallEmptyBoxBean))
            return false;
        CallEmptyBoxBean other = (CallEmptyBoxBean)o;
        if (!other.canEqual(this))
            return false;
        Object this$SiteNo = getSiteNo(), other$SiteNo = other.getSiteNo();
        if ((this$SiteNo == null) ? (other$SiteNo != null) : !this$SiteNo.equals(other$SiteNo))
            return false;
        Object this$OrderNo = getOrderNo(), other$OrderNo = other.getOrderNo();
        if ((this$OrderNo == null) ? (other$OrderNo != null) : !this$OrderNo.equals(other$OrderNo))
            return false;
        Object this$Quantity = getQuantity(), other$Quantity = other.getQuantity();
        if ((this$Quantity == null) ? (other$Quantity != null) : !this$Quantity.equals(other$Quantity))
            return false;
        Object this$Height = getHeight(), other$Height = other.getHeight();
        if ((this$Height == null) ? (other$Height != null) : !this$Height.equals(other$Height))
            return false;
        Object this$IsEmpty = getIsEmpty(), other$IsEmpty = other.getIsEmpty();
        if ((this$IsEmpty == null) ? (other$IsEmpty != null) : !this$IsEmpty.equals(other$IsEmpty))
            return false;
        Object this$UserNo = getUserNo(), other$UserNo = other.getUserNo();
        return !((this$UserNo == null) ? (other$UserNo != null) : !this$UserNo.equals(other$UserNo));
    }

    protected boolean canEqual(Object other) {
        return other instanceof CallEmptyBoxBean;
    }

    public int hashCode() {
        int PRIME = 59,
        result = 1;
        Object $SiteNo = getSiteNo();
        result = result * 59 + (($SiteNo == null) ? 43 : $SiteNo.hashCode());
        Object $OrderNo = getOrderNo();
        result = result * 59 + (($OrderNo == null) ? 43 : $OrderNo.hashCode());
        Object $Quantity = getQuantity();
        result = result * 59 + (($Quantity == null) ? 43 : $Quantity.hashCode());
        Object $Height = getHeight();
        result = result * 59 + (($Height == null) ? 43 : $Height.hashCode());
        Object $IsEmpty = getIsEmpty();
        result = result * 59 + (($IsEmpty == null) ? 43 : $IsEmpty.hashCode());
        Object $UserNo = getUserNo();
        return result * 59 + (($UserNo == null) ? 43 : $UserNo.hashCode());
    }

    public String toString() {
        return "CallEmptyBoxBean(SiteNo=" + getSiteNo() + ", OrderNo=" + getOrderNo() + ", Quantity=" + getQuantity() + ", Height=" + getHeight() + ", IsEmpty=" + getIsEmpty() + ", UserNo=" + getUserNo() + ")";
    }

    public String getSiteNo() {
        return this.SiteNo;
    }

    public Integer getOrderNo() {
        return this.OrderNo;
    }

    public Integer getQuantity() {
        return this.Quantity;
    }

    public Integer getHeight() {
        return this.Height;
    }

    public Integer getIsEmpty() {
        return this.IsEmpty;
    }

    public String getUserNo() {
        return this.UserNo;
    }
}

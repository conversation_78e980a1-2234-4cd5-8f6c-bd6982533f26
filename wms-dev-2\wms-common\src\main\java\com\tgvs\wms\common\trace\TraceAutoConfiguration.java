package com.tgvs.wms.common.trace;

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class TraceAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean(ServiceContextFilter.class)
    public FilterRegistrationBean serviceContextFilterRegistration(){
        ServiceContextFilter serviceContextFilter = new ServiceContextFilter();

        FilterRegistrationBean serviceContextFilterRegistrationBean = new FilterRegistrationBean(serviceContextFilter);
        serviceContextFilterRegistrationBean.setOrder(serviceContextFilter.getOrder());

        return serviceContextFilterRegistrationBean;
    }
}

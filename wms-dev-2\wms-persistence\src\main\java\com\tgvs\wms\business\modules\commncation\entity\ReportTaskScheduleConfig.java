package com.tgvs.wms.business.modules.commncation.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.tgvs.wms.base.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 出入库上报任务配置表
 * 极简版配置，操作人员易于理解和设置
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wms_report_task_schedule_config")
@ApiModel(value = "ReportTaskScheduleConfig对象", description = "出入库上报任务配置表")
public class ReportTaskScheduleConfig extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private String id;

    // ==================== 基本设置 ====================
    
    @ApiModelProperty(value = "配置名称")
    @TableField("config_name")
    private String configName;

    @ApiModelProperty(value = "任务类型：入库上报、出库上报")
    @TableField("task_type")
    private String taskType;

    @ApiModelProperty(value = "是否启用：0禁用，1启用")
    @TableField("is_enabled")
    private Integer isEnabled;

    // ==================== 执行时间 ====================
    
    @ApiModelProperty(value = "执行间隔（分钟）：5、10、15、30、60")
    @TableField("interval_minutes")
    private Integer intervalMinutes;

    // ==================== 处理数量 ====================
    
    @ApiModelProperty(value = "每次处理数量：20、50、100")
    @TableField("batch_size")
    private Integer batchSize;

    // ==================== 重试设置 ====================
    
    @ApiModelProperty(value = "重试次数：1、2、3")
    @TableField("retry_count")
    private Integer retryCount;

    // ==================== 系统字段 ====================
    
    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @ApiModelProperty(value = "最后执行时间（时间戳）")
    @TableField("last_execute_time")
    private Long lastExecuteTime;

    @ApiModelProperty(value = "创建人")
    @TableField("create_by")
    private String createBy;

    @ApiModelProperty(value = "更新人")
    @TableField("update_by")
    private String updateBy;

    @ApiModelProperty(value = "版本号，用于乐观锁控制并发更新")
    @Version
    @TableField("version")
    private Integer version;

    @ApiModelProperty(value = "删除标志：0正常，1已删除")
    @TableLogic
    @TableField("delete_flag")
    private Integer deleteFlag;

    @ApiModelProperty(value = "备注信息")
    @TableField("remark")
    private String remark;

    // ==================== 构造方法 ====================
    
    public ReportTaskScheduleConfig() {
        // 设置默认值
        this.isEnabled = 1; // 默认启用
        this.intervalMinutes = 5; // 默认每5分钟执行
        this.batchSize = 50; // 默认每次处理50条
        this.retryCount = 3; // 默认重试3次
        this.version = 1;
        this.deleteFlag = 0;
    }

    // ==================== 业务方法 ====================
    
    /**
     * 判断配置是否启用
     * @return true-启用，false-禁用
     */
    public boolean isConfigEnabled() {
        return this.isEnabled != null && this.isEnabled == 1;
    }

    /**
     * 获取实际的执行间隔（分钟）
     * @return 执行间隔（分钟）
     */
    public int getActualInterval() {
        if (this.intervalMinutes == null || this.intervalMinutes <= 0) {
            return 5; // 默认5分钟
        }
        // 限制在预设值范围内：5、10、15、30、60
        int[] allowedValues = {5, 10, 15, 30, 60};
        for (int value : allowedValues) {
            if (this.intervalMinutes <= value) {
                return value;
            }
        }
        return 60; // 超出范围默认60分钟
    }

    /**
     * 获取实际的批处理数量
     * @return 批处理数量
     */
    public int getActualBatchSize() {
        if (this.batchSize == null || this.batchSize <= 0) {
            return 50; // 默认值
        }
        // 限制在预设值范围内：20、50、100
        if (this.batchSize <= 20) {
            return 20;
        } else if (this.batchSize <= 50) {
            return 50;
        } else {
            return 100;
        }
    }

    /**
     * 获取实际的重试次数
     * @return 重试次数
     */
    public int getActualRetryCount() {
        if (this.retryCount == null || this.retryCount < 0) {
            return 3; // 默认值
        }
        // 限制在1-3次范围内
        return Math.min(Math.max(this.retryCount, 1), 3);
    }

    /**
     * 获取执行时间描述
     * @return 执行时间描述
     */
    public String getIntervalDescription() {
        int interval = getActualInterval();
        if (interval < 60) {
            return "每" + interval + "分钟";
        } else {
            return "每小时";
        }
    }

    /**
     * 生成Cron表达式（供系统内部使用）
     * @return Cron表达式
     */
    public String generateCronExpression() {
        int interval = getActualInterval();
        if (interval >= 60) {
            // 每小时
            return "0 0 * * * ?";
        } else {
            // 分钟级间隔
            return "0 */" + interval + " * * * ?";
        }
    }

    @Override
    public String toString() {
        return "ReportTaskScheduleConfig{" +
                "id=" + id +
                ", configName='" + configName + '\'' +
                ", taskType='" + taskType + '\'' +
                ", isEnabled=" + isEnabled +
                ", intervalMinutes=" + intervalMinutes +
                ", batchSize=" + batchSize +
                ", retryCount=" + retryCount +
                ", createTime=" + createTime +
                ", lastExecuteTime=" + lastExecuteTime +
                '}';
    }
} 
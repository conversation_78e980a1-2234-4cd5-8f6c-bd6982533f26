package com.tgvs.wms.business.enums;

import com.tgvs.wms.common.util.oConvertUtils;

public enum enumBoxType {
    unknow(Integer.valueOf(-1), "unknow", "无箱"),
    emptyBox(Integer.valueOf(0), "empty", "空箱"),
    Piece(Integer.valueOf(1), "piece", "花片主料"),
    cross(Integer.valueOf(2), "cross", "十字架"),
    batching(Integer.valueOf(3), "batching", "辅料"),
    sewbatching(Integer.valueOf(4), "sewbatching", "缝制辅料"),
    clothes(Integer.valueOf(10), "clothes", "成品");

    private Integer value;

    private String code;

    private String text;

    enumBoxType(Integer value, String code, String text) {
        this.value = value;
        this.code = code;
        this.text = text;
    }

    public static enumBoxType getByValue(int value) {
        if (oConvertUtils.isEmpty(Integer.valueOf(value)))
            return null;
        for (enumBoxType val : values()) {
            if (val.getValue().equals(Integer.valueOf(value)))
                return val;
        }
        return null;
    }

    public Integer getValue() {
        return this.value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getText() {
        return this.text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public static enumBoxType toEnum(Integer Value) {
        for (enumBoxType e : values()) {
            if (e.getValue().equals(Value))
                return e;
        }
        return null;
    }

    public static enumBoxType toEnum(String Value) {
        for (enumBoxType e : values()) {
            if (e.getValue().toString().equals(Value))
                return e;
        }
        return null;
    }
}

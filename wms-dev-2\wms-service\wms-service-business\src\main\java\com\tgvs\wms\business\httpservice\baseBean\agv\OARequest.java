package com.tgvs.wms.business.httpservice.baseBean.agv;


import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class OARequest {
    /**
     * 固定格式一
     */
    @JSONField(name = "AppCode")
    private String AppCode="D278508uploadanddownload";
    /**
     * 固定格式
     */
    @JSONField(name = "Controller")
    private String Controller="MyJFKController";
    /**
     * 业务类型名称
     */
    @JSONField(name = "ActionName")
    private String ActionName;
    /**
     * 单号
     */
    @JSONField(name = "itemNO")
    private String itemNO;
    /**
     * 固定格式
     */
    @JSONField(name = "acionId")
    private String acionId="68D51DD7-CC38-4A7A-A793-D519E7494294";
    /**
     * 业务参数
     */
    @JSONField(name = "inputjson")
    private String inputjson;
}

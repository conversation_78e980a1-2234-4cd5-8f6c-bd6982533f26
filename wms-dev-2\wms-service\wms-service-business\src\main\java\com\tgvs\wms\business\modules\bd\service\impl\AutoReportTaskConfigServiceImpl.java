package com.tgvs.wms.business.modules.bd.service.impl;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tgvs.wms.business.modules.bd.entity.AutoReportTaskConfig;
import com.tgvs.wms.business.modules.bd.mapper.AutoReportTaskConfigMapper;
import com.tgvs.wms.business.modules.bd.service.IAutoReportTaskConfigService;

/**
 * 自动上报任务配置服务实现类
 */
@Service
public class AutoReportTaskConfigServiceImpl extends ServiceImpl<AutoReportTaskConfigMapper, AutoReportTaskConfig> implements IAutoReportTaskConfigService {

    @Override
    public AutoReportTaskConfig getByTaskType(String taskType) {
        LambdaQueryWrapper<AutoReportTaskConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AutoReportTaskConfig::getTaskType, taskType);
        queryWrapper.eq(AutoReportTaskConfig::getDelFlag, 0);
        return this.getOne(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveConfig(AutoReportTaskConfig config) {
        // 检查是否已存在相同类型的配置
        AutoReportTaskConfig existConfig = this.getByTaskType(config.getTaskType());
        
        if (existConfig != null) {
            // 更新现有配置
            config.setId(existConfig.getId());
            return this.updateById(config);
        } else {
            // 新增配置
            return this.save(config);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStatus(String id, String status) {
        AutoReportTaskConfig config = this.getById(id);
        if (config != null) {
            config.setStatus(status);
            return this.updateById(config);
        }
        return false;
    }
} 
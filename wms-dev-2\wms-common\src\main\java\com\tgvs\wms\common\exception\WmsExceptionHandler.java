package com.tgvs.wms.common.exception;

import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.MalformedJwtException;
import io.jsonwebtoken.SignatureException;
import org.springframework.core.annotation.Order;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.servlet.NoHandlerFoundException;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

import com.tgvs.wms.common.constant.Result;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@ControllerAdvice
@Order(1)
public class WmsExceptionHandler {

    /**
     * 处理参数验证异常
     */
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Result<?> handleValidationExceptions(MethodArgumentNotValidException ex) {
        BindingResult bindingResult = ex.getBindingResult();
        List<FieldError> fieldErrors = bindingResult.getFieldErrors();
        StringBuilder message = new StringBuilder("请求参数不符合规则：");

        for (FieldError fieldError : fieldErrors) {
            message.append(fieldError.getDefaultMessage()).append("; ");
        }

        log.error("参数验证失败: {}", message);
        return Result.error(message.toString());
    }

    /**
     * 处理JWT相关异常
     */
    @ExceptionHandler({ ExpiredJwtException.class, MalformedJwtException.class, SignatureException.class })
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    @ResponseBody
    public Result<?> handleJwtException(Exception e, HttpServletRequest request) {
        log.error("JWT验证失败: {} - {}", request.getRequestURI(), e.getMessage());
        return Result.error("登录已过期或无效，请重新登录");
    }

    /**
     * 处理404异常
     */
    @ExceptionHandler(NoHandlerFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    @ResponseBody
    public Result<?> handleNotFoundException(NoHandlerFoundException e, HttpServletRequest request) {
        log.error("请求的资源不存在: {} - {}", request.getRequestURI(), e.getMessage());
        return Result.error("请求的资源不存在");
    }

    @ResponseBody
    @ExceptionHandler(DuplicateKeyException.class)
    public Result<?> handleDuplicateKeyException(Exception e) {
        log.error("系统发生异常", e);
        return Result.error("数据库已存在该条记录");
    }

    @ResponseBody
    @ExceptionHandler(DataIntegrityViolationException.class)
    public Result<?> handleDataIntegrityViolationException(Exception e) {
        log.error("系统发生异常", e);
        return Result.error("字段太长，超出数据库字段的字段");
    }

    /**
     * 处理容器资源不足异常 - 提供用户友好的错误信息
     */
    @ResponseBody
    @ExceptionHandler(RuntimeException.class)
    public Result<?> handleContainerShortageException(RuntimeException e) {
        String message = e.getMessage();

        // 检查是否为容器不足相关的异常
        if (message != null && (message.contains("容器资源不足") ||
                message.contains("没有可用的空") ||
                message.contains("未找到合适的空") ||
                message.contains("预装箱操作无法完成"))) {

            log.warn("容器资源不足: {}", message);
            // 返回完整的用户友好错误信息
            return Result.error(message);
        }

        // 其他 RuntimeException 按原有逻辑处理
        log.error("系统发生异常", e);
        return Result.error(message != null ? message : "操作失败");
    }

    @ResponseBody
    @ExceptionHandler(Exception.class)
    public Result<?> handleNull(Exception e) {
        log.error("系统发生异常", e);
        if (e instanceof ServiceException) {
            return Result.error(e.getMessage());
        }
        return Result.error("操作失败");
    }
}

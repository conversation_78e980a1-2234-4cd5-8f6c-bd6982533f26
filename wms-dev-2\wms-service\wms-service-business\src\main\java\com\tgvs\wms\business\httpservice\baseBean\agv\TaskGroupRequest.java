package com.tgvs.wms.business.httpservice.baseBean.agv;

import lombok.Data;

import java.util.List;

@Data
public class TaskGroupRequest {
    private String groupCode;

    private String strategy;

    private String strategyValue;

    private int groupSeq;

    private TargetRoute targetRoute;

    private List<TaskData> data;

    @lombok.Data
    public static class TaskData
    {
        private String robotTaskCode;

        private int sequence;
    }

    @lombok.Data
    public static class TargetRoute
    {
        private String type;

        private String code;
    }


}

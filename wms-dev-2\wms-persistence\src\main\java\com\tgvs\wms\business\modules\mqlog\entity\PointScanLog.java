package com.tgvs.wms.business.modules.mqlog.entity;

import java.io.Serializable;
import java.util.Date;

import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TableName("wms_point_scan")
@ApiModel(value = "wms_point_scan对象", description = "扫描器读码日志")
@Data
public class PointScanLog implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("id")
    private String id;

    @Excel(name = "节点编号", width = 15.0D)
    @ApiModelProperty("节点编号")
    private String siteNo;

    @Excel(name = "关联设备编号", width = 15.0D)
    @ApiModelProperty("关联设备编号")
    private String deviceno;

    @Excel(name = "扫描结果", width = 15.0D)
    @ApiModelProperty("扫描结果")
    private String scan;

    @Excel(name = "成功标志", width = 15.0D)
    @ApiModelProperty("成功标志")
    private Integer scanResult;

    @Excel(name = "条码", width = 15.0D)
    @ApiModelProperty("条码")
    private String bacord;

    @ApiModelProperty("创建人")
    private String createBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("更新时间")
    private Date updateTime;

    @Excel(name = "删除状态", width = 15.0D)
    @ApiModelProperty("删除状态")
    private Integer delFlag;
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tgvs.wms.business.modules.machineAuxiliary.mapper.WmsAuxiliaryDetailMapper">
    <!-- 根据关联单号查询明细列表 -->
    <select id="selectByRefNumber" resultType="com.tgvs.wms.business.modules.machineAuxiliary.entity.WmsAuxiliaryDetail">
        select * from wms_auxiliary_detail
        where ref_number = #{refNumber}
        order by priority desc, create_time desc
    </select>

    <!-- 根据物料编码查询明细列表 -->
    <select id="selectByMaterialCode" resultType="com.tgvs.wms.business.modules.machineAuxiliary.entity.WmsAuxiliaryDetail">
        select * from wms_auxiliary_detail
        where material_code = #{materialCode}
        order by create_time desc
    </select>

    <!-- 根据操作类型查询明细列表 -->
    <select id="selectByOperationType" resultType="com.tgvs.wms.business.modules.machineAuxiliary.entity.WmsAuxiliaryDetail">
        select * from wms_auxiliary_detail
        where operation_type = #{operationType}
        order by create_time desc
    </select>

    <!-- 根据物料编码统计数量 -->
    <select id="sumQuantityByMaterial" resultType="java.lang.Integer">
        select IFNULL(sum(quantity), 0)
        from wms_auxiliary_detail
        where material_code = #{materialCode}
        and operation_type = #{operationType}
    </select>

</mapper> 
package com.tgvs.wms.business.delegate;

import java.util.ArrayList;
import java.util.List;

public class EventHandler {
    private List<Event> objects = new ArrayList<>();

    public void addEvent(Object object, String methodName, Object... args) {
        this.objects.add(new Event(object, methodName, args));
    }

    public void notifyX() throws Exception {
        for (Event event : this.objects)
            event.invoke();
    }
}
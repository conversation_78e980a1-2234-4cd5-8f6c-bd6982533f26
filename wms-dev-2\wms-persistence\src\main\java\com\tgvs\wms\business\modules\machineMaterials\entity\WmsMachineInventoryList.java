package com.tgvs.wms.business.modules.machineMaterials.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 机物料盘点单表
 */
@Data
@TableName("wms_machine_inventory_list")
public class WmsMachineInventoryList implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 盘点单号
     */
    private String inStoreNumber;
    
    /**
     * 盘点状态（0:未盘点；1.正在盘点；2:盘点完成；3:盘点结果已上报）
     */
    private Integer inventoryStatus;
    
    /**
     * 盘点日期
     */
    private Date inventoryDate;
    
    /**
     * 盘点负责人
     */
    private String inventoryPerson;
    
    /**
     * 备注
     */
    private String remark;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    
    /**
     * 删除标志(0:未删除，1:已删除)
     */
    private Integer deleteFlag;

    /**
     * 盘点类型：1.正常盘点；2.财务盘点；3：异常盘点
     */
    private Integer inventoryType;

} 
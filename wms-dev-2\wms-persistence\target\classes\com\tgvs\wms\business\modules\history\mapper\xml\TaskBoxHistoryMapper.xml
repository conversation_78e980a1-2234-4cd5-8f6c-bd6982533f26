<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tgvs.wms.business.modules.history.mapper.TaskBoxHistoryMapper">

	<update id="updateTreeNodeStatus" parameterType="java.lang.String">
		update wms_task_box set has_child = #{status} where id = #{id}
	</update>

	<select id="selectListCount" resultType="com.tgvs.wms.business.modules.task.entity.TaskBoxChart">
		select type as name,count(id) as total from wms_task_box GROUP BY type ORDER BY total desc
	</select>

</mapper>
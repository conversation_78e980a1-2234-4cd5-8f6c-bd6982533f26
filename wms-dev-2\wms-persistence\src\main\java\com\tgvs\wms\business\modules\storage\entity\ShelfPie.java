package com.tgvs.wms.business.modules.storage.entity;

import java.io.Serializable;

import org.jeecgframework.poi.excel.annotation.Excel;

import com.baomidou.mybatisplus.annotation.TableName;
import com.tgvs.wms.common.annotation.Dict;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TableName("wms_box_shelf")
@ApiModel(value = "wms_box_shelf对象", description = "货位管理")
@Data
public class ShelfPie implements Serializable {
    private static final long serialVersionUID = 1L;

    @Excel(name = "容器分类", width = 15.0D, dicCode = "box_type")
    @Dict(dicCode = "box_type")
    @ApiModelProperty("容器分类")
    private String item;

    @Excel(name = "数量", width = 15.0D)
    @ApiModelProperty("数量")
    private Integer count;
}

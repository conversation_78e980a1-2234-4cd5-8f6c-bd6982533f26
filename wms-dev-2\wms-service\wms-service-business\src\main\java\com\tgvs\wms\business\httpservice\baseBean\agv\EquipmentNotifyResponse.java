package com.tgvs.wms.business.httpservice.baseBean.agv;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

/**
 * 外设执行通知响应对象
 * 对应RCS返回给WCS的响应
 */
@Data
public class EquipmentNotifyResponse {
    /**
     * 响应代码
     * SUCCESS: 成功
     * Err_TaskNotStart: 任务尚未开始
     * Err_TaskFinished: 任务已结束
     * Err_TaskNotFound: 任务找不到
     * Err_Internal: 内部错误
     */
    private String code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 响应数据
     */
    private ResponseData data;
    
    @Data
    public static class ResponseData {
        /**
         * 任务号，全局唯一
         */
        private String taskCode;
        
        /**
         * 自定义扩展字段
         */
        private JSONObject extra;
    }
} 
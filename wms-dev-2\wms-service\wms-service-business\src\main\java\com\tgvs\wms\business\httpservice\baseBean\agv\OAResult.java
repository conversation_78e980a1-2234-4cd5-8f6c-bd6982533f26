package com.tgvs.wms.business.httpservice.baseBean.agv;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class OAResult {
    @JSONField(name = "Successful")
    private boolean Successful;

    @JSONField(name = "ErrorMessage")
    private String ErrorMessage;

    @JSONField(name = "Detail")
    private String Detail;

    @JSONField(name = "Logined")
    private boolean Logined;

    @JSONField(name = "ReturnData")
    private String ReturnData;

    @JSONField(name = "DataType")
    private int DataType;

}

package com.tgvs.wms.business.modules.task.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.tgvs.wms.base.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * WMS任务上报ROS队列表
 * 记录需要向ROS系统上报的入库/出库任务信息，支持定时批量上报和重试机制
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wms_task_report_queue")
@ApiModel(value = "TaskReportQueue对象", description = "WMS任务上报ROS队列表")
public class TaskReportQueue extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID，自增长")
    @TableId(value = "id", type = IdType.AUTO)
    private String id;

    // ==================== 任务基本信息 ====================
    
    @ApiModelProperty(value = "任务单号，对应WmsBoxTaskList表的taskOrder字段")
    @TableField("task_order")
    private String taskOrder;

    @ApiModelProperty(value = "任务类型：0采购入库，1调拨入库，2生产退料回库，3领料出库，4调拨出库，5采购退货出库，6指定出库，7指定入库，8紧急出库，10盘点出库")
    @TableField("task_type")
    private Integer taskType;

    @ApiModelProperty(value = "箱号/托盘号，对应任务的载具编号")
    @TableField("box_no")
    private String boxNo;

    @ApiModelProperty(value = "箱型：1料箱，2托盘")
    @TableField("box_type")
    private Integer boxType;



    // ==================== 上报分类和状态 ====================
    
    @ApiModelProperty(value = "上报类型：INBOUND入库上报，OUTBOUND出库上报")
    @TableField("report_type")
    private String reportType;

    @ApiModelProperty(value = "上报状态：0待上报，1已上报，2上报失败，3已取消")
    @TableField("report_status")
    private Integer reportStatus;

    @ApiModelProperty(value = "上报优先级：1最高，5普通，9最低，数字越小优先级越高")
    @TableField("priority")
    private Integer priority;

    // ==================== 重试机制相关 ====================
    
    @ApiModelProperty(value = "重试次数，记录已重试的次数")
    @TableField("retry_count")
    private Integer retryCount;

    @ApiModelProperty(value = "最大重试次数，超过此次数将标记为失败")
    @TableField("max_retry_count")
    private Integer maxRetryCount;

    @ApiModelProperty(value = "下次重试时间，用于延迟重试策略")
    @TableField("next_retry_time")
    private Date nextRetryTime;

    // ==================== 时间记录 ====================
    
    @ApiModelProperty(value = "创建时间，任务结束时的记录时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "更新时间，最后一次状态变更时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty(value = "首次上报时间")
    @TableField("first_report_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date firstReportTime;

    @ApiModelProperty(value = "最后一次上报时间")
    @TableField("last_report_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastReportTime;

    @ApiModelProperty(value = "成功上报时间")
    @TableField("success_report_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date successReportTime;

    // ==================== 错误处理 ====================
    
    @ApiModelProperty(value = "错误信息，记录上报失败的详细原因")
    @TableField("error_message")
    private String errorMessage;

    @ApiModelProperty(value = "错误代码，便于分类统计错误类型")
    @TableField("error_code")
    private String errorCode;

    // ==================== 业务扩展字段 ====================
    
    @ApiModelProperty(value = "物料类型：1生产物料，2辅料，3其他")
    @TableField("material_type")
    private Integer materialType;

    @ApiModelProperty(value = "批次号，用于批量上报时的分组标识")
    @TableField("batch_no")
    private String batchNo;

    @ApiModelProperty(value = "备注信息，记录特殊情况说明")
    @TableField("remark")
    private String remark;

    // ==================== 系统字段 ====================
    
    @ApiModelProperty(value = "版本号，用于乐观锁控制并发更新")
    @Version
    @TableField("version")
    private Integer version;

    @ApiModelProperty(value = "删除标志：0正常，1已删除")
    @TableLogic
    @TableField("delete_flag")
    private Integer deleteFlag;

    // ==================== 构造方法 ====================
    
    public TaskReportQueue() {
        // 设置默认值
        this.reportStatus = 0;
        this.priority = 5;
        this.retryCount = 0;
        this.maxRetryCount = 3;
        this.version = 1;
        this.deleteFlag = 0;
    }

    // ==================== 业务方法 ====================
    
    /**
     * 判断是否可以重试
     * @return true-可以重试，false-不能重试
     */
    public boolean canRetry() {
        return this.retryCount < this.maxRetryCount && this.reportStatus != 1 && this.reportStatus != 3;
    }

    /**
     * 增加重试次数
     */
    public void incrementRetryCount() {
        if (this.retryCount == null) {
            this.retryCount = 0;
        }
        this.retryCount++;
    }

    /**
     * 判断是否为入库任务
     * @return true-入库任务，false-出库任务
     */
    public boolean isInboundTask() {
        return "INBOUND".equals(this.reportType);
    }

    /**
     * 判断是否为出库任务
     * @return true-出库任务，false-入库任务
     */
    public boolean isOutboundTask() {
        return "OUTBOUND".equals(this.reportType);
    }

    /**
     * 判断是否为紧急任务
     * @return true-紧急任务，false-普通任务
     */
    public boolean isUrgentTask() {
        return this.priority != null && this.priority <= 2;
    }

    /**
     * 设置上报成功状态
     */
    public void markReportSuccess() {
        this.reportStatus = 1;
        this.successReportTime = new Date();
        this.errorMessage = null;
        this.errorCode = null;
    }

    /**
     * 设置上报失败状态
     * @param errorCode 错误代码
     * @param errorMessage 错误信息
     */
    public void markReportFailed(String errorCode, String errorMessage) {
        this.reportStatus = 2;
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
        this.lastReportTime = new Date();
    }

    /**
     * 设置取消状态
     */
    public void markCancelled() {
        this.reportStatus = 3;
    }

    /**
     * 计算下次重试时间（指数退避策略）
     * @param baseDelayMinutes 基础延迟分钟数
     */
    public void calculateNextRetryTime(int baseDelayMinutes) {
        if (this.retryCount == null) {
            this.retryCount = 0;
        }
        
        // 指数退避：baseDelay * 2^retryCount
        long delayMinutes = baseDelayMinutes * (1L << this.retryCount);
        
        // 最大延迟不超过24小时
        delayMinutes = Math.min(delayMinutes, 24 * 60);
        
        this.nextRetryTime = new Date(System.currentTimeMillis() + delayMinutes * 60 * 1000);
    }

    @Override
    public String toString() {
        return "TaskReportQueue{" +
                "id=" + id +
                ", taskOrder='" + taskOrder + '\'' +
                ", taskType=" + taskType +
                ", reportType='" + reportType + '\'' +
                ", reportStatus=" + reportStatus +
                ", priority=" + priority +
                ", retryCount=" + retryCount +
                ", createTime=" + createTime +
                '}';
    }
}

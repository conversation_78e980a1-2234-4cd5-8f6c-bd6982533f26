<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tgvs.wms.business.modules.machineMaterials.mapper.MaterialInfoMapper">
    
    <!-- 自定义条件查询机物料基础信息 -->
    <select id="selectWmsMaterialInfoCustom" resultType="com.tgvs.wms.business.modules.machineMaterials.entity.WmsMachineInfo">
        select id, material_code, material_name, capacity, priority_container_type,
               create_by, create_time, update_by, update_time
        from wms_material_info
        <where>
            <if test="materialCode != null and materialCode != ''">
                AND material_code = #{materialCode}
            </if>
            <if test="materialName != null and materialName != ''">
                AND material_name like concat('%', #{materialName}, '%')
            </if>
            <if test="capacity != null">
                AND capacity = #{capacity}
            </if>
            <if test="priorityContainerType != null">
                AND priority_container_type = #{priorityContainerType}
            </if>
            AND delete_flag = 0
        </where>
        order by create_time desc
    </select>
    
    <!-- 批量逻辑删除机物料基础信息 -->
    <update id="deleteWmsMaterialInfoByIds">
        update wms_material_info set delete_flag = 1 
        where id in
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 查询同物料编码的物料记录大于1 -->
    <select id="selectItemNumber" resultType="com.tgvs.wms.business.modules.machineMaterials.vo.WmsMachineInfoVo">
        SELECT material_code, COUNT(*) AS itemNumber
        FROM wms_box_item
        WHERE material_quantity>0
          AND status=2
        <if test="materialCode != null">
            AND material_code = #{materialCode}
        </if>
        GROUP BY material_code
        HAVING COUNT(*) > 1
    </select>


    <!-- 根据物料编码查询可以合箱库存数据 -->
    <select id="selectConvergeBoxList" resultType="com.tgvs.wms.business.modules.machineMaterials.vo.WmsMachineInfoConvergeVo">
        SELECT B.box_no,B.grid_id,B.material_code,B.material_name,B.material_quantity,A.box_container_type,C.capacity
        FROM wms_box_item AS B
        LEFT JOIN wms_box AS A ON B.box_no=A.box_no
        LEFT JOIN wms_machine_info AS C ON B.material_code=C.material_code
        <where>
            <if test="materialCode != null and materialCode != ''">
                AND material_code = #{materialCode}
            </if>
        </where>
    </select>

</mapper> 
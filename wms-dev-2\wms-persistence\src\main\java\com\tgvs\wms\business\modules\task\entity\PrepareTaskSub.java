package com.tgvs.wms.business.modules.task.entity;

import java.io.Serializable;
import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.tgvs.wms.common.annotation.Dict;
import com.tgvs.wms.common.annotation.Excel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data

@TableName("wms_prepare_task_sub")
@ApiModel(value = "wms_prepare_task_sub对象", description = "备货任务明细")
public class PrepareTaskSub implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("id")
    private String id;

    @Excel(name = "拉布单号", width = 15.0D)
    @ApiModelProperty("拉布单号")
    private String billNo;

    @Excel(name = "布匹编号", width = 15.0D)
    @ApiModelProperty("布匹编号")
    private String clothNo;

    @Excel(name = "坯布重量", width = 15.0D)
    @ApiModelProperty("坯布重量")
    private Integer weight;

    @Excel(name = "对应铺床号", width = 15.0D)
    @ApiModelProperty("对应铺床号")
    private String latheNo;

    @Excel(name = "铺布计划编号", width = 15.0D)
    @ApiModelProperty("铺布计划编号")
    private String pubuPlan;

    @Excel(name = "缸号", width = 15.0D)
    @ApiModelProperty("缸号")
    private String vatNo;

    @Excel(name = "松布完成", width = 15.0D, dicCode = "yn")
    @Dict(dicCode = "yn")
    @ApiModelProperty("松布完成")
    private Integer issongbu;

    @Excel(name = "合约号", width = 15.0D)
    @ApiModelProperty("合约号")
    private String contractNo;

    @Excel(name = "计划日期", width = 20.0D, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("计划日期")
    private Date planTime;

    @Excel(name = "发布更新日期", width = 20.0D, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("发布更新日期")
    private Date publishTime;

    @Excel(name = "执行状态", width = 15.0D, dictTable = "wms_prepare_state", dicText = "text", dicCode = "value")
    @Dict(dictTable = "wms_prepare_state", dicText = "text", dicCode = "value")
    @ApiModelProperty("执行状态")
    private Integer state;

    @Excel(name = "完成标志", width = 15.0D)
    @ApiModelProperty("完成标志")
    private Integer completed;

    @ApiModelProperty("松布架位置")
    private String site;

    @ApiModelProperty("松布架号")
    private String dollyNo;

    @Excel(name = "AGV搬运标志", width = 15.0D, dicCode = "yn")
    @Dict(dicCode = "yn")
    @ApiModelProperty("AGV搬运标志")
    private Integer agvCompleted;

    @ApiModelProperty("提示信息")
    private String msg;

    @Excel(name = "计划执行顺序号", width = 15.0D)
    @ApiModelProperty("计划执行顺序号")
    private Integer planSort;

    @Excel(name = "优先级", width = 15.0D)
    @ApiModelProperty("优先级")
    private Integer priority;

    @Excel(name = "呼叫结果", width = 15.0D, dicCode = "result_type")
    @Dict(dicCode = "result_type")
    @ApiModelProperty("呼叫结果")
    private Integer callresult;

    @ApiModelProperty("创建人")
    private String createBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("更新时间")
    private Date updateTime;

    @Excel(name = "删除状态", width = 15.0D)
    @ApiModelProperty("删除状态")
    private Integer delFlag;
}

package com.tgvs.wms.business.modules.container.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tgvs.wms.base.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName ( "wms_machinematerials_box_pre_packing" )
@Data
public class WmsMachineMaterialsBoxPrePacking  extends BaseEntity {

	private static final long serialVersionUID =  1080046053240720055L;



	/**
	 * 格号
	 */
   	@TableField(value = "grid_code")
	private Integer gridCode;

	/**
	 * 容器号
	 */
   	@TableField(value = "box_code")
	private String boxCode;

	/**
	 * 容器类型：1.料箱；2.料架
	 */
   	@TableField(value = "container_type")
	private Integer containerType;

	/**
	 * 物料编码
	 */
   	@TableField(value = "material_code")
	private String materialCode;

	/**
	 * 物料名称
	 */
   	@TableField(value = "material_name")
	private String materialName;

	/**
	 * 规格
	 */
   	@TableField(value = "asset_class")
	private String assetClass;

	/**
	 * 资产类别
	 */
   	@TableField(value = "assetc_model")
	private String assetcModel;

	/**
	 * 品牌
	 */
	@TableField(value = "brand")
	private String brand;

	/**
	 * 原数量
	 */
	@TableField(value = "original_quantity")
	private BigDecimal originalOuantity;
	/**
	 * 待入库数量
	 */
   	@TableField(value = "pending_quantity")
	private BigDecimal pendingQuantity;

	/**
	 * 实际入库数量-预装数量
	 */
   	@TableField(value = "actual_quantity")
	private BigDecimal actualQuantity;

	/**
	 * 操作人员
	 */
   	@TableField(value = "operator")
	private String operator;

	/**
	 * 状态(0.未使用，1.预装，2.已装，3.入库完成）
	 */
   	@TableField(value = "status")
	private Integer status;

	/**
	 * 格子状态（1.空；2.未满；3.满）
	 */
	@TableField(value = "grid_status")
	private Integer gridStatus;

	/**
	 * 入库单号
	 */
	@TableField(value = "in_store_number")
	private String inStoreNumber;

	/**
	 * 唯一标识
	 */
	@TableField(value = "object_id")
	private String objectId;

	/**
	 * 任务状态：0：未生成任务；1.已生成任务
	 */
	@TableField(value = "task_status")
	private Integer taskStatus;
	/**
	 * 推送状态：1.未推送，2.已推送
	 */
	@TableField(value = "push_status")
	private Integer pushStatus;
	/**
	 * 是否删除：0.未删除；1.已删除
	 */
	@TableField(value = "delete_flag")
	private Integer deleteFlag;
	/**
	 * 任务号
	 */
	@TableField(value = "task_order")
	private String taskOrder;

	/**
	 * 耗时
	 */
	@TableField(value = "time_consuming")
	@ApiModelProperty("耗时")
	private String timeConsuming;
}

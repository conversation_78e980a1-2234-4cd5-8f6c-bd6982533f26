package com.tgvs.wms.business.httpservice.baseBean.agv;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

/**
 * 海康AGV任务下发响应实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TaskSubmitResponse extends AgvBaseResponse {

    /**
     * 业务数据
     */
    private TaskSubmitResponseData data;

    /**
     * 任务下发接口响应数据
     */
    @Data
    public static class TaskSubmitResponseData {
        
        /**
         * 任务号，全局唯一
         */
        private String robotTaskCode;
        
        /**
         * 自定义扩展字段
         */
        private Map<String, Object> extra;
    }
} 
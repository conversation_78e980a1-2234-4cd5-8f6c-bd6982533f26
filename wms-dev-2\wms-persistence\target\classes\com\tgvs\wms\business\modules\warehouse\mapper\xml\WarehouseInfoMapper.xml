<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tgvs.wms.business.modules.warehouse.mapper.WarehouseInfoMapper">

    <!-- 未来可以在这里添加自定义的 SQL 查询 -->
    <!-- 例如:
    <select id="selectCustomWarehouseInfo" resultType="com.tgvs.wms.business.modules.warehouse.entity.WarehouseInfo">
        SELECT * FROM wms_warehouse_info WHERE custom_condition = #{param}
    </select>
     -->

</mapper> 
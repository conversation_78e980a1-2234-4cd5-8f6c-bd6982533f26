package com.tgvs.wms.business.httpservice.baseBean.dps;

import com.alibaba.fastjson.annotation.JSONField;

public class CreatControlBean {
    @J<PERSON><PERSON>ield(name = "ControllerID")
    private int ControllerID;

    @<PERSON><PERSON><PERSON>ield(name = "IPAddress")
    private String IPAddress;

    @<PERSON><PERSON><PERSON>ield(name = "Port")
    private int Port;

    @JSO<PERSON>ield(name = "License")
    private String License;

    @JSO<PERSON>ield(serialize = false)
    private int MonitorController;

    @JSONField(serialize = false)
    private int MonitorInterval;

    @JSONField(serialize = false)
    private int MonitorMaxRetryCount;

    @JSONField(serialize = false)
    private int TimeOut;

    public void setControllerID(int ControllerID) {
        this.ControllerID = ControllerID;
    }

    public void setIPAddress(String IPAddress) {
        this.IPAddress = IPAddress;
    }

    public void setPort(int Port) {
        this.Port = Port;
    }

    public void setLicense(String License) {
        this.License = License;
    }

    public void setMonitorController(int MonitorController) {
        this.MonitorController = MonitorController;
    }

    public void setMonitorInterval(int MonitorInterval) {
        this.MonitorInterval = MonitorInterval;
    }

    public void setMonitorMaxRetryCount(int MonitorMaxRetryCount) {
        this.MonitorMaxRetryCount = MonitorMaxRetryCount;
    }

    public void setTimeOut(int TimeOut) {
        this.TimeOut = TimeOut;
    }

    public void setLightstepType(int LightstepType) {
        this.LightstepType = LightstepType;
    }

    public void setPCMediaPath(String PCMediaPath) {
        this.PCMediaPath = PCMediaPath;
    }

    public void setMsgAddrWeightValue(int MsgAddrWeightValue) {
        this.MsgAddrWeightValue = MsgAddrWeightValue;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof CreatControlBean))
            return false;
        CreatControlBean other = (CreatControlBean)o;
        if (!other.canEqual(this))
            return false;
        if (getControllerID() != other.getControllerID())
            return false;
        Object this$IPAddress = getIPAddress(), other$IPAddress = other.getIPAddress();
        if ((this$IPAddress == null) ? (other$IPAddress != null) : !this$IPAddress.equals(other$IPAddress))
            return false;
        if (getPort() != other.getPort())
            return false;
        Object this$License = getLicense(), other$License = other.getLicense();
        if ((this$License == null) ? (other$License != null) : !this$License.equals(other$License))
            return false;
        if (getMonitorController() != other.getMonitorController())
            return false;
        if (getMonitorInterval() != other.getMonitorInterval())
            return false;
        if (getMonitorMaxRetryCount() != other.getMonitorMaxRetryCount())
            return false;
        if (getTimeOut() != other.getTimeOut())
            return false;
        if (getLightstepType() != other.getLightstepType())
            return false;
        Object this$PCMediaPath = getPCMediaPath(), other$PCMediaPath = other.getPCMediaPath();
        return ((this$PCMediaPath == null) ? (other$PCMediaPath != null) : !this$PCMediaPath.equals(other$PCMediaPath)) ? false : (!(getMsgAddrWeightValue() != other.getMsgAddrWeightValue()));
    }

    protected boolean canEqual(Object other) {
        return other instanceof CreatControlBean;
    }

    public int hashCode() {
        int PRIME = 59,
        result = 1;
        result = result * 59 + getControllerID();
        Object $IPAddress = getIPAddress();
        result = result * 59 + (($IPAddress == null) ? 43 : $IPAddress.hashCode());
        result = result * 59 + getPort();
        Object $License = getLicense();
        result = result * 59 + (($License == null) ? 43 : $License.hashCode());
        result = result * 59 + getMonitorController();
        result = result * 59 + getMonitorInterval();
        result = result * 59 + getMonitorMaxRetryCount();
        result = result * 59 + getTimeOut();
        result = result * 59 + getLightstepType();
        Object $PCMediaPath = getPCMediaPath();
        result = result * 59 + (($PCMediaPath == null) ? 43 : $PCMediaPath.hashCode());
        return result * 59 + getMsgAddrWeightValue();
    }

    public String toString() {
        return "CreatControlBean(ControllerID=" + getControllerID() + ", IPAddress=" + getIPAddress() + ", Port=" + getPort() + ", License=" + getLicense() + ", MonitorController=" + getMonitorController() + ", MonitorInterval=" + getMonitorInterval() + ", MonitorMaxRetryCount=" + getMonitorMaxRetryCount() + ", TimeOut=" + getTimeOut() + ", LightstepType=" + getLightstepType() + ", PCMediaPath=" + getPCMediaPath() + ", MsgAddrWeightValue=" + getMsgAddrWeightValue() + ")";
    }

    public int getControllerID() {
        return this.ControllerID;
    }

    public String getIPAddress() {
        return this.IPAddress;
    }

    public int getPort() {
        return this.Port;
    }

    public String getLicense() {
        return this.License;
    }

    public int getMonitorController() {
        return this.MonitorController;
    }

    public int getMonitorInterval() {
        return this.MonitorInterval;
    }

    public int getMonitorMaxRetryCount() {
        return this.MonitorMaxRetryCount;
    }

    public int getTimeOut() {
        return this.TimeOut;
    }

    @JSONField(name = "LightstepType")
    private int LightstepType = 0;

    @JSONField(serialize = false)
    private String PCMediaPath;

    @JSONField(serialize = false)
    private int MsgAddrWeightValue;

    public int getLightstepType() {
        return this.LightstepType;
    }

    public String getPCMediaPath() {
        return this.PCMediaPath;
    }

    public int getMsgAddrWeightValue() {
        return this.MsgAddrWeightValue;
    }

    public CreatControlBean(int controllerID, String adress, int port, String key) {
        setControllerID(controllerID);
        setIPAddress(adress);
        setLicense(key);
        setPort(port);
    }
}

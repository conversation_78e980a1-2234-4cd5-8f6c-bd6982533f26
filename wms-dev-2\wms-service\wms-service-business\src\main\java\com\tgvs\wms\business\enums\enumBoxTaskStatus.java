package com.tgvs.wms.business.enums;

import com.tgvs.wms.common.util.oConvertUtils;

public enum enumBoxTaskStatus {

    type0(Integer.valueOf(0), "type1", "创建"),
    type1(Integer.valueOf(1), "type1", "任务已下发"),
    type2(Integer.valueOf(2), "type2", "执行搬运"),
    type3(Integer.valueOf(3), "type3", "输送线运输"),
    type4(Integer.valueOf(4), "type4", "完成");



    //    任务类型：0.采购入库，1.调拨入库，2.生产退料回库，3.领料出库，4.调拨出库，5.采购退货出库，6.指定出库，7.盘点出库
    private Integer value;

    private String code;

    private String text;

    enumBoxTaskStatus(Integer value, String code, String text) {
        this.value = value;
        this.code = code;
        this.text = text;
    }

    public static enumBoxTaskStatus getByValue(int value) {
        if (oConvertUtils.isEmpty(Integer.valueOf(value)))
            return null;
        for (enumBoxTaskStatus val : values()) {
            if (val.getValue().equals(Integer.valueOf(value)))
                return val;
        }
        return null;
    }

    public Integer getValue() {
        return this.value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getText() {
        return this.text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public static enumBoxTaskStatus toEnum(Integer Value) {
        for (enumBoxTaskStatus e : values()) {
            if (e.getValue().equals(Value))
                return e;
        }
        return null;
    }

    public static enumBoxTaskStatus toEnum(String Value) {
        for (enumBoxTaskStatus e : values()) {
            if (e.getValue().toString().equals(Value))
                return e;
        }
        return null;
    }
}

package com.tgvs.wms.business.httpservice.api;


import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tgvs.wms.business.httpservice.baseBean.dps.ConfirmBean;
import com.tgvs.wms.business.httpservice.baseBean.dps.HeartbeatBean;
import com.tgvs.wms.business.util.Logger;
import com.tgvs.wms.business.wmsservice.server.DpsService;
import com.tgvs.wms.business.wmsservice.server.MesService;
import com.tgvs.wms.common.annotation.AutoLog;

import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping({"/api/dps"})
@Slf4j
public class DpsApi {

    @ApiOperation(value = "DPS控制器心跳", notes = "DPS控制器心跳")
    @PostMapping({"/Hearttttttt"})
    public void Heart(@RequestBody JSONObject jsonObject) {
        HeartbeatBean heartbeatBean = new HeartbeatBean();
        if (null != jsonObject)
            heartbeatBean = (HeartbeatBean) JSON.toJavaObject((JSON)jsonObject, HeartbeatBean.class);
        if (heartbeatBean.getKey() != 0);
    }

    @AutoLog(value = "DPS电子标签按键回调", operateType = 2)
    @ApiOperation(value = "DPS电子标签按键回调", notes = "DPS电子标签按键返回事件")
    @PostMapping({"/Confirm"})
    public void Confirm(@RequestBody JSONObject jsonObject) {
        ConfirmBean confirmBean = new ConfirmBean();
        if (null != jsonObject)
            confirmBean = (ConfirmBean)JSON.toJavaObject((JSON)jsonObject, ConfirmBean.class);
        Logger.logFile(JSON.toJSONString(confirmBean));
        MesService.insertMQ("DPS", "WMS1", "DPS", "0", "Confirm", JSON.toJSONString(confirmBean));
        DpsService.result(confirmBean);
    }
}

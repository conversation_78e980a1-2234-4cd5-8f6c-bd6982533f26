package com.tgvs.wms.business.modules.auxiliaryInventory.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tgvs.wms.business.modules.auxiliaryInventory.entity.WmsAuxiliaryInventoryDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 辅料盘点明细Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-07-19
 */
public interface WmsAuxiliaryInventoryDetailMapper extends BaseMapper<WmsAuxiliaryInventoryDetail> {

    /**
     * 根据盘点单ID查询明细列表
     * 
     * @param inventoryId 盘点单ID (String类型UUID)
     * @return 明细列表
     */
    List<WmsAuxiliaryInventoryDetail> selectByInventoryId(@Param("inventoryId") String inventoryId);

    /**
     * 分页查询盘点明细
     * 
     * @param page 分页对象
     * @param inventoryId 盘点单ID (String类型UUID)
     * @param status 状态
     * @return 分页结果
     */
    IPage<WmsAuxiliaryInventoryDetail> selectDetailPage(Page<WmsAuxiliaryInventoryDetail> page, 
                                                           @Param("inventoryId") String inventoryId,
                                                           @Param("status") String status);

    /**
     * 统计盘点进度
     * 
     * @param inventoryId 盘点单ID (String类型UUID)
     * @return 统计结果
     */
    List<InventoryProgressVO> countProgressByInventoryId(@Param("inventoryId") String inventoryId);



    /**
     * 批量更新盘点数量
     * 
     * @param details 明细列表
     * @return 更新行数
     */
    int batchUpdateCountQty(@Param("details") List<WmsAuxiliaryInventoryDetail> details);

    /**
     * 统计差异项目
     * 
     * @param inventoryId 盘点单ID (String类型UUID)
     * @return 差异项目列表
     */
    List<WmsAuxiliaryInventoryDetail> selectDiffItems(@Param("inventoryId") String inventoryId);

    /**
     * 盘点进度VO
     */
  public  class InventoryProgressVO {
        private String status;
        private Integer count;

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public Integer getCount() {
            return count;
        }

        public void setCount(Integer count) {
            this.count = count;
        }
    }
}
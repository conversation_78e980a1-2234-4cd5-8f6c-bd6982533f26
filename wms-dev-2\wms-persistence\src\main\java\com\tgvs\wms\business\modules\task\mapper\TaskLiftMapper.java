package com.tgvs.wms.business.modules.task.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tgvs.wms.business.modules.task.entity.TaskLift;

public interface TaskLiftMapper extends BaseMapper<TaskLift> {
    List<TaskLift> getUserByOrgCode(IPage paramIPage, @Param("userParams") TaskLift paramTaskLift);
}

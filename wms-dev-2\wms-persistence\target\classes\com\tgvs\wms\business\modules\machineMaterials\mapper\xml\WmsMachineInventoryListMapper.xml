<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tgvs.wms.business.modules.machineMaterials.mapper.WmsMachineInventoryListMapper">
    
    <!-- 自定义条件查询机物料盘点单列表 -->
    <select id="selectWmsMachineInventoryListCustom" resultType="com.tgvs.wms.business.modules.machineMaterials.entity.WmsMachineInventoryList">
        select id, in_store_number, material_code, material_name, inventory_quantity, 
               inventory_status, inventory_date, inventory_person, remark,
               create_by, create_time, update_by, update_time, delete_flag
        from wms_machine_inventory_list
        <where>
            <if test="query.inStoreNumber != null and query.inStoreNumber != ''">
                AND in_store_number = #{query.inStoreNumber}
            </if>
            <if test="query.materialCode != null and query.materialCode != ''">
                AND material_code = #{query.materialCode}
            </if>
            <if test="query.materialName != null and query.materialName != ''">
                AND material_name like concat('%', #{query.materialName}, '%')
            </if>
            <if test="query.inventoryStatus != null">
                AND inventory_status = #{query.inventoryStatus}
            </if>
            <if test="query.inventoryPerson != null and query.inventoryPerson != ''">
                AND inventory_person = #{query.inventoryPerson}
            </if>
            <if test="query.beginInventoryDate != null">
                AND inventory_date &gt;= #{query.beginInventoryDate}
            </if>
            <if test="query.endInventoryDate != null">
                AND inventory_date &lt;= #{query.endInventoryDate}
            </if>
            <if test="query.beginCreateTime != null">
                AND create_time &gt;= #{query.beginCreateTime}
            </if>
            <if test="query.endCreateTime != null">
                AND create_time &lt;= #{query.endCreateTime}
            </if>
            AND delete_flag = 0
        </where>
        order by create_time desc
    </select>
    
</mapper> 
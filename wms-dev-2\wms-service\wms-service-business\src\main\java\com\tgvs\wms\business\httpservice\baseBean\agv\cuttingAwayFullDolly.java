package com.tgvs.wms.business.httpservice.baseBean.agv;

import com.alibaba.fastjson.annotation.J<PERSON>NField;

public class cuttingAwayFullDolly {
    @J<PERSON><PERSON>ield(name = "TaskID")
    private String TaskID;

    @J<PERSON><PERSON>ield(name = "SiteNo")
    private String SiteNo;

    @<PERSON><PERSON><PERSON><PERSON>(name = "<PERSON>N<PERSON>")
    private String DollyNo;

    @J<PERSON>NField(name = "Content")
    private String Content;

    @J<PERSON><PERSON>ield(name = "ScrapFlag")
    private String ScrapFlag;

    @JSONField(name = "SysCode")
    private String SysCode;

    public void setTaskID(String TaskID) {
        this.TaskID = TaskID;
    }

    public void setSiteNo(String SiteNo) {
        this.SiteNo = SiteNo;
    }

    public void setDollyNo(String DollyNo) {
        this.DollyNo = DollyNo;
    }

    public void setContent(String Content) {
        this.Content = Content;
    }

    public void setScrapFlag(String ScrapFlag) {
        this.ScrapFlag = ScrapFlag;
    }

    public void setSysCode(String SysCode) {
        this.SysCode = SysCode;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof cuttingAwayFullDolly))
            return false;
        cuttingAwayFullDolly other = (cuttingAwayFullDolly)o;
        if (!other.canEqual(this))
            return false;
        Object this$TaskID = getTaskID(), other$TaskID = other.getTaskID();
        if ((this$TaskID == null) ? (other$TaskID != null) : !this$TaskID.equals(other$TaskID))
            return false;
        Object this$SiteNo = getSiteNo(), other$SiteNo = other.getSiteNo();
        if ((this$SiteNo == null) ? (other$SiteNo != null) : !this$SiteNo.equals(other$SiteNo))
            return false;
        Object this$DollyNo = getDollyNo(), other$DollyNo = other.getDollyNo();
        if ((this$DollyNo == null) ? (other$DollyNo != null) : !this$DollyNo.equals(other$DollyNo))
            return false;
        Object this$Content = getContent(), other$Content = other.getContent();
        if ((this$Content == null) ? (other$Content != null) : !this$Content.equals(other$Content))
            return false;
        Object this$ScrapFlag = getScrapFlag(), other$ScrapFlag = other.getScrapFlag();
        if ((this$ScrapFlag == null) ? (other$ScrapFlag != null) : !this$ScrapFlag.equals(other$ScrapFlag))
            return false;
        Object this$SysCode = getSysCode(), other$SysCode = other.getSysCode();
        return !((this$SysCode == null) ? (other$SysCode != null) : !this$SysCode.equals(other$SysCode));
    }

    protected boolean canEqual(Object other) {
        return other instanceof cuttingAwayFullDolly;
    }

    public int hashCode() {
        int PRIME = 59, result = 1;
        Object $TaskID = getTaskID();
        result = result * 59 + (($TaskID == null) ? 43 : $TaskID.hashCode());
        Object $SiteNo = getSiteNo();
        result = result * 59 + (($SiteNo == null) ? 43 : $SiteNo.hashCode());
        Object $DollyNo = getDollyNo();
        result = result * 59 + (($DollyNo == null) ? 43 : $DollyNo.hashCode());
        Object $Content = getContent();
        result = result * 59 + (($Content == null) ? 43 : $Content.hashCode());
        Object $ScrapFlag = getScrapFlag();
        result = result * 59 + (($ScrapFlag == null) ? 43 : $ScrapFlag.hashCode());
        Object $SysCode = getSysCode();
        return result * 59 + (($SysCode == null) ? 43 : $SysCode.hashCode());
    }

    public String toString() {
        return "cuttingAwayFullDolly(TaskID=" + getTaskID() + ", SiteNo=" + getSiteNo() + ", DollyNo=" + getDollyNo() + ", Content=" + getContent() + ", ScrapFlag=" + getScrapFlag() + ", SysCode=" + getSysCode() + ")";
    }

    public String getTaskID() {
        return this.TaskID;
    }

    public String getSiteNo() {
        return this.SiteNo;
    }

    public String getDollyNo() {
        return this.DollyNo;
    }

    public String getContent() {
        return this.Content;
    }

    public String getScrapFlag() {
        return this.ScrapFlag;
    }

    public String getSysCode() {
        return this.SysCode;
    }
}

package com.tgvs.wms.business.enums;

import com.tgvs.wms.common.util.oConvertUtils;
import lombok.Getter;

/**
 * 任务状态枚举
 */
@Getter
public enum enumTaskStatus {
    /**
     * 0-创建：初始状态，任务被创建但未开始执行
     */
    create(0, "create", "创建"),
    
    /**
     * 1-已分配：任务已被分配但尚未准备就绪
     */
    allot(1, "allot", "已分配"),
    
    /**
     * 2-就绪：任务准备就绪，等待执行
     */
    ready(2, "ready", "就绪"),
    
    /**
     * 3-执行中：任务正在执行过程中
     */
    execute(3, "execute", "执行中"),
    
    /**
     * 4-允许：任务执行被允许
     */
    allow(4, "allow", "允许"),
    
    /**
     * 5-完成：任务已完成
     */
    complete(5, "complete", "完成"),
    
    /**
     * 6-取消：任务已被取消
     */
    cancel(6, "cancel", "取消"),
    
    /**
     * 7-结束：任务已结束，最终状态
     */
    finish(7, "finish", "结束"),

    /**
     * 异常状态
     */
    error(99, "finish", "结束");

    private final Integer value;
    private final String code;
    private final String text;

    enumTaskStatus(int value, String code, String text) {
        this.value = value;
        this.code = code;
        this.text = text;
    }

    public static enumTaskStatus getByValue(int value) {
        if (oConvertUtils.isEmpty(value))
            return null;
        for (enumTaskStatus val : values()) {
            if (val.getValue().equals(value))
                return val;
        }
        return null;
    }
}

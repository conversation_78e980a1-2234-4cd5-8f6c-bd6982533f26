package com.tgvs.wms.business.modules.storage.entity;

import java.io.Serializable;
import java.util.Date;

import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.tgvs.wms.base.entity.BaseEntity;
import com.tgvs.wms.common.annotation.Dict;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "wms_cache_location对象", description = "缓存货位管理")
@TableName("wms_cache_location")
@Data
public class CacheLocation extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键")
    private String id;

    @Excel(name = "站点编码", width = 15.0D, dictTable = "wms_vritual_site", dicText = "site_code", dicCode = "site_code")
    @Dict(dictTable = "wms_vritual_site", dicText = "site_code", dicCode = "site_code")
    @ApiModelProperty("站点编码")
    private String siteCode;

    @Excel(name = "所属系统", width = 15.0D, dictTable = "wms_commncation_config", dicText = "sysname", dicCode = "sysid")
    @Dict(dictTable = "wms_commncation_config", dicText = "sysname", dicCode = "sysid")
    @ApiModelProperty("所属系统")
    private String sysid;

    @Excel(name = "货位编码", width = 15.0D)
    @ApiModelProperty("货位编码")
    private String code;

    @Excel(name = "货位", width = 15.0D)
    @ApiModelProperty("货位")
    private Integer location;

    @Excel(name = "层位总数", width = 15.0D)
    @ApiModelProperty("层位总数")
    private Integer levelsum;

    @Excel(name = "列位", width = 15.0D)
    @ApiModelProperty("列位")
    private Integer columnNumber;

    @Excel(name = "区域", width = 15.0D)
    @ApiModelProperty("区域")
    private String area;

    @Excel(name = "优先级", width = 15.0D)
    @ApiModelProperty("优先级")
    private Integer priority;

    @Excel(name = "锁定", width = 15.0D, dicCode = "locked_status")
    @Dict(dicCode = "locked_status")
    @ApiModelProperty("锁定")
    private Integer locked;

    @Excel(name = "智能设备", width = 15.0D, dictTable = "wms_commncation_device", dicText = "deviceName", dicCode = "deviceNo")
    @Dict(dictTable = "wms_commncation_device", dicText = "deviceName", dicCode = "deviceNo")
    @ApiModelProperty("智能设备")
    private String robot;

    @Excel(name = "货位状态", width = 15.0D, dicCode = "location_status")
    @Dict(dicCode = "location_status")
    @ApiModelProperty("货位状态")
    private Integer state;

    @Excel(name = "货位类型", width = 15.0D, dicCode = "location_height")
    @Dict(dicCode = "location_height")
    @ApiModelProperty("货位类型")
    private Integer type;

    @Excel(name = "容器号", width = 15.0D)
    @ApiModelProperty("容器号")
    private String palletCode;

    @Excel(name = "托板高度", width = 15.0D)
    @ApiModelProperty("托板高度")
    private Integer palletType;

    @Excel(name = "托板空实状态", width = 15.0D)
    @ApiModelProperty("托板空实状态")
    private Integer palletEmpty;

    @Excel(name = "布匹号", width = 15.0D)
    @ApiModelProperty("布匹号")
    private String goodsCode;

    @Excel(name = "订单编号", width = 15.0D)
    @ApiModelProperty("订单编号")
    private String orderCode;

    @Excel(name = "工单号", width = 15.0D)
    @ApiModelProperty("工单号")
    private String workNo;

    @ApiModelProperty("创建人")
    private String createBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("更新时间")
    private Date updateTime;

    @Excel(name = "删除状态", width = 15.0D)
    @ApiModelProperty("删除状态")
    private Integer delFlag;
}

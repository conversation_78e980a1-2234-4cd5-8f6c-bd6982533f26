package com.tgvs.wms.business.modules.auxiliaryInventory.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.tgvs.wms.base.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 辅料盘点单实体类 (简化版)
 * 
 * <AUTHOR>
 * @since 2025-07-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wms_auxiliary_inventory")
@ApiModel(value = "WmsAuxiliaryInventory对象", description = "辅料盘点单")
public class WmsAuxiliaryInventory extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("盘点单号")
    @TableField("inventory_no")
    private String inventoryNo;

    @ApiModelProperty("盘点单名称")
    @TableField("inventory_name")
    private String inventoryName;

    @ApiModelProperty("盘点类型：PART-抽盘、DIFF-差异盘点")
    @TableField("inventory_type")
    private String inventoryType;

    @ApiModelProperty("仓库编码")
    @TableField("warehouse_code")
    private String warehouseCode;

    @ApiModelProperty("仓库名称")
    @TableField("warehouse_name")
    private String warehouseName;

    @ApiModelProperty("状态：DRAFT-草稿、COUNTING-盘点中、COMPLETED-已完成、CANCELLED-已取消")
    @TableField("status")
    private String status;

    @ApiModelProperty("计划开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("plan_start_time")
    private Date planStartTime;

    @ApiModelProperty("计划结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("plan_end_time")
    private Date planEndTime;

    @ApiModelProperty("实际开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("actual_start_time")
    private Date actualStartTime;

    @ApiModelProperty("实际结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("actual_end_time")
    private Date actualEndTime;

    @ApiModelProperty("盘点总项数")
    @TableField("total_items")
    private Integer totalItems;

    @ApiModelProperty("已盘点项数")
    @TableField("counted_items")
    private Integer countedItems;

    @ApiModelProperty("差异项数")
    @TableField("diff_items")
    private Integer diffItems;

    @ApiModelProperty("备注")
    @TableField("remark")
    private String remark;

    @ApiModelProperty("创建人")
    @TableField("create_by")
    private String createBy;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("create_time")
    private Date createTime;

    @ApiModelProperty("更新人")
    @TableField("update_by")
    private String updateBy;

    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("update_time")
    private Date updateTime;

    @ApiModelProperty("删除标志：0-正常、1-已删除")
    @TableField("del_flag")
    @TableLogic
    private Integer delFlag;
}
package com.tgvs.wms.business.modules.system.dto;

import lombok.Data;

/**
 * API日志查询参数DTO
 */
@Data
public class ApiLogQueryDto {

    /**
     * 页码
     */
    private Integer page = 1;

    /**
     * 每页大小
     */
    private Integer limit = 10;

    /**
     * 模块名称（模糊查询）
     */
    private String module;

    /**
     * 请求URL（模糊查询）
     */
    private String requestUrl;

    /**
     * 客户端IP
     */
    private String clientIp;

    /**
     * 执行状态
     */
    private String status;

    /**
     * 请求方式
     */
    private String requestMethod;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 最小执行时间（毫秒）
     */
    private Long minExecutionTime;

    /**
     * 最大执行时间（毫秒）
     */
    private Long maxExecutionTime;

    /**
     * 关键字搜索（搜索模块、URL、IP等）
     */
    private String keyword;

    /**
     * 获取偏移量
     */
    public int getOffset() {
        return (page - 1) * limit;
    }
}

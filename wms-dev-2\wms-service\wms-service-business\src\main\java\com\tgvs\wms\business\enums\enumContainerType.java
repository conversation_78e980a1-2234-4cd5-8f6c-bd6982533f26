package com.tgvs.wms.business.enums;

import lombok.Getter;

/**
 * 容器类型枚举
 */
@Getter
public enum enumContainerType {
    
    BOX(1, "料箱", "用于存放小件物料"),
    TRAY(2, "托盘", "用于存放大件物料"),
    RACK(3, "料架", "用于存放超大型物料"),
    BIN(4, "料仓", "用于存放散装物料");
    
    /**
     * 类型值
     */
    private final int value;
    
    /**
     * 类型名称
     */
    private final String name;
    
    /**
     * 类型描述
     */
    private final String description;
    
    enumContainerType(int value, String name, String description) {
        this.value = value;
        this.name = name;
        this.description = description;
    }
    
    /**
     * 根据类型值获取枚举
     */
    public static enumContainerType getByValue(int value) {
        for (enumContainerType type : values()) {
            if (type.getValue() == value) {
                return type;
            }
        }
        return null;
    }
    
    /**
     * 根据类型名称获取枚举
     */
    public static enumContainerType getByName(String name) {
        for (enumContainerType type : values()) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }
    
    /**
     * 判断是否为料箱
     */
    public boolean isBox() {
        return this == BOX;
    }
    
    /**
     * 判断是否为托盘
     */
    public boolean isTray() {
        return this == TRAY;
    }
    
    /**
     * 判断是否为料架
     */
    public boolean isRack() {
        return this == RACK;
    }
    
    /**
     * 判断是否为料仓
     */
    public boolean isBin() {
        return this == BIN;
    }
    
    /**
     * 获取容器类型对应的格子数
     */
    public int getGridCount() {
        switch (this) {
            case BOX:
                return 6; // 默认6格
            case TRAY:
                return 1; // 托盘只有1格
            case RACK:
                return 10; // 料架10格
            case BIN:
                return 1; // 料仓1格
            default:
                return 0;
        }
    }
    
    /**
     * 获取容器类型对应的容量系数
     */
    public double getCapacityFactor() {
        switch (this) {
            case BOX:
                return 1.0; // 标准容量
            case TRAY:
                return 2.0; // 2倍容量
            case RACK:
                return 5.0; // 5倍容量
            case BIN:
                return 10.0; // 10倍容量
            default:
                return 1.0;
        }
    }
} 
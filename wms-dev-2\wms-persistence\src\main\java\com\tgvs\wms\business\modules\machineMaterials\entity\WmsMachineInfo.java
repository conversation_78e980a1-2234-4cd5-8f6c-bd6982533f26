package com.tgvs.wms.business.modules.machineMaterials.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.tgvs.wms.base.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 机物料基础信息
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wms_machine_info")
public class WmsMachineInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    private String id;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 大类
     */
    private String bigClass;

    /**
     * 小类
     */
    private String smallClass;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 规格
     */
    private String specification;

    /**
     * 单位
     */
    private String unit;

    /**
     * 容积（箱/托盘容量）
     */
    private Integer capacity;

    /**
     * 优先料箱类型
     */
    private Integer priorityContainerType;
    /**
     * 备注1
     */
    private String notes1;
    /**
     * 备注2
     */
    private String notes2;
    /**
     * 备注3
     */
    private String notes3;
} 
package com.tgvs.wms.business.httpservice.baseBean.agv;

import com.alibaba.fastjson.annotation.J<PERSON>NField;

public class cuttingCallAgv {
    @J<PERSON><PERSON>ield(name = "TaskID")
    private String TaskID;

    @J<PERSON><PERSON><PERSON>(name = "SiteNo")
    private String SiteNo;

    @<PERSON><PERSON><PERSON><PERSON>(name = "<PERSON><PERSON>hNo")
    private String ClothNo;

    @JSONField(name = "DollyFlag")
    private String DollyFlag;

    @JSONField(name = "EmptyDolly")
    private String EmptyDolly;

    @J<PERSON>NField(name = "EmptyContent")
    private String EmptyContent;

    @J<PERSON><PERSON>ield(name = "SysCode")
    private String SysCode;

    public void setTaskID(String TaskID) {
        this.TaskID = TaskID;
    }

    public void setSiteNo(String SiteNo) {
        this.SiteNo = SiteNo;
    }

    public void setClothNo(String ClothNo) {
        this.ClothNo = ClothNo;
    }

    public void setDollyFlag(String DollyFlag) {
        this.DollyFlag = DollyFlag;
    }

    public void setEmptyDolly(String EmptyDolly) {
        this.EmptyDolly = EmptyDolly;
    }

    public void setEmptyContent(String EmptyContent) {
        this.EmptyContent = EmptyContent;
    }

    public void setSysCode(String SysCode) {
        this.SysCode = SysCode;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof cuttingCallAgv))
            return false;
        cuttingCallAgv other = (cuttingCallAgv)o;
        if (!other.canEqual(this))
            return false;
        Object this$TaskID = getTaskID(), other$TaskID = other.getTaskID();
        if ((this$TaskID == null) ? (other$TaskID != null) : !this$TaskID.equals(other$TaskID))
            return false;
        Object this$SiteNo = getSiteNo(), other$SiteNo = other.getSiteNo();
        if ((this$SiteNo == null) ? (other$SiteNo != null) : !this$SiteNo.equals(other$SiteNo))
            return false;
        Object this$ClothNo = getClothNo(), other$ClothNo = other.getClothNo();
        if ((this$ClothNo == null) ? (other$ClothNo != null) : !this$ClothNo.equals(other$ClothNo))
            return false;
        Object this$DollyFlag = getDollyFlag(), other$DollyFlag = other.getDollyFlag();
        if ((this$DollyFlag == null) ? (other$DollyFlag != null) : !this$DollyFlag.equals(other$DollyFlag))
            return false;
        Object this$EmptyDolly = getEmptyDolly(), other$EmptyDolly = other.getEmptyDolly();
        if ((this$EmptyDolly == null) ? (other$EmptyDolly != null) : !this$EmptyDolly.equals(other$EmptyDolly))
            return false;
        Object this$EmptyContent = getEmptyContent(), other$EmptyContent = other.getEmptyContent();
        if ((this$EmptyContent == null) ? (other$EmptyContent != null) : !this$EmptyContent.equals(other$EmptyContent))
            return false;
        Object this$SysCode = getSysCode(), other$SysCode = other.getSysCode();
        return !((this$SysCode == null) ? (other$SysCode != null) : !this$SysCode.equals(other$SysCode));
    }

    protected boolean canEqual(Object other) {
        return other instanceof cuttingCallAgv;
    }

    public int hashCode() {
        int PRIME = 59, result = 1;
        Object $TaskID = getTaskID();
        result = result * 59 + (($TaskID == null) ? 43 : $TaskID.hashCode());
        Object $SiteNo = getSiteNo();
        result = result * 59 + (($SiteNo == null) ? 43 : $SiteNo.hashCode());
        Object $ClothNo = getClothNo();
        result = result * 59 + (($ClothNo == null) ? 43 : $ClothNo.hashCode());
        Object $DollyFlag = getDollyFlag();
        result = result * 59 + (($DollyFlag == null) ? 43 : $DollyFlag.hashCode());
        Object $EmptyDolly = getEmptyDolly();
        result = result * 59 + (($EmptyDolly == null) ? 43 : $EmptyDolly.hashCode());
        Object $EmptyContent = getEmptyContent();
        result = result * 59 + (($EmptyContent == null) ? 43 : $EmptyContent.hashCode());
        Object $SysCode = getSysCode();
        return result * 59 + (($SysCode == null) ? 43 : $SysCode.hashCode());
    }

    public String toString() {
        return "cuttingCallAgv(TaskID=" + getTaskID() + ", SiteNo=" + getSiteNo() + ", ClothNo=" + getClothNo() + ", DollyFlag=" + getDollyFlag() + ", EmptyDolly=" + getEmptyDolly() + ", EmptyContent=" + getEmptyContent() + ", SysCode=" + getSysCode() + ")";
    }

    public String getTaskID() {
        return this.TaskID;
    }

    public String getSiteNo() {
        return this.SiteNo;
    }

    public String getClothNo() {
        return this.ClothNo;
    }

    public String getDollyFlag() {
        return this.DollyFlag;
    }

    public String getEmptyDolly() {
        return this.EmptyDolly;
    }

    public String getEmptyContent() {
        return this.EmptyContent;
    }

    public String getSysCode() {
        return this.SysCode;
    }
}

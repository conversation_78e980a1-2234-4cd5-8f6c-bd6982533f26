package com.tgvs.wms.common.trace.logging.layout;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.Map;

import com.tgvs.wms.common.trace.logging.util.LoggerUtils;

import ch.qos.logback.classic.pattern.ExtendedThrowableProxyConverter;
import ch.qos.logback.classic.pattern.ThrowableHandlingConverter;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.CoreConstants;
import ch.qos.logback.core.LayoutBase;
import lombok.Getter;
import lombok.Setter;

public class LogStandardLayout  extends LayoutBase<ILoggingEvent> {
    private final ThrowableHandlingConverter throwableHandlingConverter;

    @Getter
    @Setter
    private String format;

    private static DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");

    public LogStandardLayout(){
        throwableHandlingConverter = new ExtendedThrowableProxyConverter();
        throwableHandlingConverter.start();
    }
    @Override
    public String doLayout(ILoggingEvent event) {
        return doLayoutCommon(event);
    }

    private String doLayoutCommon(ILoggingEvent event){
        Map<String, String> fields = new LinkedHashMap<>();

        Instant instant = Instant.ofEpochMilli(event.getTimeStamp());
        ZoneId zoneId = ZoneId.systemDefault();
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, zoneId);

        fields.put("timeStamp", dateFormatter.format(localDateTime));
        fields.put("requestId", LoggerUtils.getRequestIdFromEvent(event));
        fields.put("level", event.getLevel().levelStr);
        fields.put("threadName", event.getThreadName());
        fields.put("loggerName", event.getLoggerName());

        return applyFormat(fields, event);
    }

    private String applyFormat(Map<String, String> fields, ILoggingEvent event){
        if("text".equalsIgnoreCase(format)){
            return applyTextFormat(fields.entrySet().iterator(), event);
        }else{
            return applyJsonFormat(fields.entrySet().iterator(), event);
        }
    }

    private String applyTextFormat(Iterator<Map.Entry<String, String>> entries, ILoggingEvent event){
        StringBuilder sb = new StringBuilder();

        while (entries.hasNext()){
            String value = entries.next().getValue();
            sb.append("[").append(value == null ? "" : value).append("]");
        }
        sb.append(event.getFormattedMessage());
        sb.append(CoreConstants.LINE_SEPARATOR);

        if(event.getThrowableProxy() != null){
            sb.append(throwableHandlingConverter.convert(event));
        }
        return sb.toString();
    }

    private String applyJsonFormat(Iterator<Map.Entry<String, String>> entries, ILoggingEvent event){
        StringBuilder sb = new StringBuilder();
        sb.append("{");
        while (entries.hasNext()){
            Map.Entry<String, String> entry = entries.next();
            if(entry.getValue() != null){
                appendValue(sb, entry.getKey(), entry.getValue(), sb.length() != 1);
            }
        }

        appendValue(sb, LoggerUtils.MSG_KEY, event.getFormattedMessage(), true);

        if(event.getThrowableProxy() != null){
            appendValue(sb, "exception", throwableHandlingConverter.convert(event), true);
        }

        sb.append("}").append(CoreConstants.LINE_SEPARATOR);
        return sb.toString();
    }

    private void appendValue(StringBuilder sb, String key, String value, boolean addPrefix){
        if(value == null){
            return;
        }
        if(addPrefix){
            sb.append(",");
        }
        sb.append(LoggerUtils.quote(key)).append(":");
        sb.append(LoggerUtils.quote(value));
    }
}

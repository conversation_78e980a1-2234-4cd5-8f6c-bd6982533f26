package com.tgvs.wms.business.enums;


import com.tgvs.wms.common.util.oConvertUtils;
import lombok.Getter;


@Getter
public enum enumTaskType {
    /** 任务类型：0采购入库，1、一般入库，2生产退料回库，3领料出库，4调拨出库，5采购退货出库，6指定出库，7.指定入库，8.紧急出库。9、调拨入库 10.盘点出库 */
    purchaseInbound(0, "purchaseInbound", "采购入库"),
    generalInbound(1, "generalInbound", "一般入库"),
    returnMaterialInbound(2, "returnMaterialInbound", "生产退料回库"),
    pickingOutbound(3, "pickingOutbound", "领料出库"),
    transferOutbound(4, "transferOutbound", "调拨出库"),
    purchaseReturnOutbound(5, "purchaseReturnOutbound", "采购退货出库"),
    manualOutbound(6, "manualOutbound", "指定出库"),
    manualInbound(7, "manualInbound", "指定入库"),
    emergencyOutbound(8, "emergencyOutbound", "紧急出库"),
    transferInbound(9, "transferInbound", "调拨入库"),
    inventoryOutbound(10, "inventoryOutbound", "盘点出库"),
    move(Integer.valueOf(15), "move", "移库");

    private Integer value;

    private String code;

    private String text;

    enumTaskType(Integer value, String code, String text) {
        this.value = value;
        this.code = code;
        this.text = text;
    }

    public static enumTaskType getByValue(int value) {
        if (oConvertUtils.isEmpty(Integer.valueOf(value)))
            return null;
        for (enumTaskType val : values()) {
            if (val.getValue().equals(Integer.valueOf(value)))
                return val;
        }
        return null;
    }

    public static enumTaskType toEnum(Integer Value) {
        for (enumTaskType e : values()) {
            if (e.getValue().equals(Value))
                return e;
        }
        return null;
    }

    public static enumTaskType toEnum(String Value) {
        for (enumTaskType e : values()) {
            if (e.getValue().toString().equals(Value))
                return e;
        }
        return null;
    }
}

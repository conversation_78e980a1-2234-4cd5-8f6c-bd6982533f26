package com.tgvs.wms.business.modules.auxiliaryInventory.Controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tgvs.wms.base.controller.BaseController;
import com.tgvs.wms.business.modules.auxiliaryInventory.entity.WmsAuxiliaryInventory;
import com.tgvs.wms.business.modules.auxiliaryInventory.entity.WmsAuxiliaryInventoryDetail;
import com.tgvs.wms.business.modules.auxiliaryInventory.service.IWmsAuxiliaryInventoryService;
import com.tgvs.wms.common.annotation.AutoLog;
import com.tgvs.wms.common.constant.Result;
import com.tgvs.wms.common.core.domain.QueryModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 辅料盘点管理控制器 (简化版 - 符合WMS框架标准)
 */
@Api(tags = {"辅料盘点管理"})
@RestController
@RequestMapping({"/auxiliary/inventory"})
public class WmsAuxiliaryInventoryController extends BaseController<WmsAuxiliaryInventory, IWmsAuxiliaryInventoryService> {

    private static final Logger log = LoggerFactory.getLogger(WmsAuxiliaryInventoryController.class);

    @Autowired
    private IWmsAuxiliaryInventoryService inventoryService;

    // ========== 标准CRUD操作 ==========

    /**
     * 分页查询盘点单
     */
    @ApiOperation(value = "辅料盘点-分页列表查询", notes = "辅料盘点-分页列表查询")
    @PostMapping({"/list"})
    public Result<?> queryPageList(@RequestBody(required = false) QueryModel queryModel) {
        IPage<WmsAuxiliaryInventory> list = inventoryService.pageList(queryModel);
        Result result = Result.ok(list.getRecords());
        result.setTotal(list.getTotal());
        return result;
    }

    /**
     * 新增盘点单
     */
    @AutoLog("辅料盘点-添加")
    @ApiOperation(value = "辅料盘点-添加", notes = "辅料盘点-添加")
    @RequiresPermissions({"auxiliary:inventory:add"})
    @PostMapping({"/add"})
    public Result<?> add(@RequestBody WmsAuxiliaryInventory inventory) {
        inventoryService.createInventory(inventory);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑盘点单
     */
    @AutoLog("辅料盘点-编辑")
    @ApiOperation(value = "辅料盘点-编辑", notes = "辅料盘点-编辑")
    @RequiresPermissions({"auxiliary:inventory:edit"})
    @PostMapping({"/edit"})
    public Result<?> edit(@RequestBody WmsAuxiliaryInventory inventory) {
        inventoryService.updateInventory(inventory);
        return Result.OK("编辑成功!");
    }

    /**
     * 删除盘点单
     */
    @AutoLog("辅料盘点-通过id删除")
    @ApiOperation(value = "辅料盘点-通过id删除", notes = "辅料盘点-通过id删除")
    @RequiresPermissions({"auxiliary:inventory:delete"})
    @PostMapping({"/delete"})
    public Result<?> delete(@RequestBody String id) {
        inventoryService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     */
    @AutoLog("辅料盘点-批量删除")
    @ApiOperation(value = "辅料盘点-批量删除", notes = "辅料盘点-批量删除")
    @RequiresPermissions({"auxiliary:inventory:deleteBatch"})
    @PostMapping({"/deleteBatch"})
    public Result<?> deleteBatch(@RequestBody String ids) {
        inventoryService.deleteInventory(ids.split(","));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     */
    @AutoLog("辅料盘点-通过id查询")
    @ApiOperation(value = "辅料盘点-通过id查询", notes = "辅料盘点-通过id查询")
    @PostMapping({"/queryById"})
    public Result<?> queryById(@RequestBody String id) {
        WmsAuxiliaryInventory inventory = inventoryService.getById(id);
        if (inventory == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(inventory);
    }

    // ========== 盘点业务操作 ==========

    /**
     * 从库存创建盘点任务
     */
    @AutoLog("辅料盘点-从库存创建")
    @ApiOperation(value = "辅料盘点-从库存创建", notes = "基于选中库存创建盘点任务")
    @RequiresPermissions({"auxiliary:inventory:add"})
    @PostMapping({"/createFromStock"})
    public Result<?> createFromStock(@RequestBody Map<String, Object> request) {
        try {
            String inventoryId = inventoryService.createInventoryFromStock(request);
            return Result.OK(inventoryId+"盘点任务创建成功");
        } catch (Exception e) {
            log.error("创建盘点任务失败: {}", e.getMessage());
            return Result.error("创建盘点任务失败：" + e.getMessage());
        }
    }

    /**
     * 获取盘点明细
     */
    @ApiOperation(value = "辅料盘点-获取明细", notes = "获取盘点单明细列表")
    @PostMapping({"/queryDetails"})
    public Result<?> queryDetails(@RequestBody(required = false) QueryModel queryModel) {
        // 从查询模型中获取inventoryId
        String inventoryId = null;
        if (queryModel != null && queryModel.getSearchParams() != null) {
            inventoryId =queryModel.getSearchParams().get("inventoryId");
        }
        
        if (inventoryId == null || inventoryId.trim().isEmpty()) {
            return Result.error("缺少盘点单ID参数");
        }
        
        List<WmsAuxiliaryInventoryDetail> details = inventoryService.getInventoryDetails(inventoryId);
        return Result.OK(details);
    }

    /**
     * AGV出库盘点
     */
    @AutoLog("辅料盘点-AGV出库")
    @ApiOperation(value = "辅料盘点-AGV出库", notes = "调用AGV进行物料出库到盘点工位")
    @RequiresPermissions({"auxiliary:inventory:count"})
    @PostMapping({"/agvOutbound"})
    public Result<?> agvOutbound(@RequestBody String detailId) {
        try {
            String taskId = inventoryService.createAgvOutboundTask(detailId);
            return Result.OK("AGV出库任务创建成功"+taskId);
        } catch (Exception e) {
            log.error("AGV出库任务创建失败: {}", e.getMessage());
            return Result.error("AGV出库任务创建失败：" + e.getMessage());
        }
    }

    /**
     * 创建AGV出库任务 (前端调用的API名称)
     */
    @AutoLog("辅料盘点-创建AGV出库任务")
    @ApiOperation(value = "辅料盘点-创建AGV出库任务", notes = "调用AGV进行物料出库到盘点工位")
    @RequiresPermissions({"auxiliary:inventory:count"})
    @PostMapping({"/createAgvOutboundTask"})
    public Result<?> createAgvOutboundTask(@RequestBody String detailId) {
        try {
            String taskId = inventoryService.createAgvOutboundTask(detailId);
            return Result.OK(taskId);
        } catch (Exception e) {
            log.error("AGV出库任务创建失败: {}", e.getMessage());
            return Result.error("AGV出库任务创建失败：" + e.getMessage());
        }
    }

    /**
     * 提交盘点结果
     */
    @AutoLog("辅料盘点-提交结果")
    @ApiOperation(value = "辅料盘点-提交结果", notes = "提交实盘数量和差异信息")
    @RequiresPermissions({"auxiliary:inventory:count"})
    @PostMapping({"/submitCount"})
    public Result<?> submitCount(@RequestBody Map<String, Object> request) {
        try {
            inventoryService.submitInventoryCount(request);
            return Result.OK("盘点结果提交成功!");
        } catch (Exception e) {
            log.error("提交盘点结果失败: {}", e.getMessage());
            return Result.error("提交盘点结果失败：" + e.getMessage());
        }
    }

    /**
     * 提交盘点数量 (前端调用的API名称)
     */
    @AutoLog("辅料盘点-提交盘点数量")
    @ApiOperation(value = "辅料盘点-提交盘点数量", notes = "提交实盘数量和差异信息")
    @RequiresPermissions({"auxiliary:inventory:count"})
    @PostMapping({"/submitInventoryCount"})
    public Result<?> submitInventoryCount(@RequestBody Map<String, Object> request) {
        try {
            inventoryService.submitInventoryCount(request);
            return Result.OK("盘点结果提交成功!");
        } catch (Exception e) {
            log.error("提交盘点结果失败: {}", e.getMessage());
            return Result.error("提交盘点结果失败：" + e.getMessage());
        }
    }

    /**
     * 查询AGV任务状态
     */
    @ApiOperation(value = "辅料盘点-AGV状态", notes = "查询AGV任务状态")
    @PostMapping({"/queryAgvStatus"})
    public Result<?> queryAgvStatus(@RequestBody String taskOrder) {
        Map<String, Object> status = inventoryService.getAgvTaskStatus(taskOrder);
        return Result.OK(status);
    }

    /**
     * 获取AGV任务状态 (前端调用的API名称)
     */
    @ApiOperation(value = "辅料盘点-获取AGV任务状态", notes = "查询AGV任务状态")
    @PostMapping({"/getAgvTaskStatus"})
    public Result<?> getAgvTaskStatus(@RequestBody String taskOrder) {
        Map<String, Object> status = inventoryService.getAgvTaskStatus(taskOrder);
        return Result.OK(status);
    }

    /**
     * 批量创建AGV出库任务
     */
    @AutoLog("辅料盘点-批量AGV出库")
    @ApiOperation(value = "辅料盘点-批量AGV出库", notes = "批量调用AGV进行物料出库到盘点工位")
    @RequiresPermissions({"auxiliary:inventory:count"})
    @PostMapping({"/batchCreateAgvOutboundTask"})
    public Result<?> batchCreateAgvOutboundTask(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<String> detailIds = (List<String>) request.get("detailIds");
            if (detailIds == null || detailIds.isEmpty()) {
                return Result.error("缺少盘点明细ID列表");
            }
            
            List<String> taskIds = new ArrayList<>();
            for (String detailId : detailIds) {
                String taskId = inventoryService.createAgvOutboundTask(detailId);
                taskIds.add(taskId);
            }
            
            return Result.OK("批量AGV出库任务创建成功: " + taskIds.size() + " 个任务");
        } catch (Exception e) {
            log.error("批量AGV出库任务创建失败: {}", e.getMessage());
            return Result.error("批量AGV出库任务创建失败：" + e.getMessage());
        }
    }

    /**
     * 重置盘点明细
     */
    @AutoLog("辅料盘点-重置明细")
    @ApiOperation(value = "辅料盘点-重置明细", notes = "重置盘点明细状态，用于重新盘点")
    @RequiresPermissions({"auxiliary:inventory:count"})
    @PostMapping({"/resetInventoryDetail"})
    public Result<?> resetInventoryDetail(@RequestBody String detailId) {
        try {
            boolean success = inventoryService.resetInventoryDetail(detailId);
            if (success) {
                return Result.OK("盘点明细已重置");
            } else {
                return Result.error("重置盘点明细失败");
            }
        } catch (Exception e) {
            log.error("重置盘点明细失败: {}", e.getMessage());
            return Result.error("重置盘点明细失败：" + e.getMessage());
        }
    }

    /**
     * 删除盘点明细
     */
    @AutoLog("辅料盘点-删除明细")
    @ApiOperation(value = "辅料盘点-删除明细", notes = "删除盘点明细记录")
    @RequiresPermissions({"auxiliary:inventory:edit"})
    @PostMapping({"/deleteDetail"})
    public Result<?> deleteDetail(@RequestBody String detailId) {
        try {
            boolean success = inventoryService.deleteInventoryDetail(detailId);
            if (success) {
                return Result.OK("盘点明细删除成功");
            } else {
                return Result.error("删除盘点明细失败");
            }
        } catch (Exception e) {
            log.error("删除盘点明细失败: {}", e.getMessage());
            return Result.error("删除盘点明细失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除盘点明细
     */
    @AutoLog("辅料盘点-批量删除明细")
    @ApiOperation(value = "辅料盘点-批量删除明细", notes = "批量删除盘点明细记录")
    @RequiresPermissions({"auxiliary:inventory:edit"})
    @PostMapping({"/deleteDetails"})
    public Result<?> deleteDetails(@RequestBody String detailIds) {
        try {
            inventoryService.deleteInventoryDetails(detailIds.split(","));
            return Result.OK("批量删除盘点明细成功");
        } catch (Exception e) {
            log.error("批量删除盘点明细失败: {}", e.getMessage());
            return Result.error("批量删除盘点明细失败：" + e.getMessage());
        }
    }

    /**
     * 开始盘点
     */
    @AutoLog("辅料盘点-开始")
    @ApiOperation(value = "辅料盘点-开始", notes = "开始盘点单，将状态从草稿改为盘点中")
    @RequiresPermissions({"auxiliary:inventory:start"})
    @PostMapping({"/start"})
    public Result<?> start(@RequestBody String inventoryId) {
        try {
            inventoryService.startInventory(inventoryId);
            return Result.OK("盘点已开始!");
        } catch (Exception e) {
            log.error("开始盘点失败: {}", e.getMessage());
            return Result.error("开始盘点失败：" + e.getMessage());
        }
    }

    /**
     * 完成盘点
     */
    @AutoLog("辅料盘点-完成")
    @ApiOperation(value = "辅料盘点-完成", notes = "完成整个盘点单")
    @RequiresPermissions({"auxiliary:inventory:complete"})
    @PostMapping({"/complete"})
    public Result<?> complete(@RequestBody String inventoryId) {
        try {
            inventoryService.completeInventory(inventoryId);
            return Result.OK("盘点完成!");
        } catch (Exception e) {
            log.error("完成盘点失败: {}", e.getMessage());
            return Result.error("完成盘点失败：" + e.getMessage());
        }
    }

    /**
     * 添加库存到盘点单
     */
    @AutoLog("辅料盘点-添加库存")
    @ApiOperation(value = "辅料盘点-添加库存", notes = "向盘点单添加库存明细")
    @RequiresPermissions({"auxiliary:inventory:edit"})
    @PostMapping({"/addStockToInventory"})
    public Result<?> addStockToInventory(@RequestBody Map<String, Object> request) {
        try {
            String inventoryId = (String) request.get("inventoryId");
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> stockList = (List<Map<String, Object>>) request.get("stockList");
            
            // 转换为明细实体
            List<WmsAuxiliaryInventoryDetail> details = new ArrayList<>();
            for (Map<String, Object> stock : stockList) {
                WmsAuxiliaryInventoryDetail detail = new WmsAuxiliaryInventoryDetail();
                detail.setInventoryId(inventoryId);
                detail.setMaterialCode((String) stock.get("materialCode"));
                detail.setMaterialName((String) stock.get("materialName"));
                detail.setLocationCode((String) stock.get("code"));
                detail.setBoxNo((String) stock.get("boxNo"));
                detail.setSystemQty(stock.get("materialQuantity") != null ? 
                    new java.math.BigDecimal(stock.get("materialQuantity").toString()) : java.math.BigDecimal.ZERO);
                detail.setMaterialColor((String) stock.get("materialColor"));
                detail.setMaterialSize((String) stock.get("materialSize"));
                detail.setMaterialSpec((String) stock.get("materialModel"));
                detail.setUnit((String) stock.get("unit"));
                detail.setStatus("PENDING");
                detail.setCreateBy(com.tgvs.wms.common.util.ShiroUtils.getLoginName());
                detail.setCreateTime(new Date());
                details.add(detail);
            }
            
            boolean success = inventoryService.addStockToInventory(inventoryId, details);
            if (success) {
                return Result.OK("库存添加成功");
            } else {
                return Result.error("库存添加失败");
            }
        } catch (Exception e) {
            log.error("添加库存到盘点单失败: {}", e.getMessage());
            return Result.error("添加库存失败：" + e.getMessage());
        }
    }
}
package com.tgvs.wms.business.delegate;

public class ConcreteNotifier extends Notifier {
    public void addListener(Object object, String methodName, Object... args) {
        getEventHandler().addEvent(object, methodName, args);
    }

    public void notifyX() {
        try {
            getEventHandler().notifyX();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

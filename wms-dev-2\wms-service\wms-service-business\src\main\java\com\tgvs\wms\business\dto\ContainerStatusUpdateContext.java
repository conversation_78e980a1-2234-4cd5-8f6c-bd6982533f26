package com.tgvs.wms.business.dto;

import com.tgvs.wms.business.enums.UpdateTrigger;
import lombok.Builder;
import lombok.Data;

/**
 * 容器状态更新上下文
 * 用于传递容器状态更新所需的各种参数
 */
@Data
@Builder
public class ContainerStatusUpdateContext {
    
    /**
     * 已使用格子数（料箱专用）
     */
    private Integer usedGrids;
    
    /**
     * 总格子数（料箱专用）
     */
    private Integer totalGrids;
    
    /**
     * 填充百分比（托盘专用，0-100）
     */
    private Integer fillPercentage;
    
    /**
     * 物料种类数
     */
    private Integer materialTypeCount;
    
    /**
     * 容器类型（1-料箱，2-托盘）
     */
    private Integer containerType;
    
    /**
     * 更新触发器
     */
    private UpdateTrigger updateTrigger;
    
    /**
     * 操作人员ID
     */
    private String operatorId;
    
    /**
     * 是否为智能仓
     */
    private Boolean isSmartWarehouse;
    
    /**
     * 是否为虚拟仓库容器
     */
    private Boolean isVirtualContainer;

    /**
     * 合约号（容器固有属性，不应在入库时修改）
     */
    private String contractNo;

    /**
     * 容器格数类型（容器固有属性，不应在入库时修改）
     * 1-一格箱 2-两格箱 3-三格箱 4-四格箱 6-六格箱 8-八格箱
     */
    private Integer boxContainerType;

    /**
     * 备注信息
     */
    private String remark;
    
    /**
     * 创建料箱更新上下文
     */
    public static ContainerStatusUpdateContext forBox(int usedGrids, int totalGrids, UpdateTrigger trigger) {
        return ContainerStatusUpdateContext.builder()
                .usedGrids(usedGrids)
                .totalGrids(totalGrids)
                .containerType(1)
                .updateTrigger(trigger)
                .build();
    }

    /**
     * 创建料箱更新上下文（包含固有属性）
     */
    public static ContainerStatusUpdateContext forBox(int usedGrids, int totalGrids, UpdateTrigger trigger,
                                                     String contractNo, Integer boxContainerType) {
        return ContainerStatusUpdateContext.builder()
                .usedGrids(usedGrids)
                .totalGrids(totalGrids)
                .containerType(1)
                .updateTrigger(trigger)
                .contractNo(contractNo)
                .boxContainerType(boxContainerType)
                .build();
    }

    /**
     * 创建托盘更新上下文（基于填充百分比）
     */
    public static ContainerStatusUpdateContext forTray(int fillPercentage, int materialTypeCount, UpdateTrigger trigger) {
        return ContainerStatusUpdateContext.builder()
                .fillPercentage(fillPercentage)
                .materialTypeCount(materialTypeCount)
                .containerType(2)
                .updateTrigger(trigger)
                .build();
    }

    /**
     * 创建托盘更新上下文（包含固有属性）
     */
    public static ContainerStatusUpdateContext forTray(int fillPercentage, int materialTypeCount, UpdateTrigger trigger,
                                                      String contractNo, Integer boxContainerType) {
        return ContainerStatusUpdateContext.builder()
                .fillPercentage(fillPercentage)
                .materialTypeCount(materialTypeCount)
                .containerType(2)
                .updateTrigger(trigger)
                .contractNo(contractNo)
                .boxContainerType(boxContainerType)
                .build();
    }
    
    /**
     * 创建托盘预装阶段更新上下文
     */
    public static ContainerStatusUpdateContext forTrayPrebox(int materialTypeCount, UpdateTrigger trigger) {
        return ContainerStatusUpdateContext.builder()
                .materialTypeCount(materialTypeCount)
                .containerType(2)
                .updateTrigger(trigger)
                .build();
    }
    
    /**
     * 设置操作人员
     */
    public ContainerStatusUpdateContext withOperator(String operatorId) {
        this.operatorId = operatorId;
        return this;
    }
    
    /**
     * 设置智能仓标识
     */
    public ContainerStatusUpdateContext withSmartWarehouse(boolean isSmartWarehouse) {
        this.isSmartWarehouse = isSmartWarehouse;
        return this;
    }
    
    /**
     * 设置虚拟容器标识
     */
    public ContainerStatusUpdateContext withVirtualContainer(boolean isVirtualContainer) {
        this.isVirtualContainer = isVirtualContainer;
        return this;
    }
    
    /**
     * 设置备注
     */
    public ContainerStatusUpdateContext withRemark(String remark) {
        this.remark = remark;
        return this;
    }
    
    /**
     * 验证上下文参数的有效性
     */
    public boolean isValid() {
        if (containerType == null || updateTrigger == null) {
            return false;
        }

        // 料箱验证逻辑
        if (containerType == 1) {
            // 对于INBOUND_COMPLETE触发器，允许ContainerStatusManager自动计算格子信息
            if (updateTrigger == UpdateTrigger.INBOUND_COMPLETE) {
                return true; // 自动计算模式
            }
            // 其他触发器需要明确的格子信息
            return usedGrids != null && totalGrids != null && totalGrids > 0;
        }

        // 托盘根据触发器验证不同参数
        if (containerType == 2) {
            if (updateTrigger == UpdateTrigger.BINDING_INBOUND) {
                // 绑定入库需要填充百分比
                return fillPercentage != null && fillPercentage >= 0 && fillPercentage <= 100;
            } else if (updateTrigger == UpdateTrigger.PREBOX_STAGE) {
                // 预装阶段需要物料种类数
                return materialTypeCount != null && materialTypeCount >= 0;
            }
        }

        return true;
    }
    
    /**
     * 获取描述信息
     */
    public String getDescription() {
        StringBuilder desc = new StringBuilder();
        desc.append("容器类型=").append(containerType == 1 ? "料箱" : "托盘");
        desc.append(", 触发器=").append(updateTrigger != null ? updateTrigger.getName() : "未知");
        
        if (usedGrids != null && totalGrids != null) {
            desc.append(", 格子使用=").append(usedGrids).append("/").append(totalGrids);
        }
        
        if (fillPercentage != null) {
            desc.append(", 填充百分比=").append(fillPercentage).append("%");
        }
        
        if (materialTypeCount != null) {
            desc.append(", 物料种类数=").append(materialTypeCount);
        }
        
        return desc.toString();
    }
}

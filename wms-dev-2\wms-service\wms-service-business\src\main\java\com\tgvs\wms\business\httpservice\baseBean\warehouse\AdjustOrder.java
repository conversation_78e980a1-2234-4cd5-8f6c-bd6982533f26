package com.tgvs.wms.business.httpservice.baseBean.warehouse;


import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

public class AdjustOrder {
    private Integer id;

    private String messageName;

    private String boxId;

    @JsonProperty("wmsId")
    private String wmsId;

    @JsonProperty("allowLevel")
    private List<Integer> allowlevel;

    public void setId(Integer id) {
        this.id = id;
    }

    public void setMessageName(String messageName) {
        this.messageName = messageName;
    }

    public void setBoxId(String boxId) {
        this.boxId = boxId;
    }

    public void setWmsId(String wmsId) {
        this.wmsId = wmsId;
    }

    public void setAllowlevel(List<Integer> allowlevel) {
        this.allowlevel = allowlevel;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof AdjustOrder))
            return false;
        AdjustOrder other = (AdjustOrder)o;
        if (!other.canEqual(this))
            return false;
        Object this$id = getId(), other$id = other.getId();
        if ((this$id == null) ? (other$id != null) : !this$id.equals(other$id))
            return false;
        Object this$messageName = getMessageName(), other$messageName = other.getMessageName();
        if ((this$messageName == null) ? (other$messageName != null) : !this$messageName.equals(other$messageName))
            return false;
        Object this$boxId = getBoxId(), other$boxId = other.getBoxId();
        if ((this$boxId == null) ? (other$boxId != null) : !this$boxId.equals(other$boxId))
            return false;
        Object this$wmsId = getWmsId(), other$wmsId = other.getWmsId();
        if ((this$wmsId == null) ? (other$wmsId != null) : !this$wmsId.equals(other$wmsId))
            return false;
        List<Integer> this$allowlevel = (List<Integer>)getAllowlevel(), other$allowlevel = (List<Integer>)other.getAllowlevel();
        return !((this$allowlevel == null) ? (other$allowlevel != null) : !this$allowlevel.equals(other$allowlevel));
    }

    protected boolean canEqual(Object other) {
        return other instanceof AdjustOrder;
    }

    public int hashCode() {
        int PRIME = 59,
        result = 1;
        Object $id = getId();
        result = result * 59 + (($id == null) ? 43 : $id.hashCode());
        Object $messageName = getMessageName();
        result = result * 59 + (($messageName == null) ? 43 : $messageName.hashCode());
        Object $boxId = getBoxId();
        result = result * 59 + (($boxId == null) ? 43 : $boxId.hashCode());
        Object $wmsId = getWmsId();
        result = result * 59 + (($wmsId == null) ? 43 : $wmsId.hashCode());
        List<Integer> $allowlevel = (List<Integer>)getAllowlevel();
        return result * 59 + (($allowlevel == null) ? 43 : $allowlevel.hashCode());
    }

    public String toString() {
        return "AdjustOrder(id=" + getId() + ", messageName=" + getMessageName() + ", boxId=" + getBoxId() + ", wmsId=" + getWmsId() + ", allowlevel=" + getAllowlevel() + ")";
    }

    public Integer getId() {
        return this.id;
    }

    public String getMessageName() {
        return this.messageName;
    }

    public String getBoxId() {
        return this.boxId;
    }

    public String getWmsId() {
        return this.wmsId;
    }

    public List<Integer> getAllowlevel() {
        return this.allowlevel;
    }
}

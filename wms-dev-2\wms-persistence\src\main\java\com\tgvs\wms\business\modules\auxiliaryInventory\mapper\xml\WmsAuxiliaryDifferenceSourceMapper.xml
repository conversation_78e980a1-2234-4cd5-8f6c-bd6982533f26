<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tgvs.wms.business.modules.auxiliaryInventory.mapper.WmsAuxiliaryDifferenceSourceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tgvs.wms.business.modules.auxiliaryInventory.entity.WmsAuxiliaryDifferenceSource">
        <id column="id" property="id" />
        <result column="material_code" property="materialCode" />
        <result column="material_name" property="materialName" />
        <result column="material_spec" property="materialSpec" />
        <result column="material_color" property="materialColor" />
        <result column="material_size" property="materialSize" />
        <result column="unit" property="unit" />
        <result column="location_code" property="locationCode" />
        <result column="location_name" property="locationName" />
        <result column="box_no" property="boxNo" />
        <result column="box_type" property="boxType" />
        <result column="contract_no" property="contractNo" />
        <result column="contract_no_po" property="contractNoPo" />
        <result column="item_no" property="itemNo" />
        <result column="wms_qty" property="wmsQty" />
        <result column="erp_qty" property="erpQty" />
        <result column="diff_qty" property="diffQty" />
        <result column="diff_type" property="diffType" />
        <result column="diff_rate" property="diffRate" />
        <result column="source_system" property="sourceSystem" />
        <result column="source_batch" property="sourceBatch" />
        <result column="import_time" property="importTime" />
        <result column="status" property="status" />
        <result column="selected_time" property="selectedTime" />
        <result column="completed_time" property="completedTime" />
        <result column="remark" property="remark" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, material_code, material_name, material_spec, material_color, material_size, unit,
        location_code, location_name, box_no, box_type, contract_no, contract_no_po, item_no,
        wms_qty, erp_qty, diff_qty, diff_type, diff_rate,
        source_system, source_batch, import_time, status, selected_time, completed_time,
        remark, create_by, create_time, update_by, update_time, del_flag
    </sql>

    <!-- 分页查询差异数据源列表 -->
    <select id="selectDifferenceSourcePage" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM wms_auxiliary_difference_source
        WHERE del_flag = 0
        <if test="materialCode != null and materialCode != ''">
            AND material_code LIKE CONCAT('%', #{materialCode}, '%')
        </if>
        <if test="materialName != null and materialName != ''">
            AND material_name LIKE CONCAT('%', #{materialName}, '%')
        </if>
        <if test="contractNo != null and contractNo != ''">
            AND contract_no LIKE CONCAT('%', #{contractNo}, '%')
        </if>
        <if test="itemNo != null and itemNo != ''">
            AND item_no LIKE CONCAT('%', #{itemNo}, '%')
        </if>
        <if test="boxNo != null and boxNo != ''">
            AND box_no LIKE CONCAT('%', #{boxNo}, '%')
        </if>
        <if test="diffType != null and diffType != ''">
            AND diff_type = #{diffType}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="sourceBatch != null and sourceBatch != ''">
            AND source_batch = #{sourceBatch}
        </if>
        ORDER BY create_time DESC, diff_rate DESC
    </select>

    <!-- 查询待处理的差异数据列表 -->
    <select id="selectPendingDifferenceList" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM wms_auxiliary_difference_source
        WHERE del_flag = 0 
          AND status = 'PENDING'
        ORDER BY ABS(diff_rate) DESC, create_time ASC
    </select>

    <!-- 根据差异类型统计数量 -->
    <select id="countByDiffTypeAndStatus" resultType="int">
        SELECT COUNT(1)
        FROM wms_auxiliary_difference_source
        WHERE del_flag = 0
        <if test="diffType != null and diffType != ''">
            AND diff_type = #{diffType}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
    </select>

    <!-- 批量更新状态 -->
    <update id="batchUpdateStatus">
        UPDATE wms_auxiliary_difference_source
        SET status = #{status},
            update_by = #{updateBy},
            update_time = NOW()
            <if test="status == 'SELECTED'">
            , selected_time = NOW()
            </if>
            <if test="status == 'COMPLETED'">
            , completed_time = NOW()
            </if>
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND del_flag = 0
    </update>

</mapper>
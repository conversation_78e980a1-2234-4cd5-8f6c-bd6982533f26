package com.tgvs.wms.business.enums;


import com.tgvs.wms.common.util.oConvertUtils;

public enum enumTaskType {
    inbound(Integer.valueOf(1), "inbound", "入库"),
    outbound(Integer.valueOf(2), "outbound", "出库"),
    move(Integer.valueOf(15), "move", "移库"),
    outboundEmpty(Integer.valueOf(7), "outbound", "空箱出库"),
    outboundManual(Integer.valueOf(8), "outbound", "指定出库"),
    conveyor(Integer.valueOf(9), "conveyor", "输送");

//    type0(Integer.valueOf(0), "type0", "采购入库"),
//    type1(Integer.valueOf(1), "type1", "调拨入库"),
//    type2(Integer.valueOf(2), "type2", "生产退料回库"),
//    type3(Integer.valueOf(3), "type3", "领料出库"),
//    type4(Integer.valueOf(4), "type4", "调拨出库"),
//    type5(Integer.valueOf(5), "type5", "采购退货出库"),
//    type6(Integer.valueOf(6), "type6", "指定出库"),
//    type7(Integer.valueOf(7), "type7", "盘点出库");


//    任务类型：0.采购入库，1.调拨入库，2.生产退料回库，3.领料出库，4.调拨出库，5.采购退货出库，6.指定出库，7.盘点出库
    private Integer value;

    private String code;

    private String text;

    enumTaskType(Integer value, String code, String text) {
        this.value = value;
        this.code = code;
        this.text = text;
    }

    public static enumTaskType getByValue(int value) {
        if (oConvertUtils.isEmpty(Integer.valueOf(value)))
            return null;
        for (enumTaskType val : values()) {
            if (val.getValue().equals(Integer.valueOf(value)))
                return val;
        }
        return null;
    }

    public Integer getValue() {
        return this.value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getText() {
        return this.text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public static enumTaskType toEnum(Integer Value) {
        for (enumTaskType e : values()) {
            if (e.getValue().equals(Value))
                return e;
        }
        return null;
    }

    public static enumTaskType toEnum(String Value) {
        for (enumTaskType e : values()) {
            if (e.getValue().toString().equals(Value))
                return e;
        }
        return null;
    }
}

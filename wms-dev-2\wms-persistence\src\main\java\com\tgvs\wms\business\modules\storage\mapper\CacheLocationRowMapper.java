package com.tgvs.wms.business.modules.storage.mapper;


import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tgvs.wms.business.modules.storage.entity.CacheLocationRow;

public interface CacheLocationRowMapper extends BaseMapper<CacheLocationRow> {
    boolean deleteByMainId(@Param("mainId") String paramString);

    List<CacheLocationRow> selectByMainId(@Param("mainId") String paramString);
}

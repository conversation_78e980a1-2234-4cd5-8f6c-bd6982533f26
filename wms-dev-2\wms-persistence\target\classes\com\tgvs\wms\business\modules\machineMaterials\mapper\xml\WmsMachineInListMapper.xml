<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tgvs.wms.business.modules.machineMaterials.mapper.WmsMachineInListMapper">
    
    <!-- 自定义条件查询机物料入库列表 -->
    <select id="selectWmsMachineInListCustom" resultType="com.tgvs.wms.business.modules.machineMaterials.entity.WmsMachineInList">
        select id, in_store_number, material_code, material_name, in_quantity, 
               asset_class, asset_model, brand, priority, task_type,
               create_by, create_time, update_by, update_time, delete_flag
        from machine_in_list
        <where>
            <if test="inStoreNumber != null and inStoreNumber != ''">
                AND in_store_number = #{inStoreNumber}
            </if>
            <if test="materialCode != null and materialCode != ''">
                AND material_code = #{materialCode}
            </if>
            <if test="materialName != null and materialName != ''">
                AND material_name like concat('%', #{materialName}, '%')
            </if>
            <if test="assetClass != null and assetClass != ''">
                AND asset_class = #{assetClass}
            </if>
            <if test="assetModel != null and assetModel != ''">
                AND asset_model = #{assetModel}
            </if>
            <if test="brand != null and brand != ''">
                AND brand = #{brand}
            </if>
            <if test="inQuantity != null">
                AND in_quantity = #{inQuantity}
            </if>
            <if test="priority != null">
                AND priority = #{priority}
            </if>
            <if test="taskType != null">
                AND task_type = #{taskType}
            </if>
            AND delete_flag = 0
        </where>
        order by create_time desc
    </select>
    
    <!-- 批量逻辑删除机物料入库信息 -->
    <update id="deleteWmsMachineInListByIds">
        update machine_in_list set delete_flag = 1 
        where id in
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    
</mapper> 
package com.tgvs.wms.business.httpservice.baseBean.mes;


import com.alibaba.fastjson.annotation.JSO<PERSON>ield;

public class CallClothBoardBean {
    @J<PERSON>NField(name = "SiteNo")
    private String SiteNo;

    @J<PERSON><PERSON>ield(name = "<PERSON><PERSON>hNo")
    private String ClothNo;

    @J<PERSON>NField(name = "BoardType")
    private String BoardType;

    @JSONField(name = "ScrapClothNo")
    private String ScrapClothNo;

    @JSONField(name = "Weight")
    private Integer Weight;

    @JSONField(name = "UserNo")
    private String UserNo;

    public void setSiteNo(String SiteNo) {
        this.SiteNo = SiteNo;
    }

    public void setClothNo(String ClothNo) {
        this.ClothNo = ClothNo;
    }

    public void setBoardType(String BoardType) {
        this.BoardType = BoardType;
    }

    public void setScrapClothNo(String ScrapClothNo) {
        this.ScrapClothNo = ScrapClothNo;
    }

    public void setWeight(Integer Weight) {
        this.Weight = Weight;
    }

    public void setUserNo(String UserNo) {
        this.UserNo = UserNo;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof CallClothBoardBean))
            return false;
        CallClothBoardBean other = (CallClothBoardBean)o;
        if (!other.canEqual(this))
            return false;
        Object this$SiteNo = getSiteNo(), other$SiteNo = other.getSiteNo();
        if ((this$SiteNo == null) ? (other$SiteNo != null) : !this$SiteNo.equals(other$SiteNo))
            return false;
        Object this$ClothNo = getClothNo(), other$ClothNo = other.getClothNo();
        if ((this$ClothNo == null) ? (other$ClothNo != null) : !this$ClothNo.equals(other$ClothNo))
            return false;
        Object this$BoardType = getBoardType(), other$BoardType = other.getBoardType();
        if ((this$BoardType == null) ? (other$BoardType != null) : !this$BoardType.equals(other$BoardType))
            return false;
        Object this$ScrapClothNo = getScrapClothNo(), other$ScrapClothNo = other.getScrapClothNo();
        if ((this$ScrapClothNo == null) ? (other$ScrapClothNo != null) : !this$ScrapClothNo.equals(other$ScrapClothNo))
            return false;
        Object this$Weight = getWeight(), other$Weight = other.getWeight();
        if ((this$Weight == null) ? (other$Weight != null) : !this$Weight.equals(other$Weight))
            return false;
        Object this$UserNo = getUserNo(), other$UserNo = other.getUserNo();
        return !((this$UserNo == null) ? (other$UserNo != null) : !this$UserNo.equals(other$UserNo));
    }

    protected boolean canEqual(Object other) {
        return other instanceof CallClothBoardBean;
    }

    public int hashCode() {
        int PRIME = 59,
        result = 1;
        Object $SiteNo = getSiteNo();
        result = result * 59 + (($SiteNo == null) ? 43 : $SiteNo.hashCode());
        Object $ClothNo = getClothNo();
        result = result * 59 + (($ClothNo == null) ? 43 : $ClothNo.hashCode());
        Object $BoardType = getBoardType();
        result = result * 59 + (($BoardType == null) ? 43 : $BoardType.hashCode());
        Object $ScrapClothNo = getScrapClothNo();
        result = result * 59 + (($ScrapClothNo == null) ? 43 : $ScrapClothNo.hashCode());
        Object $Weight = getWeight();
        result = result * 59 + (($Weight == null) ? 43 : $Weight.hashCode());
        Object $UserNo = getUserNo();
        return result * 59 + (($UserNo == null) ? 43 : $UserNo.hashCode());
    }

    public String toString() {
        return "CallClothBoardBean(SiteNo=" + getSiteNo() + ", ClothNo=" + getClothNo() + ", BoardType=" + getBoardType() + ", ScrapClothNo=" + getScrapClothNo() + ", Weight=" + getWeight() + ", UserNo=" + getUserNo() + ")";
    }

    public String getSiteNo() {
        return this.SiteNo;
    }

    public String getClothNo() {
        return this.ClothNo;
    }

    public String getBoardType() {
        return this.BoardType;
    }

    public String getScrapClothNo() {
        return this.ScrapClothNo;
    }

    public Integer getWeight() {
        return this.Weight;
    }

    public String getUserNo() {
        return this.UserNo;
    }
}

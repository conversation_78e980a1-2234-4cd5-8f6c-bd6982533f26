package com.tgvs.wms.business.httpservice.baseBean.warehouse;


import com.fasterxml.jackson.annotation.JsonProperty;

public class messageType {
    @JsonProperty("messageName")
    private String messageName;

    public String toString() {
        return "messageType(messageName=" + getMessageName() + ")";
    }

    public int hashCode() {
        int PRIME = 59,
        result = 1;
        Object $messageName = getMessageName();
        return result * 59 + (($messageName == null) ? 43 : $messageName.hashCode());
    }

    protected boolean canEqual(Object other) {
        return other instanceof messageType;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof messageType))
            return false;
        messageType other = (messageType)o;
        if (!other.canEqual(this))
            return false;
        Object this$messageName = getMessageName(), other$messageName = other.getMessageName();
        return !((this$messageName == null) ? (other$messageName != null) : !this$messageName.equals(other$messageName));
    }

    public void setMessageName(String messageName) {
        this.messageName = messageName;
    }

    public String getMessageName() {
        return this.messageName;
    }
}

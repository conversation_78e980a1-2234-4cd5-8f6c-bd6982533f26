package com.tgvs.wms.business.httpservice.baseBean.mes;


import com.alibaba.fastjson.annotation.JSONField;

public class BoxPackageBean {
    @JSONField(name = "DepSiteNo")
    private String DepSiteNo;

    @J<PERSON><PERSON>ield(name = "DestSiteNo")
    private String DestSiteNo;

    @J<PERSON><PERSON><PERSON>(name = "BoxNo")
    private String BoxNo;

    @J<PERSON><PERSON>ield(name = "BoxType")
    private String BoxType;

    @J<PERSON><PERSON>ield(name = "IsEmpty")
    private Integer IsEmpty;

    @JSONField(name = "BoxHeight")
    private Integer BoxHeight;

    @JSONField(name = "BoxVolume")
    private Integer BoxVolume;

    @JSO<PERSON>ield(name = "UserNo")
    private String UserNo;

    @J<PERSON>NField(name = "BookNo")
    private String BookNo;

    @JSONField(name = "DestOrderNo")
    private String DestOrderNo;

    public void setDepSiteNo(String DepSiteNo) {
        this.DepSiteNo = DepSiteNo;
    }

    public void setDestSiteNo(String DestSiteNo) {
        this.DestSiteNo = DestSiteNo;
    }

    public void setBoxNo(String BoxNo) {
        this.BoxNo = BoxNo;
    }

    public void setBoxType(String BoxType) {
        this.BoxType = BoxType;
    }

    public void setIsEmpty(Integer IsEmpty) {
        this.IsEmpty = IsEmpty;
    }

    public void setBoxHeight(Integer BoxHeight) {
        this.BoxHeight = BoxHeight;
    }

    public void setBoxVolume(Integer BoxVolume) {
        this.BoxVolume = BoxVolume;
    }

    public void setUserNo(String UserNo) {
        this.UserNo = UserNo;
    }

    public void setBookNo(String BookNo) {
        this.BookNo = BookNo;
    }

    public void setDestOrderNo(String DestOrderNo) {
        this.DestOrderNo = DestOrderNo;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof BoxPackageBean))
            return false;
        BoxPackageBean other = (BoxPackageBean)o;
        if (!other.canEqual(this))
            return false;
        Object this$DepSiteNo = getDepSiteNo(), other$DepSiteNo = other.getDepSiteNo();
        if ((this$DepSiteNo == null) ? (other$DepSiteNo != null) : !this$DepSiteNo.equals(other$DepSiteNo))
            return false;
        Object this$DestSiteNo = getDestSiteNo(), other$DestSiteNo = other.getDestSiteNo();
        if ((this$DestSiteNo == null) ? (other$DestSiteNo != null) : !this$DestSiteNo.equals(other$DestSiteNo))
            return false;
        Object this$BoxNo = getBoxNo(), other$BoxNo = other.getBoxNo();
        if ((this$BoxNo == null) ? (other$BoxNo != null) : !this$BoxNo.equals(other$BoxNo))
            return false;
        Object this$BoxType = getBoxType(), other$BoxType = other.getBoxType();
        if ((this$BoxType == null) ? (other$BoxType != null) : !this$BoxType.equals(other$BoxType))
            return false;
        Object this$IsEmpty = getIsEmpty(), other$IsEmpty = other.getIsEmpty();
        if ((this$IsEmpty == null) ? (other$IsEmpty != null) : !this$IsEmpty.equals(other$IsEmpty))
            return false;
        Object this$BoxHeight = getBoxHeight(), other$BoxHeight = other.getBoxHeight();
        if ((this$BoxHeight == null) ? (other$BoxHeight != null) : !this$BoxHeight.equals(other$BoxHeight))
            return false;
        Object this$BoxVolume = getBoxVolume(), other$BoxVolume = other.getBoxVolume();
        if ((this$BoxVolume == null) ? (other$BoxVolume != null) : !this$BoxVolume.equals(other$BoxVolume))
            return false;
        Object this$UserNo = getUserNo(), other$UserNo = other.getUserNo();
        if ((this$UserNo == null) ? (other$UserNo != null) : !this$UserNo.equals(other$UserNo))
            return false;
        Object this$BookNo = getBookNo(), other$BookNo = other.getBookNo();
        if ((this$BookNo == null) ? (other$BookNo != null) : !this$BookNo.equals(other$BookNo))
            return false;
        Object this$DestOrderNo = getDestOrderNo(), other$DestOrderNo = other.getDestOrderNo();
        return !((this$DestOrderNo == null) ? (other$DestOrderNo != null) : !this$DestOrderNo.equals(other$DestOrderNo));
    }

    protected boolean canEqual(Object other) {
        return other instanceof BoxPackageBean;
    }

    public int hashCode() {
        int PRIME = 59,
        result = 1;
        Object $DepSiteNo = getDepSiteNo();
        result = result * 59 + (($DepSiteNo == null) ? 43 : $DepSiteNo.hashCode());
        Object $DestSiteNo = getDestSiteNo();
        result = result * 59 + (($DestSiteNo == null) ? 43 : $DestSiteNo.hashCode());
        Object $BoxNo = getBoxNo();
        result = result * 59 + (($BoxNo == null) ? 43 : $BoxNo.hashCode());
        Object $BoxType = getBoxType();
        result = result * 59 + (($BoxType == null) ? 43 : $BoxType.hashCode());
        Object $IsEmpty = getIsEmpty();
        result = result * 59 + (($IsEmpty == null) ? 43 : $IsEmpty.hashCode());
        Object $BoxHeight = getBoxHeight();
        result = result * 59 + (($BoxHeight == null) ? 43 : $BoxHeight.hashCode());
        Object $BoxVolume = getBoxVolume();
        result = result * 59 + (($BoxVolume == null) ? 43 : $BoxVolume.hashCode());
        Object $UserNo = getUserNo();
        result = result * 59 + (($UserNo == null) ? 43 : $UserNo.hashCode());
        Object $BookNo = getBookNo();
        result = result * 59 + (($BookNo == null) ? 43 : $BookNo.hashCode());
        Object $DestOrderNo = getDestOrderNo();
        return result * 59 + (($DestOrderNo == null) ? 43 : $DestOrderNo.hashCode());
    }

    public String toString() {
        return "BoxPackageBean(DepSiteNo=" + getDepSiteNo() + ", DestSiteNo=" + getDestSiteNo() + ", BoxNo=" + getBoxNo() + ", BoxType=" + getBoxType() + ", IsEmpty=" + getIsEmpty() + ", BoxHeight=" + getBoxHeight() + ", BoxVolume=" + getBoxVolume() + ", UserNo=" + getUserNo() + ", BookNo=" + getBookNo() + ", DestOrderNo=" + getDestOrderNo() + ")";
    }

    public String getDepSiteNo() {
        return this.DepSiteNo;
    }

    public String getDestSiteNo() {
        return this.DestSiteNo;
    }

    public String getBoxNo() {
        return this.BoxNo;
    }

    public String getBoxType() {
        return this.BoxType;
    }

    public Integer getIsEmpty() {
        return this.IsEmpty;
    }

    public Integer getBoxHeight() {
        return this.BoxHeight;
    }

    public Integer getBoxVolume() {
        return this.BoxVolume;
    }

    public String getUserNo() {
        return this.UserNo;
    }

    public String getBookNo() {
        return this.BookNo;
    }

    public String getDestOrderNo() {
        return this.DestOrderNo;
    }
}

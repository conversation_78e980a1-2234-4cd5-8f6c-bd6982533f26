package com.tgvs.wms.business.modules.storage.entity;

import java.io.Serializable;
import java.util.Date;

import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.tgvs.wms.base.entity.BaseEntity;
import com.tgvs.wms.common.annotation.Dict;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TableName("wms_stock")
@ApiModel(value = "wms_stock对象", description = "库存管理")
@Data
public class Stock extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键")
    private String id;

    @Excel(name = "货位编码", width = 15.0D)
    @ApiModelProperty("货位编码")
    private String code;

    @Excel(name = "货位", width = 15.0D)
    @ApiModelProperty("货位")
    private Integer location;

    @Excel(name = "层位", width = 15.0D)
    @ApiModelProperty("层位")
    private Integer level;

    @Excel(name = "容器类型", width = 15.0D, dicCode = "box_type")
    @Dict(dicCode = "box_type")
    @ApiModelProperty("容器类型")
    private Integer boxType;

    @Excel(name = "容器号", width = 15.0D)
    @ApiModelProperty("容器号")
    private String boxNo;

    @Excel(name = "空实状态", width = 15.0D, dicCode = "box_empty_status")
    @Dict(dicCode = "box_empty_status")
    @ApiModelProperty("容器空实状态")
    private Integer boxEmpty;

    @Excel(name = "料箱高度", width = 15.0D)
    @Dict(dicCode = "box_height")
    @ApiModelProperty("料箱高度")
    private Integer boxHeight;

    @Excel(name = "料箱容量", width = 15.0D)
    @ApiModelProperty("料箱容量")
    private Integer boxVolume;

    @ApiModelProperty("行位")
    private Integer rowNum;

    @Excel(name = "列位", width = 15.0D)
    @ApiModelProperty("列位")
    private Integer columnNumber;

    @Excel(name = "巷道", width = 15.0D)
    @ApiModelProperty("巷道")
    private Integer aisle;

    @Excel(name = "区域", width = 15.0D)
    @ApiModelProperty("区域")
    private String area;

    @ApiModelProperty("优先级")
    private Integer priority;

    @Excel(name = "锁定", width = 15.0D, dicCode = "locked_status")
    @Dict(dicCode = "locked_status")
    @ApiModelProperty("锁定")
    private Integer locked;

    @Excel(name = "容器状态", width = 15.0D, dicCode = "box_status")
    @Dict(dicCode = "box_status")
    @ApiModelProperty("容器状态")
    private Integer state;

    @Excel(name = "重量", width = 15.0D)
    @ApiModelProperty("重量")
    private Integer weight;

    @ApiModelProperty("合约")
    private String contractNo;

    @Excel(name = "单编号", width = 20.0D)
    @ApiModelProperty("订单编号")
    private String bookNo;

    @Excel(name = "批次号", width = 15.0D)
    @ApiModelProperty("批次号")
    private String batch;

    @ApiModelProperty("所属系统")
    private String sysid;

    @ApiModelProperty("创建人")
    private String createBy;

    @Excel(name = "入库时间", width = 20.0D, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("更新时间")
    private Date updateTime;

    @ApiModelProperty("删除状态")
    private Integer delFlag;
}

package com.tgvs.wms.business.enums;

import com.tgvs.wms.common.util.oConvertUtils;

public enum enumPriority {
    lowest(Integer.valueOf(0), "lowest", "最低"),
    lower(Integer.valueOf(10), "lower", "较低"),
    low(Integer.valueOf(20), "low", "低"),
    normal(Integer.valueOf(30), "normal", "正常"),
    high(Integer.valueOf(40), "high", "高"),
    higher(Integer.valueOf(50), "higher", "较高"),
    highest(Integer.valueOf(60), "highest", "最高");

    private Integer value;

    private String code;

    private String text;

    enumPriority(Integer value, String code, String text) {
        this.value = value;
        this.code = code;
        this.text = text;
    }

    public static enumPriority getByValue(int value) {
        if (oConvertUtils.isEmpty(Integer.valueOf(value)))
            return null;
        for (enumPriority val : values()) {
            if (val.getValue().equals(Integer.valueOf(value)))
                return val;
        }
        return null;
    }

    public Integer getValue() {
        return this.value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getText() {
        return this.text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public static enumPriority toEnum(Integer Value) {
        for (enumPriority e : values()) {
            if (e.getValue().equals(Value))
                return e;
        }
        return null;
    }

    public static enumPriority toEnum(String boxno) {
        if (boxno.indexOf("") == -1)
            return normal;
        return high;
    }
}

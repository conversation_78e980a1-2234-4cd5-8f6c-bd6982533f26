package com.tgvs.wms.business.enums;


import com.tgvs.wms.common.util.oConvertUtils;

public enum enumStatistics {
    emptyinbound(Integer.valueOf(11), "emptyinbound", "空箱入库"),
    fullinbound(Integer.valueOf(12), "fullinbound", "实箱入库"),
    convory(Integer.valueOf(13), "convory", "料箱输送"),
    emptyoutbound(Integer.valueOf(21), "emptyoutbound", "空箱出库"),
    fulloutbound(Integer.valueOf(22), "fulloutbound", "实箱出库"),
    emptyconvory(Integer.valueOf(23), "emptyconvory", "空箱补给"),
    loosefabric(Integer.valueOf(31), "loosefabric", "松布匹数"),
    spreading(Integer.valueOf(32), "spreading", "铺布匹数"),
    surplus(Integer.valueOf(33), "surplus", "余布收集"),
    surplusprocess(Integer.valueOf(34), "surplusprocess", "余布处理"),
    abbpallet(Integer.valueOf(41), "abbpallet", "搬运托板"),
    agvdolly(Integer.valueOf(51), "agvdolly", "调度松布架"),
    emptystore(Integer.valueOf(98), "emptystore", "空箱库存"),
    fullstore(Integer.valueOf(99), "fullstore", "实箱库存");

    private Integer value;

    private String code;

    private String text;

    enumStatistics(Integer value, String code, String text) {
        this.value = value;
        this.code = code;
        this.text = text;
    }

    public static enumStatistics getByValue(int value) {
        if (oConvertUtils.isEmpty(Integer.valueOf(value)))
            return null;
        for (enumStatistics val : values()) {
            if (val.getValue().equals(Integer.valueOf(value)))
                return val;
        }
        return null;
    }

    public Integer getValue() {
        return this.value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getText() {
        return this.text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public static enumStatistics toEnum(Integer Value) {
        for (enumStatistics e : values()) {
            if (e.getValue().equals(Value))
                return e;
        }
        return null;
    }

    public static enumStatistics toEnum(String Value) {
        for (enumStatistics e : values()) {
            if (e.getValue().toString().equals(Value))
                return e;
        }
        return null;
    }
}

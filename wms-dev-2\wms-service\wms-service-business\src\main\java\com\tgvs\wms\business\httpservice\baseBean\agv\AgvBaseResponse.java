package com.tgvs.wms.business.httpservice.baseBean.agv;

import lombok.Data;
import com.alibaba.fastjson.annotation.JSONField;

/**
 * 海康AGV接口基础响应类
 */
@Data
public class AgvBaseResponse {

    /**
     * 消息码
     * 通用响应code：
     * SUCCESS - 成功
     * Err_Internal - 内部未知错误 
     * Err_DataValidationFailed - 数据格式验证失败
     * Err_RequestDuplicate - 请求重复
     * Err_InvalidVersion - 请求版本不合法
     */
    @JSONField(name = "code")
    private String code;
    
    /**
     * 提示消息
     */
    @JSONField(name = "message")
    private String message;

    @JSONField(name = "reqCode")
    private String reqCode; // 请求编号 (与请求中的一致)

    @JSONField(name = "data")
    private Object data; // 可选的响应数据字段
}

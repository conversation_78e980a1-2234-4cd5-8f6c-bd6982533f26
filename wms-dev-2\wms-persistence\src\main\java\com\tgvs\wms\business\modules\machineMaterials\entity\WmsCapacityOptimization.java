package com.tgvs.wms.business.modules.machineMaterials.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tgvs.wms.base.entity.BaseEntity;
import io.swagger.models.auth.In;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.io.Serializable;
import lombok.Data;
import java.util.Date;


@EqualsAndHashCode(callSuper = true)
@TableName ( "wms_capacity_optimization" )
@Data
public class WmsCapacityOptimization  extends BaseEntity {

	private static final long serialVersionUID =  3884691467110656003L;

	/**
	 * 容器号
	 */
   	@TableField(value = "box_no")
	private String boxNo;

	/**
	 * 格号
	 */
   	@TableField(value = "grid_no")
	private Integer gridNo;

	/**
	 * 物料编码
	 */
   	@TableField(value = "material_code")
	private String materialCode;

	/**
	 * 物料名称
	 */
   	@TableField(value = "material_name")
	private String materialName;

	/**
	 * 箱型
	 */
   	@TableField(value = "box_type")
	private Integer boxType;

	/**
	 * 容量
	 */
   	@TableField(value = "grid_capacity")
	private Integer gridCapacity;

	/**
	 * 库存数量
	 */
   	@TableField(value = "material_quantity")
	private Integer materialQuantity;

	/**
	 * 是否删除：0.正常；1.删除
	 */
   	@TableField(value = "delete_flag")
	private Integer deleteFlag;

	/**
	 * 状态：0：未出库；1：已出库
	 */
	@TableField(value = "status")
	private Integer status;
}

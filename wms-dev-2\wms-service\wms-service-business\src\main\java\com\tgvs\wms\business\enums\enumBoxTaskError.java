package com.tgvs.wms.business.enums;

import com.tgvs.wms.common.util.oConvertUtils;

public enum enumBoxTaskError {
    normal(Integer.valueOf(0), "normal", "正常"),
    non_existent(Integer.valueOf(1001), "non_existent", "库存信息不存在"),
    empty_out(Integer.valueOf(1002), "empty_out", "空出,出库时货位无箱"),
    noread(Integer.valueOf(1003), "noread", "读码失败"),
    non_task(Integer.valueOf(1004), "non_task", "无任务信息"),
    error_site(Integer.valueOf(1005), "error_site", "不匹配的工位"),
    cancle_out(Integer.valueOf(1006), "cancle_out", "出库任务被取消"),
    high_range(Integer.valueOf(1007), "high_range", "料箱高度超出范围");

    private Integer value;

    private String code;

    private String text;

    enumBoxTaskError(Integer value, String code, String text) {
        this.value = value;
        this.code = code;
        this.text = text;
    }

    public static enumBoxTaskError getByValue(int value) {
        if (oConvertUtils.isEmpty(Integer.valueOf(value)))
            return null;
        for (enumBoxTaskError val : values()) {
            if (val.getValue().equals(Integer.valueOf(value)))
                return val;
        }
        return null;
    }

    public Integer getValue() {
        return this.value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getText() {
        return this.text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public static enumBoxTaskError toEnum(Integer Value) {
        for (enumBoxTaskError e : values()) {
            if (e.getValue().equals(Value))
                return e;
        }
        return null;
    }
}

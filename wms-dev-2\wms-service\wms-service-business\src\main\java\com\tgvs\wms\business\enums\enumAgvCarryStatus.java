package com.tgvs.wms.business.enums;

import com.tgvs.wms.common.util.oConvertUtils;

public enum enumAgvCarryStatus {
    not(Integer.valueOf(0), "not", "未搬运"),
    half(Integer.valueOf(1), "half", "少搬运"),
    carry(Integer.valueOf(2), "carry", "预搬运");

    private Integer value;

    private String code;

    private String text;

    enumAgvCarryStatus(Integer value, String code, String text) {
        this.value = value;
        this.code = code;
        this.text = text;
    }

    public static enumAgvCarryStatus getByValue(int value) {
        if (oConvertUtils.isEmpty(Integer.valueOf(value)))
            return null;
        for (enumAgvCarryStatus val : values()) {
            if (val.getValue().equals(Integer.valueOf(value)))
                return val;
        }
        return null;
    }

    public Integer getValue() {
        return this.value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getText() {
        return this.text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public static enumAgvCarryStatus toEnum(Integer Value) {
        for (enumAgvCarryStatus e : values()) {
            if (e.getValue() == Value)
                return e;
        }
        return null;
    }

    public static enumAgvCarryStatus toEnum(String Value) {
        for (enumAgvCarryStatus e : values()) {
            if (e.getValue().toString().equals(Value))
                return e;
        }
        return null;
    }
}

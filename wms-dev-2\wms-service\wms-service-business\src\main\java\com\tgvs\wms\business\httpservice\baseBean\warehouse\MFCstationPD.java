package com.tgvs.wms.business.httpservice.baseBean.warehouse;


public class MFCstationPD {
    private Integer id;

    private Integer status;

    private Integer level;

    private Integer pos;

    public void setId(Integer id) {
        this.id = id;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public void setPos(Integer pos) {
        this.pos = pos;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof MFCstationPD))
            return false;
        MFCstationPD other = (MFCstationPD)o;
        if (!other.canEqual(this))
            return false;
        Object this$id = getId(), other$id = other.getId();
        if ((this$id == null) ? (other$id != null) : !this$id.equals(other$id))
            return false;
        Object this$status = getStatus(), other$status = other.getStatus();
        if ((this$status == null) ? (other$status != null) : !this$status.equals(other$status))
            return false;
        Object this$level = getLevel(), other$level = other.getLevel();
        if ((this$level == null) ? (other$level != null) : !this$level.equals(other$level))
            return false;
        Object this$pos = getPos(), other$pos = other.getPos();
        return !((this$pos == null) ? (other$pos != null) : !this$pos.equals(other$pos));
    }

    protected boolean canEqual(Object other) {
        return other instanceof MFCstationPD;
    }

    public int hashCode() {
        int PRIME = 59,
        result = 1;
        Object $id = getId();
        result = result * 59 + (($id == null) ? 43 : $id.hashCode());
        Object $status = getStatus();
        result = result * 59 + (($status == null) ? 43 : $status.hashCode());
        Object $level = getLevel();
        result = result * 59 + (($level == null) ? 43 : $level.hashCode());
        Object $pos = getPos();
        return result * 59 + (($pos == null) ? 43 : $pos.hashCode());
    }

    public String toString() {
        return "MFCstationPD(id=" + getId() + ", status=" + getStatus() + ", level=" + getLevel() + ", pos=" + getPos() + ")";
    }

    public Integer getId() {
        return this.id;
    }

    public Integer getStatus() {
        return this.status;
    }

    public Integer getLevel() {
        return this.level;
    }

    public Integer getPos() {
        return this.pos;
    }
}

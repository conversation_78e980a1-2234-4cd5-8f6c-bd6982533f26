package com.tgvs.wms.business.modules.container.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tgvs.wms.business.modules.container.entity.AuxiliaryPrebox;
import com.tgvs.wms.common.core.domain.QueryModel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 辅料预装箱Mapper接口
 */
@Mapper
public interface AuxiliaryPreboxMapper extends BaseMapper<AuxiliaryPrebox> {
    
    /**
     * 根据前缀查询最大批次号
     */
    String selectMaxBatchNoByPrefix(@Param("prefix") String prefix);

//    /**
//     * 根据预装箱批次号查询预装箱信息
//     */
//    List<AuxiliaryPrebox> queryPreboxByStockBatchId(@Param("stockBatchId") String stockBatchId);

    /**
     * 直接使用QueryModel查询预装箱分组信息
     */
    List<AuxiliaryPrebox> queryPreboxByQueryModel(@Param("queryModel") QueryModel queryModel);
    
    /**
     * 根据容器号查询预装箱明细信息
     */
    List<AuxiliaryPrebox> queryPreboxDetailByContainer(@Param("containerNo") String containerNo);
} 
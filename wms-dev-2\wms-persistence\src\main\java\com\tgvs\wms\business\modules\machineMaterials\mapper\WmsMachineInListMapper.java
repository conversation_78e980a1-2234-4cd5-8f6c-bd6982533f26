package com.tgvs.wms.business.modules.machineMaterials.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tgvs.wms.business.modules.machineMaterials.entity.WmsMachineInList;
import org.apache.ibatis.annotations.Mapper;

/**
 * 机物料入库Mapper接口
 */
@Mapper
public interface WmsMachineInListMapper extends BaseMapper<WmsMachineInList> {
    
    /**
     * 批量逻辑删除机物料入库信息
     * 
     * @param ids 主键ID数组
     * @return 影响行数
     */
    int deleteWmsMachineInListByIds(String[] ids);
} 
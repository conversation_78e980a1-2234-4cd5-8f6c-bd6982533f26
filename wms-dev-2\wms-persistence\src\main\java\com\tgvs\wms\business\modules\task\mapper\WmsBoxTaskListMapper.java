package com.tgvs.wms.business.modules.task.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tgvs.wms.business.modules.container.entity.WmsMachineMaterialsBoxPrePacking;
import com.tgvs.wms.business.modules.task.entity.WmsBoxTaskList;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface WmsBoxTaskListMapper extends BaseMapper<WmsBoxTaskList> {
    List<WmsMachineMaterialsBoxPrePacking> selectOutTaskList();
    /**
     * 从数据库获取指定名称序列的下一个值
     * @param sequenceName 序列的名称 (例如 "seq_task_order")
     * @return 序列的下一个值
     */
    Long getNextTaskOrderSequenceValue(@Param("sequenceName") String sequenceName);
}

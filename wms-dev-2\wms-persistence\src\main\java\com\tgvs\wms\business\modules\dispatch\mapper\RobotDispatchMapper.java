package com.tgvs.wms.business.modules.dispatch.mapper;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tgvs.wms.business.modules.dispatch.entity.RobotDispatch;

public interface RobotDispatchMapper extends BaseMapper<RobotDispatch> {
    void updateTreeNodeStatus(@Param("id") String paramString1, @Param("status") String paramString2);

    RobotDispatch selectByTaskId(@Param("taskId") Integer paramInteger);
}

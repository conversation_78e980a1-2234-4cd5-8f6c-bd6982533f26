<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tgvs.wms.business.modules.bd.mapper.PointMapper">
    <!-- 根据节点编码查询
    <select id="getPointByNo2" resultType="com.tgvs.wms.business.modules.bd.entity.Point">
		select * from  wms_point  where point_no = #{pointno} and del_flag <![CDATA[<>]]> 0
	</select>-->
    <!-- 根据节点编码查询 -->
    <select id="getPointByNo" resultType="com.tgvs.wms.business.modules.bd.entity.Point">
		select * from  wms_point  where devic_type=2 and point_no = #{pointno}
	</select>
</mapper>
package com.tgvs.wms.business.modules.task.entity;

import java.io.Serializable;
import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.tgvs.wms.base.entity.BaseEntity;
import com.tgvs.wms.common.annotation.Dict;
import com.tgvs.wms.common.annotation.Excel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data

@TableName("wms_task_lift")
@ApiModel(value = "wms_task_lift对象", description = "提升机任务管理")
public class TaskLift extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键")
    private String id;

    @Excel(name = "任务号", width = 15.0D)
    @ApiModelProperty("任务号")
    private Integer taskId;

    @Excel(name = "调度执行对象", width = 15.0D, dictTable = "wms_commncation_device", dicText = "deviceName", dicCode = "deviceNo")
    @Dict(dictTable = "wms_commncation_device", dicText = "deviceName", dicCode = "deviceNo")
    @ApiModelProperty("调度执行对象")
    private String dispatcher;

    @Excel(name = "源位置", width = 15.0D, dictTable = "wms_point", dicText = "point_name", dicCode = "point_no")
    @Dict(dictTable = "wms_point", dicText = "point_name", dicCode = "point_no")
    @ApiModelProperty("源位置")
    private String fromSite;

    @Excel(name = "源楼层", width = 15.0D)
    @ApiModelProperty("源楼层")
    private Integer fromLevel;

    @Excel(name = "源区域", width = 15.0D, dictTable = "wms_base_area", dicText = "text", dicCode = "code")
    @Dict(dictTable = "wms_base_area", dicText = "text", dicCode = "code")
    @ApiModelProperty("源区域")
    private String fromArea;

    @Excel(name = "目的位置", width = 15.0D, dictTable = "wms_point", dicText = "point_name", dicCode = "point_no")
    @Dict(dictTable = "wms_point", dicText = "point_name", dicCode = "point_no")
    @ApiModelProperty("目的位置")
    private String toSite;

    @Excel(name = "目的楼层", width = 15.0D)
    @ApiModelProperty("目的楼层")
    private Integer toLevel;

    @Excel(name = "占用情况", width = 15.0D)
    @ApiModelProperty("占用情况")
    @TableField(exist = false)
    private Integer occupy;

    @Excel(name = "目的区域", width = 15.0D, dictTable = "wms_base_area", dicText = "text", dicCode = "code")
    @Dict(dictTable = "wms_base_area", dicText = "text", dicCode = "code")
    @ApiModelProperty("目的区域")
    private String toArea;

    @Excel(name = "任务类型", width = 15.0D, dictTable = "wms_task_type", dicText = "text", dicCode = "value")
    @Dict(dictTable = "wms_task_type", dicText = "text", dicCode = "value")
    @ApiModelProperty("任务类型")
    private Integer type;

    @Excel(name = "优先级", width = 15.0D)
    @ApiModelProperty("优先级")
    private Integer priority;

    @Excel(name = "任务锁定", width = 15.0D, dicCode = "locked_status")
    @Dict(dicCode = "locked_status")
    @ApiModelProperty("任务锁定")
    private Integer locked;

    @Excel(name = "任务状态", width = 15.0D, dictTable = "wms_task_state", dicText = "text", dicCode = "value")
    @Dict(enumType = "TaskStatus", dicText = "text", dicCode = "value")
    @ApiModelProperty("任务状态")
    private Integer state;

    @Excel(name = "容器号", width = 15.0D)
    @ApiModelProperty("容器号")
    private String boxNo;

    @Excel(name = "消息文本", width = 15.0D)
    @ApiModelProperty("消息文本")
    private String msg;

    @Excel(name = "结束时间", width = 20.0D, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("结束时间")
    private Date endTime;

    @ApiModelProperty("创建人")
    private String createBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("更新时间")
    private Date updateTime;

    @Excel(name = "删除状态", width = 15.0D)
    @ApiModelProperty("删除状态")
    private Integer delFlag;
}

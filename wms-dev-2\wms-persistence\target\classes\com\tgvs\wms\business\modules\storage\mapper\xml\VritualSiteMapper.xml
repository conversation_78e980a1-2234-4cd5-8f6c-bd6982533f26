<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tgvs.wms.business.modules.storage.mapper.VritualSiteMapper">
    <!-- 根据节点编码查询 -->
    <select id="selectByCode" resultType="com.tgvs.wms.business.modules.storage.entity.VritualSite">
		select * from  wms_vritual_site  where site_code
		 = #{siteCode}
	</select>

	<select id="selectLathe" resultType="com.tgvs.wms.business.modules.storage.entity.Lathe">
		SELECT * from (
		/*按任务查询数量-开始*/
		select XX1.lathe_no, COALESCE(sumcount,0) as subcount from
		(SELECT distinct lathe_no from wms_prepare_task where area=#{area}  and  state <![CDATA[=]]> 25 ) XX1 /*任务中的松布架列表*/ left join

		(
		/*数量-开始*/
		SELECT lathe_no,count(*) AS sumcount  from (
		select to_lathe_no AS lathe_no,bill_no,cloth_no from wms_agv_dolly A /*松布架*/ inner join wms_prepare_task B on A.billno=b.bill_no where A.area =#{area} and b.state=25  /*松布架库存中在执行的拉布单库存*/
		union
		SELECT latheno AS lathe_no,billno as bill_no,cloth_no FROM wms_cache_location_row where area=#{area} and latheno is not null  and latheno !='' /*缓存架上库存*/
		) X1 GROUP BY lathe_no
		/*数量-结束*/
		) XX2
		on XX1.lathe_no =XX2.lathe_no
		/*按任务查询数量-结束*/
		) XXX1
		where lathe_no not in (select latheno AS lathe_no from wms_dispatch_agv where type=4) /*不在AGV调度中*/
		and lathe_no not in (select point_no AS lathe_no from wms_point where point_type=20 and locked = 1 and point_no like '%00C%')
		order by subcount
	</select>

</mapper>
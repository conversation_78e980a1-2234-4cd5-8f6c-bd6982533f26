package com.tgvs.wms.business.httpservice.baseBean.agv;

import com.alibaba.fastjson.annotation.J<PERSON>NField;

public class DollyInfo {
    @J<PERSON><PERSON>ield(name = "TaskID")
    private String TaskID;

    @J<PERSON><PERSON>ield(name = "SiteNo")
    private String SiteNo;

    @J<PERSON><PERSON><PERSON>(name = "<PERSON>No")
    private String DollyNo;

    @J<PERSON><PERSON>ield(name = "LayerQty")
    private String LayerQty;

    @JSONField(name = "Content")
    private String Content;

    @J<PERSON>NField(name = "ScrapFlag")
    private String ScrapFlag;

    public void setTaskID(String TaskID) {
        this.TaskID = TaskID;
    }

    public void setSiteNo(String SiteNo) {
        this.SiteNo = SiteNo;
    }

    public void setDollyNo(String DollyNo) {
        this.DollyNo = DollyNo;
    }

    public void setLayerQty(String LayerQty) {
        this.LayerQty = LayerQty;
    }

    public void setContent(String Content) {
        this.Content = Content;
    }

    public void setScrapFlag(String ScrapFlag) {
        this.ScrapFlag = ScrapFlag;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof DollyInfo))
            return false;
        DollyInfo other = (DollyInfo)o;
        if (!other.canEqual(this))
            return false;
        Object this$TaskID = getTaskID(), other$TaskID = other.getTaskID();
        if ((this$TaskID == null) ? (other$TaskID != null) : !this$TaskID.equals(other$TaskID))
            return false;
        Object this$SiteNo = getSiteNo(), other$SiteNo = other.getSiteNo();
        if ((this$SiteNo == null) ? (other$SiteNo != null) : !this$SiteNo.equals(other$SiteNo))
            return false;
        Object this$DollyNo = getDollyNo(), other$DollyNo = other.getDollyNo();
        if ((this$DollyNo == null) ? (other$DollyNo != null) : !this$DollyNo.equals(other$DollyNo))
            return false;
        Object this$LayerQty = getLayerQty(), other$LayerQty = other.getLayerQty();
        if ((this$LayerQty == null) ? (other$LayerQty != null) : !this$LayerQty.equals(other$LayerQty))
            return false;
        Object this$Content = getContent(), other$Content = other.getContent();
        if ((this$Content == null) ? (other$Content != null) : !this$Content.equals(other$Content))
            return false;
        Object this$ScrapFlag = getScrapFlag(), other$ScrapFlag = other.getScrapFlag();
        return !((this$ScrapFlag == null) ? (other$ScrapFlag != null) : !this$ScrapFlag.equals(other$ScrapFlag));
    }

    protected boolean canEqual(Object other) {
        return other instanceof DollyInfo;
    }

    public int hashCode() {
        int PRIME = 59, result = 1;
        Object $TaskID = getTaskID();
        result = result * 59 + (($TaskID == null) ? 43 : $TaskID.hashCode());
        Object $SiteNo = getSiteNo();
        result = result * 59 + (($SiteNo == null) ? 43 : $SiteNo.hashCode());
        Object $DollyNo = getDollyNo();
        result = result * 59 + (($DollyNo == null) ? 43 : $DollyNo.hashCode());
        Object $LayerQty = getLayerQty();
        result = result * 59 + (($LayerQty == null) ? 43 : $LayerQty.hashCode());
        Object $Content = getContent();
        result = result * 59 + (($Content == null) ? 43 : $Content.hashCode());
        Object $ScrapFlag = getScrapFlag();
        return result * 59 + (($ScrapFlag == null) ? 43 : $ScrapFlag.hashCode());
    }

    public String toString() {
        return "DollyInfo(TaskID=" + getTaskID() + ", SiteNo=" + getSiteNo() + ", DollyNo=" + getDollyNo() + ", LayerQty=" + getLayerQty() + ", Content=" + getContent() + ", ScrapFlag=" + getScrapFlag() + ")";
    }

    public String getTaskID() {
        return this.TaskID;
    }

    public String getSiteNo() {
        return this.SiteNo;
    }

    public String getDollyNo() {
        return this.DollyNo;
    }

    public String getLayerQty() {
        return this.LayerQty;
    }

    public String getContent() {
        return this.Content;
    }

    public String getScrapFlag() {
        return this.ScrapFlag;
    }

    public Integer getLayers() {
        Integer i = new Integer(0);
        if (null != this.LayerQty) {
            try {
                i = Integer.valueOf(Integer.parseInt(getLayerQty()));
            } catch (Exception e) {
                e.printStackTrace();
            }
            return i;
        }
        return i;
    }
}

package com.tgvs.wms.framework.aspect;

import com.alibaba.fastjson.JSON;
import com.tgvs.wms.common.annotation.Log;
import com.tgvs.wms.common.util.IpUtils;
import com.tgvs.wms.common.util.SqliteLogUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.UUID;

/**
 * 全局日志切面 - 使用现有@Log注解
 * 同时记录到文件和SQLite数据库
 */
@Aspect
@Component
@Slf4j
public class LogAspect {

    @Autowired
    private SqliteLogUtil sqliteLogUtil;

    /**
     * 定义切点：拦截所有标注了@Log注解的方法
     */
    @Pointcut("@annotation(com.tgvs.wms.common.annotation.Log)")
    public void logPointcut() {
    }

    /**
     * 环绕通知：记录方法执行前后的日志
     */
    @Around("logPointcut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        String requestId = UUID.randomUUID().toString().replace("-", "");
        
        // 获取方法签名
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        
        // 获取@Log注解
        Log logAnnotation = method.getAnnotation(Log.class);
        
        // 收集日志信息
        String module = logAnnotation.title();
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = method.getName();
        
        // 获取请求信息
        HttpServletRequest request = null;
        String requestUrl = "";
        String requestMethod = "";
        String clientIp = "";
        String userAgent = "";
        
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                request = attributes.getRequest();
                requestUrl = request.getRequestURL().toString();
                requestMethod = request.getMethod();
                clientIp = IpUtils.getIpAddr(request);
                userAgent = request.getHeader("User-Agent");
            }
        } catch (Exception e) {
            log.warn("获取请求信息失败", e);
        }
        
        // 记录入参
        String requestParams = "";
        if (logAnnotation.isSaveRequestData()) {
            Object[] args = joinPoint.getArgs();
            if (args != null && args.length > 0) {
                try {
                    // 过滤敏感参数
                    Object[] filteredArgs = filterSensitiveData(args);
                    requestParams = JSON.toJSONString(filteredArgs);
                } catch (Exception e) {
                    requestParams = "参数序列化失败: " + e.getMessage();
                }
            } else {
                requestParams = "无";
            }
        }
        
        // 构建控制台日志信息
        StringBuilder logInfo = new StringBuilder();
        logInfo.append("\n==================== 接口调用开始 ====================");
        logInfo.append("\n请求ID: ").append(requestId);
        logInfo.append("\n模块: ").append(module);
        logInfo.append("\n类名: ").append(className);
        logInfo.append("\n方法: ").append(methodName);
        logInfo.append("\n请求URL: ").append(requestUrl);
        logInfo.append("\n请求方式: ").append(requestMethod);
        logInfo.append("\n客户端IP: ").append(clientIp);
        logInfo.append("\n请求参数: ").append(requestParams);
        
        log.info(logInfo.toString());
        
        Object result = null;
        Exception exception = null;
        String status = "SUCCESS";
        String errorMessage = "";
        String responseData = "";
        
        try {
            // 执行目标方法
            result = joinPoint.proceed();

            // 检查返回结果是否表示业务失败
            if (result != null && isBusinessFailure(result)) {
                status = "FAILED";
                errorMessage = extractErrorMessage(result);
            }

        } catch (Exception e) {
            exception = e;
            status = "FAILED";
            errorMessage = e.getMessage();
            throw e;
        } finally {
            // 记录执行结果
            long endTime = System.currentTimeMillis();
            long executionTime = endTime - startTime;

            // 记录出参
            // 对于业务失败的情况，即使配置关闭了响应数据记录，也要记录以便调试
            boolean shouldSaveResponse = logAnnotation.isSaveResponseData() || "FAILED".equals(status);

            if (shouldSaveResponse && result != null && exception == null) {
                try {
                    responseData = JSON.toJSONString(result);
                } catch (Exception e) {
                    responseData = "返回结果序列化失败: " + e.getMessage();
                }
            }

            // 构建结果日志
            StringBuilder resultLog = new StringBuilder();
            resultLog.append("\n==================== 接口调用结束 ====================");
            resultLog.append("\n请求ID: ").append(requestId);
            resultLog.append("\n执行时间: ").append(executionTime).append("ms");
            resultLog.append("\n执行状态: ").append(status);

            if (exception != null) {
                resultLog.append("\n异常信息: ").append(errorMessage);
                log.error(resultLog.toString(), exception);
            } else {
                if (!responseData.isEmpty()) {
                    resultLog.append("\n返回结果: ").append(responseData);
                }
                // 根据状态决定日志级别
                if ("FAILED".equals(status)) {
                    resultLog.append("\n业务失败信息: ").append(errorMessage);
                    log.error(resultLog.toString());
                } else {
                    log.info(resultLog.toString());
                }
            }

            resultLog.append("\n====================================================");

            // 异步保存到SQLite数据库
            sqliteLogUtil.saveLogAsync(requestId, module, className, methodName,
                    requestUrl, requestMethod, clientIp, userAgent,
                    requestParams, responseData, executionTime,
                    status, errorMessage);
        }
        
        return result;
    }

    /**
     * 判断返回结果是否表示业务失败
     * 主要检查ApiResponse类型的返回值
     */
    private boolean isBusinessFailure(Object result) {
        if (result == null) {
            return false;
        }

        // 检查是否是ApiResponse类型且success为false
        if (result.getClass().getSimpleName().equals("ApiResponse")) {
            try {
                // 使用反射获取success字段
                java.lang.reflect.Field successField = result.getClass().getDeclaredField("success");
                successField.setAccessible(true);
                Boolean success = (Boolean) successField.get(result);
                return success != null && !success;
            } catch (Exception e) {
                // 反射失败，尝试通过JSON解析
                try {
                    String jsonStr = JSON.toJSONString(result);
                    return jsonStr.contains("\"success\":false");
                } catch (Exception ex) {
                    log.debug("无法判断业务执行状态，默认为成功", ex);
                    return false;
                }
            }
        }

        return false;
    }

    /**
     * 从返回结果中提取错误信息
     */
    private String extractErrorMessage(Object result) {
        if (result == null) {
            return "";
        }

        try {
            // 尝试通过反射获取message字段
            java.lang.reflect.Field messageField = result.getClass().getDeclaredField("message");
            messageField.setAccessible(true);
            Object message = messageField.get(result);
            return message != null ? message.toString() : "";
        } catch (Exception e) {
            // 反射失败，尝试通过JSON解析
            try {
                String jsonStr = JSON.toJSONString(result);
                // 简单的JSON解析提取message
                if (jsonStr.contains("\"message\":")) {
                    int start = jsonStr.indexOf("\"message\":\"") + 11;
                    int end = jsonStr.indexOf("\"", start);
                    if (start > 10 && end > start) {
                        return jsonStr.substring(start, end);
                    }
                }
            } catch (Exception ex) {
                log.debug("无法提取错误信息", ex);
            }
        }

        return "";
    }
    
    /**
     * 过滤敏感数据
     */
    private Object[] filterSensitiveData(Object[] args) {
        if (args == null) {
            return null;
        }
        
        Object[] filteredArgs = new Object[args.length];
        for (int i = 0; i < args.length; i++) {
            Object arg = args[i];
            if (arg != null) {
                String argStr = arg.toString().toLowerCase();
                // 检查是否包含敏感信息
                if (argStr.contains("password") || argStr.contains("pwd") || 
                    argStr.contains("token") || argStr.contains("secret")) {
                    filteredArgs[i] = "***敏感信息已隐藏***";
                } else {
                    filteredArgs[i] = arg;
                }
            } else {
                filteredArgs[i] = null;
            }
        }
        return filteredArgs;
    }
} 
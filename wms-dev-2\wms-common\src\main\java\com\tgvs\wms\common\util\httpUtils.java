package com.tgvs.wms.common.util;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import lombok.extern.slf4j.Slf4j;
import okhttp3.Call;
import okhttp3.FormBody;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.apache.http.HttpEntity;
import org.apache.http.StatusLine;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Component;

import com.tgvs.wms.common.annotation.AutoLog;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

@Slf4j
@Component
public class httpUtils {

    public static final String mesUrl = "http://118.190.158.64:8080/wms-apitest/api/mes";

    public static final String agvUrl = "http://118.190.158.64:8080/wms-apitest/api/agv";

    public static final String mfcUrl = "http://118.190.158.64:8080/wms-apitest/api/wcs";

    private static CloseableHttpClient httpclient = HttpClientBuilder.create().build();

    private static RequestConfig requestConfig = RequestConfig.custom()
            .setSocketTimeout(60000)
            .setConnectTimeout(60000).build();

    private static OkHttpClient client = (new OkHttpClient()).newBuilder()
            .connectTimeout(60L, TimeUnit.SECONDS)
            .readTimeout(60L, TimeUnit.SECONDS)
            .build();

    // 忽略SSL证书验证的客户端（仅用于开发/测试环境）
    private static OkHttpClient unsafeClient;
    static {
        try {
            final TrustManager[] trustAllCerts = new TrustManager[] {
                    new X509TrustManager() {
                        @Override
                        public void checkClientTrusted(java.security.cert.X509Certificate[] chain, String authType) {
                        }

                        @Override
                        public void checkServerTrusted(java.security.cert.X509Certificate[] chain, String authType) {
                        }

                        @Override
                        public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                            return new java.security.cert.X509Certificate[] {};
                        }
                    }
            };

            final SSLContext sslContext = SSLContext.getInstance("SSL");
            sslContext.init(null, trustAllCerts, new java.security.SecureRandom());

            unsafeClient = new OkHttpClient.Builder()
                    .sslSocketFactory(sslContext.getSocketFactory(), (X509TrustManager) trustAllCerts[0])
                    .hostnameVerifier((hostname, session) -> true)
                    .connectTimeout(60L, TimeUnit.SECONDS)
                    .readTimeout(60L, TimeUnit.SECONDS)
                    .build();
        } catch (Exception e) {
            log.error("初始化不安全的OkHttpClient失败", e);
            unsafeClient = client; // 如果初始化失败，使用标准客户端
        }
    }

    @AutoLog("通讯-发送请求")
    public static String doPostHttp(String systemName, String https, String api, String params, String token)
            throws Exception {
        String url = String.format("%s%s", new Object[] { https, api });
        HttpPost httpPost = new HttpPost(url);
        requestConfig = RequestConfig.custom().setSocketTimeout(60000).setConnectTimeout(60000).build();
        httpPost.setConfig(requestConfig);
        httpPost.setHeader("Content-Type", "application/json;charset=utf8");
        if (null != token && !token.equals(""))
            httpPost.setHeader("Authorization", token);
        String charSet = "UTF-8";
        StringEntity entity = new StringEntity(params, charSet);
        httpPost.setEntity((HttpEntity) entity);
        CloseableHttpResponse response = null;
        try {
            response = httpclient.execute((HttpUriRequest) httpPost);
            StatusLine status = response.getStatusLine();
            int state = status.getStatusCode();
            if (state == 200) {
                HttpEntity responseEntity = response.getEntity();
                String jsonString = EntityUtils.toString(responseEntity);
                return jsonString;
            }
            System.err.println("请求返回:" + state + "(" + url + ")");
        } finally {
            if (response != null)
                try {
                    response.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            try {
                httpclient.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    public static void doPutOkHttp(String systemName, String url, String api, String params) throws IOException {
        String urls = String.format("%s%s", new Object[] { url, api });
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), params);
        Request request = (new Request.Builder()).url(urls).put(requestBody).build();
        Call call = client.newCall(request);
        Response response = call.execute();
        System.out.println(response.body().string());
    }

    @AutoLog("通讯-发送请求")
    public static String doPostOkHttp(String systemName, String url, String api, String params, String token)
            throws IOException {
        String urls = String.format("%s%s", new Object[] { url, api });
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json;charset=utf-8"), params);
        Request request = (new Request.Builder()).url(urls).addHeader("Content-Type", "application/json")
                .post(requestBody).build();
        if (null != token)
            request = (new Request.Builder()).url(urls).addHeader("Authorization", token)
                    .addHeader("Content-Type", "application/json").post(requestBody).build();
        Call call = client.newCall(request);
        Response response = null;
        try {
            response = call.execute();
        } catch (IOException ioException) {
            log.info("!!!!!doPostOkHttp请求地址:" + urls + "  " + ioException.getMessage());
            log.info("!!!!!doPostOkHttp请求参数:" + params);
            log.info("!!!!!doPostOkHttp失败原因:" + ioException.getCause());
        }
        if (null != response && response.code() == 200) {
            if (null != response.body()) {
                String strresponse = response.body().string();
                return strresponse;
            }
            return null;
        }
        return null;
    }

    @AutoLog("通讯-发送请求")
    public static boolean doPostOkHttpForDps(String systemName, String url, String api, String params, String token)
            throws IOException {
        String urls = String.format("%s%s", new Object[] { url, api });
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json;charset=utf-8"), params);
        Request request = (new Request.Builder()).url(urls).addHeader("Content-Type", "application/json")
                .post(requestBody).build();
        if (null != token)
            request = (new Request.Builder()).url(urls).addHeader("token", token)
                    .addHeader("Content-Type", "application/json").post(requestBody).build();
        Call call = client.newCall(request);
        Response response = null;
        try {
            response = call.execute();
        } catch (IOException ioException) {
            log.info("!!!!!doPostOkHttp请求地址:" + urls + "  " + ioException.getMessage());
            log.info("!!!!!doPostOkHttp请求参数:" + params);
            log.info("!!!!!doPostOkHttp失败原因:" + ioException.getCause());
            return false;
        }
        if (null != response && response.code() == 200)
            return true;
        return false;
    }

    @AutoLog("通讯-发送请求2")
    public static String doPostHttpwithToken(String systemName, String https, String token, String api, String params) {
        String methodUrl = "http://xxx.xxx.xx.xx:8280/xx/adviserxx/1.0 ";
        HttpURLConnection connection = null;
        OutputStream dataout = null;
        BufferedReader reader = null;
        String line = null;
        StringBuilder result = new StringBuilder();
        try {
            String urls = String.format("%s%s", new Object[] { https, api });
            URL url = new URL(urls);
            connection = (HttpURLConnection) url.openConnection();
            connection.setDoOutput(true);
            connection.setDoInput(true);
            connection.setRequestMethod("POST");
            connection.setUseCaches(false);
            connection.setConnectTimeout(3000);
            connection.setReadTimeout(3000);
            connection.setInstanceFollowRedirects(true);
            connection.setRequestProperty("connection", "Keep-Alive");
            connection.setRequestProperty("charset", "utf-8");
            connection.setRequestProperty("Content-Type", "application/json");
            if (null != token && !token.equals(""))
                connection.setRequestProperty("Authorization", token);
            connection.connect();
            dataout = new DataOutputStream(connection.getOutputStream());
            dataout.write(params.getBytes());
            dataout.flush();
            dataout.close();
            if (connection.getResponseCode() == 200) {
                reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"));
                while ((line = reader.readLine()) != null)
                    result.append(line).append(System.getProperty("line.separator"));
                System.out.println(result.toString());
                return result.toString();
            }
        } catch (IOException e) {
            e.printStackTrace();
            return e.getMessage();
        } finally {
            try {
                reader.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
            connection.disconnect();
        }
        return result.toString();
    }

    public static void doGetHttp(String systemName, String functionName, Map params) {
        String methodUrl = getSystemUrl(systemName) + "/" + functionName;
        String sUrl = "";
        if (null != params && params.size() > 0)
            for (Object map : params.values())
                sUrl = params.toString();
        HttpURLConnection connection = null;
        BufferedReader reader = null;
        String line = null;
        try {
            URL url = new URL(methodUrl + "?mobile=15334567890&name=zhansan");
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.connect();
            if (connection.getResponseCode() == 200) {
                reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"));
                StringBuilder result = new StringBuilder();
                while ((line = reader.readLine()) != null)
                    result.append(line).append(System.getProperty("line.separator"));
                System.out.println(result.toString());
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                reader.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
            connection.disconnect();
        }
    }

    private static String getSystemUrl(String systemName) {
        String Url = "";
        switch (systemName) {
            case "MES":
                Url = "http://118.190.158.64:8080/wms-apitest/api/mes";
                break;
            case "MFC":
                Url = "http://118.190.158.64:8080/wms-apitest/api/wcs";
                break;
            case "AGV":
                Url = "http://118.190.158.64:8080/wms-apitest/api/agv";
                break;
        }
        return Url;
    }

    @AutoLog("通讯-发送请求")
    public static String doPutOkHttp(String systemName, String url, Map<String, String> headers, String params)
            throws IOException {
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), params);
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .put(requestBody);

        // 添加所有请求头
        if (headers != null && !headers.isEmpty()) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                requestBuilder.addHeader(entry.getKey(), entry.getValue());
            }
        }

        Request request = requestBuilder.build();
        // 使用忽略证书验证的客户端
        Call call = ("Hikrobot".equals(systemName) ? unsafeClient : client).newCall(request);
        Response response = null;
        try {
            response = call.execute();
        } catch (IOException ioException) {
            log.info("!!!!!doPutOkHttp请求地址:" + url + "  " + ioException.getMessage());
            log.info("!!!!!doPutOkHttp请求参数:" + params);
            log.info("!!!!!doPutOkHttp失败原因:" + ioException.getCause());
            throw ioException;
        }
        if (null != response && response.code() == 200) {
            if (null != response.body()) {
                return response.body().string();
            }
        }
        return null;
    }

    @AutoLog("通讯-发送请求")
    public static String httpsPost(String url, Map<String, String> headers, String params) throws IOException {
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), params);
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .post(requestBody);

        // 添加所有请求头
        if (headers != null && !headers.isEmpty()) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                requestBuilder.addHeader(entry.getKey(), entry.getValue());
            }
        }

        Request request = requestBuilder.build();
        // 使用忽略证书验证的客户端
        Call call = unsafeClient.newCall(request);
        Response response = null;
        try {
            response = call.execute();
        } catch (IOException ioException) {
            log.info("!!!!!httpsPost请求地址:" + url + "  " + ioException.getMessage());
            log.info("!!!!!httpsPost请求参数:" + params);
            log.info("!!!!!httpsPost失败原因:" + ioException.getCause());
            throw ioException;
        }
        if (null != response && response.code() == 200) {
            if (null != response.body()) {
                return response.body().string();
            }
        }
        return null;
    }

    /**
     * 发送form-data格式的POST请求
     * 适用于I9-金复凯系统等需要form-data格式的接口
     * 
     * @param url      请求URL
     * @param formData 表单数据
     * @return 响应结果
     * @throws IOException 请求异常
     */
    @AutoLog("通讯-发送form-data请求")
    public static String doPostFormData(String url, Map<String, Object> formData) throws IOException {
        try {
            // 构建form-data请求体
            FormBody.Builder formBuilder = new FormBody.Builder();

            if (formData != null && !formData.isEmpty()) {
                for (Map.Entry<String, Object> entry : formData.entrySet()) {
                    String key = entry.getKey();
                    Object value = entry.getValue();

                    if (value != null) {
                        formBuilder.add(key, value.toString());
                    }
                }
            }

            RequestBody requestBody = formBuilder.build();

            // 构建请求
            Request request = new Request.Builder()
                    .url(url)
                    .post(requestBody)
                    .build();

            log.info("发送form-data请求: url={}, formData={}", url, formData);

            // 发送请求
            Call call = client.newCall(request);
            Response response = null;

            try {
                response = call.execute();

                if (response != null && response.isSuccessful()) {
                    if (response.body() != null) {
                        String responseString = response.body().string();
                        log.info("form-data请求响应: url={}, response={}", url, responseString);
                        return responseString;
                    }
                } else {
                    log.warn("form-data请求失败: url={}, code={}, message={}",
                            url, response != null ? response.code() : "null",
                            response != null ? response.message() : "null");
                }

            } catch (IOException ioException) {
                log.error("form-data请求异常: url={}, error={}", url, ioException.getMessage(), ioException);
                throw ioException;
            } finally {
                if (response != null) {
                    response.close();
                }
            }

        } catch (Exception e) {
            log.error("构建form-data请求异常: url={}, error={}", url, e.getMessage(), e);
            throw new IOException("构建form-data请求失败: " + e.getMessage(), e);
        }

        return null;
    }

    /**
     * 发送带请求头的form-data格式的POST请求
     * 适用于需要携带Token等请求头的form-data接口
     * 
     * @param url      请求URL
     * @param formData 表单数据
     * @param headers  请求头
     * @return 响应结果
     * @throws IOException 请求异常
     */
    @AutoLog("通讯-发送带请求头的form-data请求")
    public static String doPostFormDataWithHeaders(String url, Map<String, Object> formData, Map<String, String> headers) throws IOException {
        try {
            // 构建form-data请求体
            FormBody.Builder formBuilder = new FormBody.Builder();

            if (formData != null && !formData.isEmpty()) {
                for (Map.Entry<String, Object> entry : formData.entrySet()) {
                    String key = entry.getKey();
                    Object value = entry.getValue();

                    if (value != null) {
                        formBuilder.add(key, value.toString());
                    }
                }
            }

            RequestBody requestBody = formBuilder.build();

            // 构建请求
            Request.Builder requestBuilder = new Request.Builder()
                    .url(url)
                    .post(requestBody);

            // 添加请求头
            if (headers != null && !headers.isEmpty()) {
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    requestBuilder.addHeader(entry.getKey(), entry.getValue());
                }
            }

            Request request = requestBuilder.build();

            log.info("发送带请求头的form-data请求: url={}, formData={}, headers={}", url, formData, headers);

            // 发送请求
            Call call = client.newCall(request);
            Response response = null;

            try {
                response = call.execute();

                if (response != null && response.isSuccessful()) {
                    if (response.body() != null) {
                        String responseString = response.body().string();
                        log.info("带请求头的form-data请求响应: url={}, response={}", url, responseString);
                        return responseString;
                    }
                } else {
                    log.warn("带请求头的form-data请求失败: url={}, code={}, message={}",
                            url, response != null ? response.code() : "null",
                            response != null ? response.message() : "null");
                }

            } catch (IOException ioException) {
                log.error("带请求头的form-data请求异常: url={}, error={}", url, ioException.getMessage(), ioException);
                throw ioException;
            } finally {
                if (response != null) {
                    response.close();
                }
            }

        } catch (Exception e) {
            log.error("构建带请求头的form-data请求异常: url={}, error={}", url, e.getMessage(), e);
            throw new IOException("构建带请求头的form-data请求失败: " + e.getMessage(), e);
        }

        return null;
    }
}
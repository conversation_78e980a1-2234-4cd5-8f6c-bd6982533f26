package com.tgvs.wms.business.modules.machineMaterials.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tgvs.wms.business.modules.machineMaterials.entity.WmsMachineOutbound;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 机物料出库Mapper接口
 */
@Mapper
public interface WmsMachineOutboundMapper extends BaseMapper<WmsMachineOutbound> {
    
    /**
     * 自定义条件查询机物料出库列表
     * 
     * @param wmsMachineOutbound 查询条件
     * @return 机物料出库列表
     */
    List<WmsMachineOutbound> selectWmsMachineOutboundCustom(WmsMachineOutbound wmsMachineOutbound);
    
    /**
     * 批量逻辑删除机物料出库信息
     * 
     * @param ids 主键ID数组
     * @return 影响行数
     */
    int deleteWmsMachineOutboundByIds(String[] ids);
    
    /**
     * 根据出库单号查询机物料出库信息
     * 
     * @param outStoreNumber 出库单号
     * @return 机物料出库信息
     */
    WmsMachineOutbound selectWmsMachineOutboundByNumber(String outStoreNumber);
} 
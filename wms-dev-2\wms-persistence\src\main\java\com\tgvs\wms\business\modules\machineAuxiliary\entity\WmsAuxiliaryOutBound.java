package com.tgvs.wms.business.modules.machineAuxiliary.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.tgvs.wms.base.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 辅料出库实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wms_auxiliary_outbound")
public class WmsAuxiliaryOutBound extends BaseEntity {

    private static final long serialVersionUID = 1L;



    /**
     * 单据编号(出库单号)
     */
    @ApiModelProperty("单据编号(出库单号)")
    private String outStoreNumber;

    /**
     * 出库类型(0领料通知 1采购退货)
     */
    @ApiModelProperty("出库类型(0领料通知 1采购退货)")
    private Integer outType;



    /**
     * 状态(0:待处理,1:处理中,2:已完成,3:已取消)
     */
    @ApiModelProperty("状态(0:待处理,1:处理中,2:已完成,3:已取消)")
    private Integer status;

    /**
     * 删除标志 0-正常 1-删除
     */
    @ApiModelProperty("删除标志 0-正常 1-删除")
    private Integer deleteFlag;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remarks;
}
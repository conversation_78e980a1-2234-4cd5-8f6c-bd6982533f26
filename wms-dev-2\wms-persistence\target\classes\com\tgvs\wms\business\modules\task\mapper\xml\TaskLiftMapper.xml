<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tgvs.wms.business.modules.task.mapper.TaskLiftMapper">

    <!-- SQL片段：getUserByOrgCode 的 FROM 和 WHERE 部分 -->
    <sql id="getToSiteStatusFromSql">
        FROM
        wms_task_lift
        LEFT JOIN wms_point ON wms_task_lift.to_site = wms_point.point_no
        WHERE
        wms_point.point_type = 11

    </sql>

    <!-- 根据 orgCode 查询用户，包括子部门下的用户 -->
    <select id="getUserByOrgCode" resultType="com.tgvs.wms.business.modules.task.entity.TaskLift">
        SELECT
        wms_task_lift.* ,
        wms_point.occupy
        <include refid="getToSiteStatusFromSql"/>
        ORDER BY
        wms_task_lift.state DESC,wms_task_lift.dispatcher ASC
    </select>
</mapper>